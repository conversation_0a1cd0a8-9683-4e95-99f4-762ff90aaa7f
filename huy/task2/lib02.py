import warnings

import numpy as np
import pandas as pd
from hyperopt import fmin, tpe, Trials, STATUS_OK
from sklearn.metrics import roc_auc_score
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
from hyperopt import hp
from hyperopt import <PERSON>ATUS_OK
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
import time
from sklearn.utils import compute_sample_weight


warnings.filterwarnings("ignore")

# ===== CONFIGURATION =====
# Khi test tạo target code nhớ là thay đổi cả
DATA_PATH = "dl_train_month (1).csv"
TARGET_COL = "label_binary_3M"
TAG = TARGET_COL.split('_')[-1]

THRESHOLD = 10
TIME_COL = "month"
OUTPUT_NAME = "hyperopt_results"
MAX_EVALS = 1000

# ===== FEATURES TO REMOVE (LEAKAGE) =====
LEAK_FEATURES = [
    'label_binary_3M_center_v1',
    'label_binary_3M_center_v2',
    'label_binary_1M_center_v1',
    'label_binary_2W_center_v1',
    'label_binary_2W',
    'label_binary_2W_center_v1',
    'label_binary_2W_center_v2',
    'label_binary_1M',
    'label_binary_1M_center_v1',
    'label_binary_1M_center_v2',
    'label_binary_3M',
    'label_binary_3M_center_v1',
    'label_binary_3M_center_v2'
    'label_binary_2W', 'label_binary_1M', 'label_binary_3M',
    'label_binary_2W_center_v1', 'label_binary_2W_center_v2',
    'label_binary_1M_center_v1', 'label_binary_1M_center_v2',
    'label_binary_3M_center_v1', 'label_binary_3M_center_v2',
    'label_binary_v1_2W', 'label_binary_v1_1M', 'label_binary_v1_3M',
    'label_strength3_2W', 'label_strength3_1M', 'label_strength3_3M',
]

# Split by time
TRAIN_CUTOFF = "2023-01-01"
VAL_MONTH = "2023-06-01"


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    def create_qcut_bin(df, col='y_prob', q=5, label_template=None):
        if label_template is None:
            label_template = ['0-20', '20-40', '40-60', '60-80', '80-100']
        try:
            return pd.qcut(df[col], q=q, labels=label_template, duplicates='raise')
        except ValueError:
            # Tính số bin khả dụng
            unique_vals = df[col].quantile(np.linspace(0, 1, q + 1)).unique()
            real_q = len(unique_vals) - 1
            labels = label_template[:real_q]
            return pd.qcut(df[col], q=real_q, labels=labels, duplicates='drop')

    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = create_qcut_bin(df_lift)

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def filter_leak_features(cname_feature):
    return [f for f in cname_feature if f not in LEAK_FEATURES]


def binary_profit_threshold(row, col='label_binary_3M', threshold=5):
    """
    Trả về 1 nếu lợi nhuận tương lai ở cột `col` >= threshold, ngược lại là 0.
    """
    v = row.get(col)
    if pd.isna(v):
        return np.nan
    return int(v >= threshold)


def ternary_profit_strength(row, col='profit_3M', strong_th=10, weak_th=0):
    """
    Phân loại lợi nhuận thành 3 lớp:
    - 2: lợi nhuận >= strong_th
    - 1: lợi nhuận >= weak_th
    - 0: còn lại
    """
    v = row.get(col)
    if pd.isna(v):
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2023-01-01', val_cutoff='2023-06-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    # tickers = sorted(df['ticker'].unique())
    # n_test = max(1, int(len(tickers) * test_size / 100))
    # test_tickers = tickers[:n_test]

    list_ticker = list(df['ticker'].unique())
    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'

    print(df[cname_tvt].value_counts())
    return df


# ===== Additional Utilities =====
def print_top_features(model, top_n=20):
    importance = model.get_booster().get_score(importance_type='gain')
    imp_df = pd.DataFrame(importance.items(), columns=["feature", "gain"])
    imp_df = imp_df.sort_values(by="gain", ascending=False).reset_index(drop=True)
    print(f"\n📌 Top {top_n} feature quan trọng:")
    print(imp_df.head(top_n))


def print_target_distribution(pdx, target_col='label_binary_3M'):
    for tvt in ['train', 'val', 'test']:
        subset = pdx[pdx['tvt'] == tvt]
        pct = subset[target_col].mean() * 100
        print(f"{tvt.upper():<5}: {pct:.2f}% target = 1, ({subset.shape[0]} samples)")


def print_top_features_to_csv(model, filename="top_features.csv", top_n=20):
    importance_dict = model.get_booster().get_score(importance_type='gain')
    imp_df = pd.DataFrame(importance_dict.items(), columns=['feature', 'importance'])
    imp_df = imp_df.sort_values(by='importance', ascending=False).reset_index(drop=True)

    if top_n:
        imp_df = imp_df.head(top_n)

    imp_df.to_csv(filename, index=False)
    print(f"📄 Top {top_n} features saved to {filename}")


def print_target_distribution_to_csv(pdx, target_col='label_binary_3M', filename='target_distribution.csv'):
    result = []

    for tvt in ['train', 'val', 'test']:
        subset = pdx[pdx['tvt'] == tvt]
        if len(subset) == 0:
            continue
        pct_target = (subset[target_col] == 1).mean() * 100
        result.append({
            'Split': tvt,
            '%Target = 1': round(pct_target, 2),
            'Count': len(subset)
        })

    df_result = pd.DataFrame(result)
    df_result.to_csv(filename, index=False)
    print(f"📄 Target distribution saved to {filename}")


def label_binary(row, label, strong_th=5):
    v = row.get(f'profit_{label}')  # lấy giá trị từ cột profit_{label}
    if pd.isna(v):
        return np.nan
    return int(v >= strong_th)


# ===== LOAD DATA =====

def load_and_split_v1():
    df = pd.read_csv(DATA_PATH, low_memory=False)
    df['week'] = pd.to_datetime(df['time']).dt.strftime('%Y-%W')
    df['month'] = df['time'].map(lambda x: x[:7])

    profit_cols = [col for col in df.columns if col.startswith('profit_')]
    cname_tvt = 'tvt'
    labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols

    for label in ['3M']:
        df[f'label_binary_{label}'] = df.apply(
            lambda row: label_binary(row, label, strong_th=THRESHOLD), axis=1
        )
        labels_tag.append(f'label_binary_{label}')


    df[TIME_COL] = pd.to_datetime(df[TIME_COL])

    # ✅ Split the dataset
    df = split_tvt(
        df,
        time_col=TIME_COL,
        test_size=10,
        train_cutoff=TRAIN_CUTOFF,
        val_cutoff=VAL_MONTH,
    )

    # ✅ Check for ticker leakage
    tickers_train = set(df[df['tvt'] == 'train']['ticker'])
    tickers_test = set(df[df['tvt'] == 'test']['ticker'])
    overlap = tickers_train & tickers_test
    print(f"🚨 Ticker overlap between train and test: {len(overlap)}")
    if overlap:
        print("⚠️ Overlapping tickers:", list(overlap)[:10])

    # ✅ Print time ranges
    print("🕒 Time ranges per split:")
    print("  Train:", df[df['tvt'] == 'train'][TIME_COL].min(), "→", df[df['tvt'] == 'train'][TIME_COL].max())
    print("  Val  :", df[df['tvt'] == 'val'][TIME_COL].min(), "→", df[df['tvt'] == 'val'][TIME_COL].max())
    print("  Test_1 :", df[df['tvt'] == 'test'][TIME_COL].min(), "→", df[df['tvt'] == 'test_1'][TIME_COL].max())
    print("  Test_2 :", df[df['tvt'] == 'test'][TIME_COL].min(), "→", df[df['tvt'] == 'test_2'][TIME_COL].max())

    # Split into X, y
    train_df = df[df['tvt'] == 'train']
    val_df = df[df['tvt'] == 'val']
    test_1_df = df[df['tvt'] == 'test_1']
    test_2_df = df[df['tvt'] == 'test_2']

    X_train = train_df.drop(columns=labels_tag)
    y_train = train_df[TARGET_COL].astype(int)
    X_val = val_df.drop(columns=labels_tag)
    y_val = val_df[TARGET_COL].astype(int)

    X_test_1 = test_1_df.drop(columns=labels_tag)
    X_test_2 = test_2_df.drop(columns=labels_tag)
    y_test_1 = test_df[TARGET_COL].astype(int)
    y_test_2 = test_df[TARGET_COL].astype(int)

    cname_feature = filter_leak_features(list(X_train.columns))
    X_train= X_train[cname_feature]
    X_val = X_val[cname_feature]
    X_test_1 = X_test_1[cname_feature]
    X_test_2 = X_test_2[cname_feature]

    print(f"Train: {train_df.shape}, Val: {val_df.shape}, Test: {test_df.shape}")
    print(f"Train label count:\n{y_train.value_counts()}")
    print(f"Val label count:\n{y_val.value_counts()}")
    print(f"Test 1 label count:\n{y_test_1.value_counts()}")
    print(f"Test 2 label count:\n{y_test_2.value_counts()}")
    print(f"%Target = 1: train:{y_train.mean():.2f}, val:{y_val.mean():.2f}, test_1:{y_test.mean():.2f}, test_2:{y_test.mean():.2f}")

    print(f"✅ Số feature sau khi lọc: {len(cname_feature)}")


    # OPTIONAL: standardization + dtype cleanup
    scaler = StandardScaler()
    scaler.fit(X_train)

    X_train = pd.DataFrame(scaler.transform(X_train), columns=X_train.columns)
    X_val = pd.DataFrame(scaler.transform(X_val), columns=X_val.columns)
    X_test_1 = pd.DataFrame(scaler.transform(X_test_1), columns=X_test_1.columns)
    X_test_2 = pd.DataFrame(scaler.transform(X_test_2), columns=X_test_2.columns)

    for dframe in [X_train, X_val, X_test_1, X_test_2]:
        for col in dframe.select_dtypes(include='float64').columns:
            dframe[col] = dframe[col].astype('float32')
        for col in dframe.select_dtypes(include='int64').columns:
            dframe[col] = dframe[col].astype('int32')


    return X_train, y_train, X_val, y_val, X_test_1, y_test_1, X_test_2, y_test_2, cname_feature


def get_top_gain_importance(model, top_n=20):
    """
    Extract top features by 'gain' importance from a trained XGBoost model.

    Args:
        model: A trained XGBoost model (e.g., XGBClassifier or Booster)
        top_n: (Optional) number of top features to return. If None, return all.

    Returns:
        DataFrame with columns ['feature', 'importance'], sorted descending by gain.
    """
    # Get importance by 'gain'
    importance_dict = model.get_booster().get_score(importance_type='gain')

    # Convert to DataFrame
    imp_df = pd.DataFrame(
        importance_dict.items(),
        columns=['feature', 'importance']
    )

    # Sort by gain importance
    imp_df = imp_df.sort_values(by='importance', ascending=False).reset_index(drop=True)

    if top_n is not None:
        imp_df = imp_df.head(top_n)

    return imp_df


