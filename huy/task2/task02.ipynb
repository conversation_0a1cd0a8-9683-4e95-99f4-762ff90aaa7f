#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
#%%
best_results = {}
best_auc_val = 0
best_model = {}

X_train, y_train, X_val, y_val, X_test_1, y_test_1, X_test_2, y_test_2, cname_feature = load_and_split_v1()

trials = Trials()
start_time = time.time()
milestones = [1, 10, 20, 50, 100, 200, 500, 1000]
MAX_EVALS = 101


def objective(params):
    global best_auc_val
    global best_model
    global best_results
    try:
        start_time = time.time()

        # Ensure correct types for integer hyperparameters
        params['max_depth'] = int(params['max_depth'])
        params['min_child_weight'] = int(params.get('min_child_weight', 1))
        params['max_bin'] = int(params.get('max_bin', 1))

        model = XGBClassifier(
            **params,
            use_label_encoder=False,
            eval_metric='auc',
            device='cuda',
            objective='binary:logistic',
            tree_method='gpu_hist',
            random_state=42,
            n_jobs=-1,
            n_estimators=500,
            early_stopping_rounds=70,
        )

        sample_weights = compute_sample_weight(class_weight='balanced', y=y_train)

        model.fit(X_train, y_train, sample_weight=sample_weights, eval_set=[(X_val, y_val)], verbose=False)

        val_preds = model.predict_proba(X_val)[:, 1]
        test_preds_1 = model.predict_proba(X_test_1)[:, 1]
        test_preds_2 = model.predict_proba(X_test_2)[:, 1]

        auc_val = roc_auc_score(y_val, val_preds)
        auc_test_1 = roc_auc_score(y_test_1, test_preds_1)
        auc_test_2 = roc_auc_score(y_test_2, test_preds_2)

        iter_num = len(trials.trials)

        if auc_val > best_model.get(auc_val, 0):
            best_model = {
                'params': params,
                'auc_val': auc_val,
                'auc_test_1': auc_test_1,
                'auc_test_2': auc_test_2,
                'time': round(time.time() - start_time, 2),
                'n_train': len(X_train),
                'n_val': len(X_val),
                'n_test': len(X_test),
                'pct_test': round(len(X_test) / (len(X_train) + len(X_val) + len(X_test)) * 100, 2)
            }

        if iter_num in milestones:
            best_results[iter_num] = best_model

            # ✅ Print output in VSCode terminal immediately
            print("\n" + "=" * 60, flush=True)
            print(f"📊 Iteration: {iter_num}", flush=True)
            print(f"🔹 Best AUC Val : {best_model.get('auc_val', 0):.4f}", flush=True)
            print(f"🔹 Best AUC Test 1: {best_model.get('auc_test_1', 0):.4f}", flush=True)
            print(f"🔹 Best AUC Test 2: {best_model.get('auc_test_2', 0):.4f}", flush=True)
            print(f"🔹 Best Params  : {best_model.get('params', 0)}", flush=True)
            print(f"🔹 pct_test : {best_model.get('pct_test', 0)}", flush=True)
            print("=" * 60, flush=True)

        return {'loss': -auc_val, 'status': STATUS_OK}

    except Exception as e:
        return {
            'loss': 9999,
            'status': STATUS_OK,
        }


space = {
    'max_depth': hp.quniform('max_depth', 3, 7, 1),
    'learning_rate': hp.quniform('learning_rate', 0.01, 0.1, 0.02),
    'subsample': hp.quniform('subsample', 0.60, 1.00, 0.1),
    'colsample_bytree': hp.quniform('colsample_bytree', 0.1, 0.9, 0.1),
    'min_child_weight': hp.quniform('min_child_weight', 1, 4, 1),
    'gamma': hp.quniform('gamma', 0, 0.1, 0.02),
    'reg_alpha': hp.quniform('reg_alpha', 0, 1, 0.2),
    'reg_lambda': hp.quniform('reg_lambda', 1, 5, 1),
    'max_bin': hp.quniform('max_bin', 128, 512, 128),
}

best = fmin(
    fn=objective,
    space=space,
    algo=tpe.suggest,
    max_evals=MAX_EVALS,
    trials=trials,
    verbose=True
)

# ===== REPORT =====
report = []
for milestone in milestones:
    rec = best_results.get(milestone)
    if rec:
        report.append([
            milestone,
            rec['params'],
            rec['auc_val'],
            rec['auc_test'],
            rec['best_iteration'],
            rec['time']
        ])

report_df = pd.DataFrame(report, columns=[
    'Iteration', 'Best Hyperparameters', 'Val AUC', 'Test AUC', 'Best Iteration', 'Time (s)'
])

timestamp = time.strftime('%Y%m%d_%H%M%S')
report_file = f"{OUTPUT_NAME}_{timestamp}.csv"
report_df.to_csv(report_file, index=False)
print("\n===== FINAL REPORT =====")
print(report_df)
print(f"Saved to: {report_file}")

# ===== FINAL MODEL =====
if best_results:
    last_milestone = max(best_results.keys())
    best_params = best_results[last_milestone]['params']
    final_model = XGBClassifier(
        **best_params,
        use_label_encoder=False,
        eval_metric='auc',
        device='cuda',
        objective='binary:logistic',
        tree_method='gpu_hist',
        random_state=42,
        n_jobs=-1,
        n_estimators=1000,
    )

    X_full_train = pd.concat([X_train, X_val])
    y_full_train = pd.concat([y_train, y_val])

    final_model.fit(X_full_train, y_full_train)


    test_preds_1 = final_model.predict_proba(X_test_1)[:, 1]
    test_preds_2 = final_model.predict_proba(X_test_2)[:, 1]

    auc_test_1 = roc_auc_score(y_test_1, test_preds_1)
    auc_test_2 = roc_auc_score(y_test_2, test_preds_2)



    print(f"\n✅ Final Model Test_1 AUC: {auc_test_1:.4f}")
    print(f"\n✅ Final Model Test_2 AUC: {auc_test_2:.4f}")

    print("\n⭐ Top 20 Features by Gain Importance:")
    imp_df = get_top_gain_importance(final_model, top_n=20)
    imp_df.to_csv("top_features_from_final_model.csv", index=False)
    print("📄 Saved to top_features_from_final_model.csv")
    print(imp_df)

    print("\n===== LIFT TABLE =====")
    print("Test 1")
    report_lift_table(X_test_1, y_test_1, test_preds_1, profit_col=f'profit_{TAG}')
    print("Test 2")
    report_lift_table(y_test_2, y_test_2, test_preds_2, profit_col=f'profit_{TAG}')


    # model_file = f"final_model_{timestamp}.pkl"
    # joblib.dump(final_model, model_file)
    # print(f"Model saved to: {model_file}")
else:
    print("⚠️ No best results found!")


#%%
def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    pdxy[cname_tvt] = 'other'
    tTest = pdxy['ticker'].isin(list_ticker_test)
    tTrain = ~tTest

    # Sort by time
    pdxy = pdxy.sort_values(by='time')

    iA = pdxy['time'] < YMD_test

    iB = pdxy['time'] > (pd.to_datetime(YMD_test) + timedelta(days=100)).strftime('%Y-%m-%d')

    pdxy.loc[iA & tTrain, cname_tvt] = 'train'
    pdxy.loc[iB & tTrain, cname_tvt] = 'val'
    pdxy.loc[tTest, cname_tvt] = 'test'
    return pdxy


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0