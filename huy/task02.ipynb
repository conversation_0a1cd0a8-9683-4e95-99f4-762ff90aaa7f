#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
#%%
X_train, y_train, X_val, y_val, X_test, y_test, cname_feature
#%%
pdXY,cname_feature = create_pdxy(X_train, y_train, X_val, y_val, X_test, y_test, target_name='target')
#%%
cname_feature[:100]
#%%

#%%

#%%

#%%
params={}
model = XGBClassifier(
    **params,
    tree_method='gpu_hist',
    use_label_encoder=False,
    eval_metric='auc',
    random_state=42,
    n_jobs=-1
)

#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())


X_train, y_train, X_val, y_val, X_test, y_test, cname_feature = load_and_split_v1()



# ===== SEARCH SPACE =====
space = {
    'max_depth': hp.choice('max_depth', range(3, 10)),
    'learning_rate': hp.quniform('learning_rate', 0.01, 0.3, 0.01),
    'subsample': hp.quniform('subsample', 0.60, 1.00, 0.05),
    'colsample_bytree': hp.quniform('colsample_bytree', 0.10, 0.90, 0.05),
    'min_child_weight': hp.quniform('min_child_weight', 1, 10, 1),
    'gamma': hp.quniform('gamma', 0, 5, 0.1),
    'reg_alpha': hp.quniform('reg_alpha', 0, 10, 0.5),
    'reg_lambda': hp.quniform('reg_lambda', 0, 10, 0.5),
    'n_estimators': hp.choice('n_estimators', range(200, 1501, 100))
}



# ===== OBJECTIVE =====
# ===== OBJECTIVE =====
from hyperopt import STATUS_OK
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
import time

trials = Trials()
best_results = {}
start_time = time.time()
milestones = [1, 10, 20, 50, 100, 200, 500, 1000]

def objective(params):
    start_time = time.time()
    
    # Ensure correct types for integer hyperparameters
    params['max_depth'] = int(params['max_depth'])
    params['min_child_weight'] = int(params.get('min_child_weight', 1))
    params['n_estimators'] = int(params.get('n_estimators', 100))










    model = XGBClassifier(
        **params,
        use_label_encoder=False,
        eval_metric='auc',
        tree_method='gpu_hist',
        random_state=42,
        n_jobs=-1
    )

    model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)
    
    val_preds = model.predict_proba(X_val)[:, 1]
    test_preds = model.predict_proba(X_test)[:, 1]
    
    auc_val = roc_auc_score(y_val, val_preds)
    auc_test = roc_auc_score(y_test, test_preds)
    
    iter_num = len(trials.trials)

    if iter_num in milestones:
        best_results[iter_num] = {
    'params': params,
    'auc_val': auc_val,
    'auc_test': auc_test,
    'best_iteration': params['n_estimators'],
    'time': round(time.time() - start_time, 2),
    'n_train': len(X_train),
    'n_val': len(X_val),
    'n_test': len(X_test),
    'pct_test': round(len(X_test) / (len(X_train) + len(X_val) + len(X_test)) * 100, 2)
}

        # ✅ Print output in VSCode terminal immediately
        print("\n" + "=" * 60, flush=True)
        print(f"📊 Iteration: {iter_num}", flush=True)
        print(f"🔹 Best AUC Val : {auc_val:.4f}", flush=True)
        print(f"🔹 Best AUC Test: {auc_test:.4f}", flush=True)
        print(f"🔹 Best NTree   : {params['n_estimators']}", flush=True)
        print(f"🔹 Best Params  : {params}", flush=True)
        print("=" * 60, flush=True)

    return {'loss': -auc_val, 'status': STATUS_OK}



from hyperopt import hp

space = {
    'max_depth': hp.choice('max_depth', range(3, 10)),
    'learning_rate': hp.quniform('learning_rate', 0.01, 0.3, 0.01),
    'subsample': hp.quniform('subsample', 0.60, 1.00, 0.05),
    'colsample_bytree': hp.quniform('colsample_bytree', 0.10, 0.90, 0.05),
    'min_child_weight': hp.quniform('min_child_weight', 1, 10, 1),
    'gamma': hp.quniform('gamma', 0, 5, 0.1),
    'reg_alpha': hp.quniform('reg_alpha', 0, 10, 0.5),
    'reg_lambda': hp.quniform('reg_lambda', 0, 10, 0.5),
    'n_estimators': hp.choice('n_estimators', range(200, 1501, 100))
}






best = fmin(
    fn=objective,
    space=space,
    algo=tpe.suggest,
    max_evals=MAX_EVALS,
    trials=trials,
    verbose=True
)

# report_df = run_hyperopt_loop(space, max_evals=1000, eval_step=10)

# ===== REPORT =====
report = []
for milestone in milestones:
    rec = best_results.get(milestone)
    if rec:
        report.append([
            milestone,
            rec['params'],
            rec['auc_val'],
            rec['auc_test'],
            rec['best_iteration'],
            rec['time']
        ])

report_df = pd.DataFrame(report, columns=[
    'Iteration', 'Best Hyperparameters', 'Val AUC', 'Test AUC', 'Best Iteration', 'Time (s)'
])

timestamp = time.strftime('%Y%m%d_%H%M%S')
report_file = f"{OUTPUT_NAME}_{timestamp}.csv"
report_df.to_csv(report_file, index=False)
print("\n===== FINAL REPORT =====")
print(report_df)
print(f"Saved to: {report_file}")

# ===== FINAL MODEL =====
if best_results:
    last_milestone = max(best_results.keys())
    best_params = best_results[last_milestone]['params']
    final_model = XGBClassifier(
        **best_params,
        tree_method='gpu_hist',
        use_label_encoder=False,
        eval_metric='auc',
        random_state=42,
        n_jobs=-1
    )

    X_full_train = pd.concat([X_train, X_val])
    y_full_train = pd.concat([y_train, y_val])

    final_model.fit(X_full_train, y_full_train)
    test_preds = final_model.predict_proba(X_test)[:, 1]
    final_auc = roc_auc_score(y_test, test_preds)

    print(f"\n✅ Final Model Test AUC: {final_auc:.4f}")
    model_file = f"final_model_{timestamp}.pkl"
    joblib.dump(final_model, model_file)
    print(f"Model saved to: {model_file}")
else:
    print("⚠️ No best results found!")

#%%
import xgboost as xgb
print("XGBoost version:", xgb.__version__)
print("GPU support:", xgb.get_config())
#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
model = XGBClassifier(**best_params)
model.fit(X_train, y_train)
#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
get_top_gain_importance(final_model, top_n=20)
#%%
print(final_model.get_booster().feature_names)

#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
def get_top_gain_importance(model, top_n=20):
    """
    Extract top features by 'gain' importance from a trained XGBoost model.

    Args:
        model: A trained XGBClassifier model.
        top_n: (Optional) Number of top features to return.

    Returns:
        A pandas DataFrame of features sorted by gain importance.
    """
    importance_dict = model.get_booster().get_score(importance_type='gain')
    imp_df = pd.DataFrame(importance_dict.items(), columns=['feature', 'importance'])
    imp_df = imp_df.sort_values(by='importance', ascending=False).reset_index(drop=True)

    if top_n is not None:
        imp_df = imp_df.head(top_n)

    return imp_df
#%%
cname_feature = filter_leak_features(cname_feature)
#%%
# --- STEP 1: Load library ---
exec(open('lib02.py', 'r', encoding='utf-8').read())

# --- STEP 2: Load final model ---
import joblib

# --- STEP 1: Load model ---
model_path = "final_model_20250710_160100.pkl"
model = joblib.load(model_path)
print(f"✅ Loaded model from: {model_path}")

# --- STEP 2: Load data ---
X_train, y_train, X_val, y_val, X_test, y_test, cname_feature = load_and_split_v1()
pdx, _ = create_pdxy(X_train, y_train, X_val, y_val, X_test, y_test, target_name='label_binary_3M')

# --- STEP 3: Print top features ---
print("\n⭐ Top 20 Features by Gain Importance:")
imp_df = get_top_gain_importance(model, top_n=20)
print(imp_df)
imp_df.to_csv("top_features_from_final_model.csv", index=False)
print("📄 Saved to top_features_from_final_model.csv")

# --- STEP 4: Print % target per split ---
print("\n📊 %Target = 1 per Split:")
result = []
for split in ['train', 'val', 'test']:
    subset = pdx[pdx['tvt'] == split]
    pct_target = (subset['label_binary_3M'] == 1).mean() * 100
    count = len(subset)
    print(f"{split.capitalize():<5}: {pct_target:5.2f}% (n = {count})")
    result.append({'Split': split, '%Target = 1': round(pct_target, 2), 'Count': count})

# Optional: Save to CSV
df_result = pd.DataFrame(result)
df_result.to_csv("target_distribution_from_final_model.csv", index=False)
print("📄 Saved to target_distribution_from_final_model.csv")

#%%
print(final_model.get_booster().feature_names)