################################################
## Classify
label_list_mc = ['score_1M', 'score_1M_center', 'score_1M_v2', 'score_1M_center_v2']

for lb in label_list_mc:
    print('Phân bố tổng thể', pd.Series(df_data[lb]).value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    # hiển thị phân bố train
    print("Train dist:", pd.Series(y_train).value_counts().sort_index())
    sns.countplot(x=np.asarray(y_train, dtype=int))
    plt.title(f"Train {lb} distribution")
    plt.show()

    # ======= sample_weight (đa lớp vẫn ok) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)

    # ======= param grid =======
    param_dist = {
        'learning_rate': [0.01, 0.03, 0.05, 0.1],
        'max_depth': [3, 5, 7, 9],
        'min_child_weight': [3, 5, 7],
        'subsample': [0.7, 0.8, 0.9, 1.0],
        'colsample_bytree': [0.7, 0.8, 0.9, 1.0],
        'gamma': [0, 0.05, 0.1],
        'reg_lambda': [3, 6, 9],
        'reg_alpha': [0.5, 1, 5],
        'n_estimators': [300, 500, 800],
        'max_bin': [128, 256, 512]
    }

    num_class = int(pd.Series(y_train).nunique())

    xgb_base = xgb.XGBClassifier(
        tree_method='hist',
        objective='multi:softprob',
        num_class=num_class,
        n_jobs=25,
        eval_metric=['mlogloss','merror'],
        random_state=42
    )

    # ======= RandomizedSearchCV =======
    cv = StratifiedKFold(n_splits=4, shuffle=True, random_state=42)
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=30,
        scoring='roc_auc_ovr',   # có thể đổi 'roc_auc_ovr_weighted' nếu lệch lớp nặng
        cv=cv,
        verbose=2,
        random_state=42,
        n_jobs=25,
        return_train_score=False
    )
    search.fit(X_train_final, y_train, sample_weight=sample_weight, verbose=False)

    print("\nBest params:", search.best_params_)
    print("Best CV (roc_auc_ovr):", search.best_score_)

    # ======= Train lại với best params + early stopping =======
    best_params = dict(search.best_params_)
    final_model = xgb.XGBClassifier(
        **best_params,
        tree_method='hist',
        objective='multi:softprob',
        num_class=num_class,
        n_jobs=-1,
        eval_metric=['mlogloss','merror'],
        random_state=42
    )
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        early_stopping_rounds=40,
        verbose=False
    )

    # ======= Đánh giá TRAIN (check overfit nhẹ) =======
    proba_tr = final_model.predict_proba(X_train_final)
    classes = final_model.classes_
    y_pred_tr = classes[np.argmax(proba_tr, axis=1)]

    acc_tr = accuracy_score(y_train, y_pred_tr)
    f1m_tr = f1_score(y_train, y_pred_tr, average='macro')
    try:
        auc_tr = roc_auc_score(y_train, proba_tr, multi_class='ovr', average='macro', labels=classes)
    except ValueError:
        auc_tr = np.nan

    print(f"[TRAIN] ACC={acc_tr*100:.2f}% | F1_macro={f1m_tr*100:.2f}% | AUC_ovr_macro={'NA' if np.isnan(auc_tr) else f'{auc_tr*100:.2f}%'}")
    print(classification_report(y_train, y_pred_tr, digits=4))
    cm_tr = confusion_matrix(y_train, y_pred_tr, labels=classes, normalize='true')
    print("Confusion Matrix (normalized) - TRAIN:\n", cm_tr)

    # ======= Đánh giá TEST =======
    for X_test_final, y_test, data_test, tag in [(X_test1_final, y_test1, data_test1, "TEST1"), (X_test2_final, y_test2, data_test2, "TEST2")]:

        proba_te = final_model.predict_proba(X_test_final)
        y_pred_te = classes[np.argmax(proba_te, axis=1)]

        acc = accuracy_score(y_test, y_pred_te)
        f1m = f1_score(y_test, y_pred_te, average='macro')
        try:
            auc = roc_auc_score(y_test, proba_te, multi_class='ovr', average='macro', labels=classes)
        except ValueError:
            auc = np.nan

        print(f"[{tag}] ACC={acc*100:.2f}% | F1_macro={f1m*100:.2f}% | AUC_ovr_macro={'NA' if np.isnan(auc) else f'{auc*100:.2f}%'}")
        print(classification_report(y_test, y_pred_te, digits=4))
        cm = confusion_matrix(y_test, y_pred_te, labels=classes, normalize='true')
        print(f"Confusion Matrix (normalized) - {tag}:\n", cm)

        # ======= Báo cáo ngắn gọn =======
        base = len(y_test)
        counts = pd.Series(y_test).value_counts().reindex(classes, fill_value=0)
        percent_each = (counts / base * 100).round(1).astype(str) + '%'
        report = [{
            "Label": lb,
            "Classes": len(classes),
            "Base": base,
            "ACC": f"{acc*100:.1f}%",
            "F1_macro": f"{f1m*100:.1f}%",
            "AUC_ovr_macro": ("NA" if np.isnan(auc) else f"{auc*100:.1f}%"),
            "Distrib_test": {int(k) if isinstance(k,(np.integer,int)) else k: v for k, v in percent_each.to_dict().items()}
        }]
        print(pd.DataFrame(report).to_markdown(index=False))

        print("======================================================")