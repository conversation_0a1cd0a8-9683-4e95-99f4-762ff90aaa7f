#%%
exec(open('lib02.py', 'r', encoding='utf-8').read())
#%%
load_and_split_v1()
#%%
exec(open("label_strategy_scorer.py", encoding='utf-8').read())

#%%
def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    pdxy[cname_tvt] = 'other'
    tTest = pdxy['ticker'].isin(list_ticker_test)
    tTrain = ~tTest

    # Sort by time
    pdxy = pdxy.sort_values(by='time')

    iA = pdxy['time'] < YMD_test

    iB = pdxy['time'] > (pd.to_datetime(YMD_test) + timedelta(days=100)).strftime('%Y-%m-%d')

    pdxy.loc[iA & tTrain, cname_tvt] = 'train'
    pdxy.loc[iB & tTrain, cname_tvt] = 'val'
    pdxy.loc[tTest, cname_tvt] = 'test'
    return pdxy


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0
    
    # ==== AUC theo ICB_CODE ====
df_raw = pd.read_csv(DATA_PATH, low_memory=False).reset_index(drop=True)
df_test_1_full = df_raw.iloc[X_test_1.index].copy()
df_test_1_full['pred'] = test_preds_1
df_test_1_full['target'] = y_test_1.values

auc_by_icb = []
for icb_code, g in df_test_1_full.groupby('ICB_Code'):
    n = len(g)
    pct_target = g['target'].mean()
    try:
        auc = roc_auc_score(g['target'], g['pred'])
    except:
        auc = np.nan
    auc_by_icb.append({
        'ICB_Code': icb_code,
        'N': n,
        '%Target=1': round(pct_target * 100, 2),
        'AUC': round(auc, 4)
    })

auc_df = pd.DataFrame(auc_by_icb).sort_values('AUC', ascending=False)
auc_df.to_csv('auc_by_icb.csv', index=False)
print("\n===== AUC BY ICB_CODE (TOP 10) =====")
print(auc_df.head(10).to_string(index=False))


# ==== AUC theo ticker ====
auc_by_ticker = []
for ticker, g in df_test_1_full.groupby('ticker'):
    n = len(g)
    pct_target = g['target'].mean()
    try:
        auc = roc_auc_score(g['target'], g['pred'])
    except:
        auc = np.nan
    auc_by_ticker.append({
        'Ticker': ticker,
        'N': n,
        '%Target=1': round(pct_target * 100, 2),
        'AUC': round(auc, 4)
    })

auc_ticker_df = pd.DataFrame(auc_by_ticker).sort_values('AUC', ascending=False)
auc_ticker_df.to_csv('auc_by_ticker.csv', index=False)
print("\n===== AUC BY TICKER (TOP 10) =====")
print(auc_ticker_df.head(10).to_string(index=False))