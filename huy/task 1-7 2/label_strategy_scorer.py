import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score
from lib02 import load_and_split_v1
from xgboost import XGBClassifier
from sklearn.utils.class_weight import compute_sample_weight
import time
import lib02

# Load master dataframe once
df_master = pd.read_csv("dl_train_month (1).csv")

label_defs = [
    ("3M", 10), ("3M", 7), ("3M", 5), ("3M", 3),
    ("1M", 10), ("1M", 5), ("1M", 3),
    ("2W_center_5", 3),
    ("2W_center_3", 1),
    ("1M_center_10", 5),
    ("1M_center_7", 3),
    ("3M_center_20", 10),
    ("3M_center_15", 7), 
    ("2W", 4), ("1M", 5), ("3M", 10), 
    ("1M_center_7", 3), ("1M_center_10", 5), 
    ("3M_center_15", 7), ("3M_center_20", 10), 
]

results = []

for label, threshold in label_defs:
    print(f"\n🚀 Testing label: profit_{label} ≥ {threshold}")

    df = df_master.copy()
    profit_col = f'profit_{label}'
    label_col = f'label_binary_{label}'

    if profit_col not in df.columns:
        print(f"⚠️ Skipping: Column `{profit_col}` not found.")
        results.append({'Label': f'{label} >= {threshold}', 'Error': f'Missing column: {profit_col}'})
        continue

    # Create binary label in-memory
    df[label_col] = df[profit_col].apply(lambda x: np.nan if pd.isna(x) else int(x >= threshold))

    # Debugging: Check label distribution
    print(f"\n=== {label_col} with threshold {threshold} ===")
    print(df[label_col].value_counts(dropna=False))
    positive_count = df[label_col].sum()
    total_count = df[label_col].count()
    pct_positive = 100 * positive_count / total_count
    print(f"🔍 {label_col}: {positive_count} / {total_count} positives ({pct_positive:.2f}%)\n")


    # Save temporarily for mylib02 to load
    df.to_csv("temp_label_data.csv", index=False)

# Update TARGET_COL and DATA_PATH for correct label
    lib02.TARGET_COL = label_col
    lib02.TAG = f"{label}_{threshold}"  # 🔥 Ensure unique tag for each label + threshold
    lib02.DATA_PATH = "temp_label_data.csv"


    # 🚫 Prevent leakage:
    lib02.LEAK_FEATURES.append(label_col)  # exclude label as input
    profit_cols = [col for col in df.columns if col.startswith('profit_')]
    lib02.LEAK_FEATURES.extend(profit_cols)  # exclude profits as features

    try:
        X_train, y_train, X_val, y_val, X_test_1, y_test_1, X_test_2, y_test_2, _, _, _ = load_and_split_v1(
            label_list=[label],
            threshold_dict={label: threshold}
        )

        baseline_prob_test_1 = np.full_like(y_test_1, fill_value=y_test_1.mean(), dtype=float)
        baseline_prob_test_2 = np.full_like(y_test_2, fill_value=y_test_2.mean(), dtype=float)
        baseline_auc_test_1 = roc_auc_score(y_test_1, baseline_prob_test_1)
        baseline_auc_test_2 = roc_auc_score(y_test_2, baseline_prob_test_2)

        model = XGBClassifier(
            n_estimators=300,
            max_depth=6,
            learning_rate=0.06,
            subsample=0.6000000000000001,
            colsample_bytree=0.4,
            max_bin = 128,
            gamma = 0.1,
            min_child_weight = 1,
            reg_alpha = 0.6000000000000001,
            reg_lambda = 3.0,
            use_label_encoder=False,
            eval_metric='auc',
            tree_method='gpu_hist',
            # device='cuda',
            random_state=42
        )

        sample_weights = compute_sample_weight('balanced', y_train)
        model.fit(
            X_train, y_train,
            sample_weight=sample_weights,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=50,
            verbose=False
        )

        preds_test_1 = model.predict_proba(X_test_1)[:, 1]
        preds_test_2 = model.predict_proba(X_test_2)[:, 1]

        auc_test_1 = roc_auc_score(y_test_1, preds_test_1)
        auc_test_2 = roc_auc_score(y_test_2, preds_test_2)

        results.append({
            'Label': f'{label} >= {threshold}',
            'Baseline AUC Test 1': round(baseline_auc_test_1, 4),
            'Baseline AUC Test 2': round(baseline_auc_test_2, 4),
            'AUC Test 1': round(auc_test_1, 4),
            'AUC Test 2': round(auc_test_2, 4),
            'Mean AUC': round((auc_test_1 + auc_test_2) / 2, 4),
            '%Target_1': round(y_test_1.mean() * 100, 2),
            '%Target_2': round(y_test_2.mean() * 100, 2),
            'N_test_1': len(y_test_1),
            'N_test_2': len(y_test_2)
        })

    except Exception as e:
        print(f"❌ Error for label {label} ≥ {threshold}: {e}")
        results.append({'Label': f'{label} >= {threshold}', 'Error': str(e)})

# Save results
df_result = pd.DataFrame(results)
df_result.to_csv(f"label_strategy_auc_results_{time.strftime('%Y%m%d_%H%M%S')}.csv", index=False)
print(df_result.head(10))
