# import argparse
import os
import re
import warnings
from datetime import datetime

from pathos.multiprocessing import ProcessingPool as Pool

warnings.simplefilter(action='ignore')
import pandas as pd
from itertools import combinations
from core_utils.base_eval import Simulation, PreProcess

FPATH = 'ticker_v1a/'


def find_sell(df_price: pd.DataFrame, list_buy_index: list, list_sell_index: list,
              list_sell_reason: list, cutloss):
    # df_predefine_sell: pd.DataFrame
    """
    Find sell for pair (buy_pattern - sell reason)
    Arguments:
    - df_price: data frame with columns ymd, Close, Open
    - list_buy_index: list of Buy indexes in df_price
    - list_sell_index: list of Sell indexes in df_price
    - list_sell_reason: list of sell reasons
    - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
    Output:
    - list_deal_index: list of first buy indexes for each deal
    - list_deal_buy_price:  list of buying price (open price of T+1)
    - list_deal_sell_index: list of first sell indexes for each deal
    - list_deal_sell_price: list of selling price (open price of T+1)
    - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
    - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
    """

    def sell_decision(idx):
        price = df_price['Open_1D'].iloc[idx]
        list_deal_sell_index.append(idx)
        list_deal_sell_price.append(price)
        list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
        list_deal_market_price.append(
            (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

    # Initialize signal list with "none"
    list_signal = [None] * df_price.shape[0]

    # Mark sell signals with corresponding reasons
    for i, j in zip(list_sell_index, list_sell_reason):
        list_signal[i] = j

    # Mark buy signals
    for i in list_buy_index:
        list_signal[i] = "buy"

    # Initialize output lists
    list_deal_index = []
    list_deal_buy_price = []
    list_deal_sell_index = []
    list_deal_sell_price = []
    list_deal_profit = []
    list_deal_result = []
    list_deal_market_price = []

    current_status = None  # Status can be None or "buy"
    predefine_sell_data = None
    for i in range(min(list_buy_index), df_price.shape[0]):
        # Look for the buy signal
        if current_status is None:
            if list_signal[i] != "buy":
                continue
            # Do not evaluate deal happens today
            if i < df_price.shape[0] - 1:
                price = df_price['Open_1D'].iloc[i]
                list_deal_index.append(i)
                list_deal_buy_price.append(price)
                # predefine_sell_data = df_predefine_sell.loc[i]
                current_status = "buy"

        # Look for the cutloss or sell signal
        elif current_status == "buy":

            if i == df_price.shape[0] - 1:  # Reaching the end
                list_deal_result.append("hold")
                current_status = None
                sell_decision(i)
                continue

            # T + 3
            if i < list_deal_index[-1] + 3:
                continue

            # Cutloss
            close = df_price['Close'].iloc[i]
            if close < list_deal_buy_price[-1] * (1 - cutloss):
                list_deal_result.append("cutloss")
                current_status = None
                sell_decision(i)
                continue

            # # Sell by predefine sell signal
            # if predefine_sell_data['pre_sell_id'] == i:
            #     list_deal_result.append(predefine_sell_data['pre_sell'])
            #     current_status = None
            #     sell_decision(i)
            #     continue

            # Sell by pattern signal
            if list_signal[i] != "buy" and list_signal[i] is not None:
                list_deal_result.append(list_signal[i])
                current_status = None
                sell_decision(i)

    return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
            list_deal_result, list_deal_market_price)


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def eval_filter_ticker_v2(pdxx, dictFilter, CUTLOSS=0.1):
    """
    Evaluate filter for a ticker
    Input:
        pdxx: ticker daily series
        dictFilter: dictionary of filters
        CUTLOSS: cutloss threshold
    Output:
        pdy: dataframe of latest hit
    """
    pd_all = pdxx.copy()
    ticker = pd_all.ticker.head(1).values[0]
    # Apply sell filters
    s_cols = ['time', 'Close', 'Volume', 'Sell_filter']
    sell_data = []

    for f in dictFilter:
        if f.startswith('~'):
            try:
                pd_Sell_filtered = pd_all.query(f'({dictFilter[f]})').copy()
                pd_Sell_filtered['Sell_filter'] = f[1:]
                sell_data.append(pd_Sell_filtered)
            except Exception as e:
                print(f"{ticker}--{f[1:]}--error: {e}")

    _sell_data = [data for data in sell_data if not data.empty]
    pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True) if _sell_data else sell_data[-1]
    pd_sell = pd_sell[s_cols]

    # Apply buy filters
    b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Volume']
    buy_data = []

    for f in dictFilter:
        if f.startswith('_'):
            try:
                pd_buy_filtered = pd_all.query(f'({dictFilter[f]})').copy()
                pd_buy_filtered['filter'] = f[1:]
                buy_data.append(pd_buy_filtered)
            except Exception as e:
                print(f"{ticker}--{f[1:]}--error: {e}")

    _buy_data = [data for data in buy_data if not data.empty]
    pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]

    pd_buy['hit'] = 1
    pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])
    pd_buy = pd_buy[b_cols]

    # Evaluate by deal
    # Combination sell pattern
    s_filters = []
    for f in dictFilter:
        if f.startswith('~'):
            s_filters.append(f[1:])
    C_s_filters = []
    for r in range(1, len(s_filters) + 1):
        C_s_filters.extend([list(comb) for comb in combinations(s_filters, r)])

    buy_reasons = list(pd_buy['filter'].unique())

    l_pd_deal = []
    now = pd_all.iloc[-1:][['time', 'Close', 'Volume']].copy()
    now['Sell_filter'] = 'Hold'
    for buy_reason in buy_reasons:
        buy_reason_indexes = list(pd_buy[pd_buy['filter'] == buy_reason].index).copy()

        for C_s_filter in C_s_filters:
            pd_sell_temp = pd_sell.query(f"Sell_filter == {C_s_filter}").copy()
            pd_sell_temp = pd_sell_temp._append(now)
            sell_indexes = list(pd_sell_temp.index)
            sell_reasons = list(pd_sell_temp['Sell_filter'])

            result = find_sell(df_price=pd_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']],
                               list_buy_index=buy_reason_indexes,
                               list_sell_index=sell_indexes, list_sell_reason=sell_reasons, cutloss=CUTLOSS)

            deal_time = pd_all.loc[result[0]]['time'].values
            ticker = pd_all.loc[result[0]]['ticker'].values
            deal_sell_time = pd_all.loc[result[2]]['time'].values
            pd_deal_pa = pd.DataFrame({'ticker': ticker,
                                       'time': deal_time,
                                       'buy_price': result[1],
                                       'sell_price': result[3],
                                       'profit': result[4],
                                       'sell_filter': result[5],
                                       'sell_time': deal_sell_time,
                                       'profit_vni': result[6],
                                       })
            C_s_filter.sort()
            pd_deal_pa['filter'] = f"{buy_reason}_{C_s_filter}"

            l_pd_deal.append(pd_deal_pa)

    deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                 'profit_vni']
    pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

    pd_deal = pd_deal.merge(pd_all[['time', 'Open_1D', 'Close']], on=['time'], how='left')
    return pd_deal


def parse_input():
    """
    Parses command-line inputs provided by the user.
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Template for Python script with user input.")

    parser.add_argument('--profiles', type=str, default='email', help='Input data file or raw input string')
    parser.add_argument('--cutloss', type=float, default=0.15, help='cutloss')

    return parser.parse_args()


def main():
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """
    # args = parse_input()
    # configFilters = json.loads(open("config.json", "r").read())
    # dictFilter = json.loads(configFilters[args.profiles][0])
    # if 'Init' in dictFilter.keys():
    #     for key, value in dictFilter.items():
    #         dictFilter[key] = value.replace("{Init}", dictFilter['Init'])
    # cutloss = args.cutloss
    dictFilter = {
        "Init": "(Volume*Price>1e+3) & (time>='2010-01-01') & (time<='2025-01-01')",
        "_VolMax5Y": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
        "_VolMax5Y1": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 2*MA200) & (NP_P0 < 0.8*NP_P1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        "$VolMax5Y": "MA2, MA3, MA4, S13, SellTest, SellMaxVol",
        "$VolMax5Y1": "MA2, MA3, MA4, S13, SellTest, SellMaxVol, Exit1",
        "exit_under_profit_1m": "0.07",
        "#Exit1": "exit_under_profit('1M', 0.03)",

    }
    cutloss = 0.15

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            res = eval_filter_ticker_v2(pdxx, dictFilter, CUTLOSS=cutloss)
            return res
        except Exception as e:
            print(f"Error: {ticker}: {e}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    # Process dataframe
    pd_deal = pd.concat([res for res in lres if res is not None and not res.empty], axis=0).reset_index(drop=True)

    dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'count_cutloss': 'count', 'n_month': 'sum',
            'count_hold': 'count', 'count_win': 'count', 'count_loss': 'count', 'count_sell': 'count',
            'n_quarter': 'sum', 'sum_profit': 'sum',
            }

    # pd_deal
    df_process = PreProcess()
    pd_deal = df_process.deals(pd_deal)
    _pdd = df_process.group_by(pd_deal, ['filter'])

    # Simulation deal
    start_date, end_date = parse_time(dictFilter)
    simulation = Simulation(pd_deal, start_date=start_date, end_date=end_date, initial_assets=1e8, max_deals=10)
    result = simulation.run_fast(iterate=2)

    si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_utilization']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'utilization']

    for pattern, value in result.items():
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            _pdd.loc[_pdd['filter'] == pattern, si_col] = value[si_result_col]

    pdd = _pdd[['filter', 'deal']].copy()

    pdd['%win_deal'] = (_pdd['count_win'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%loss_deal'] = (_pdd['count_loss'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%hold_deal'] = (_pdd['count_hold'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd["%cutloss_deal"] = (_pdd['count_cutloss'].astype(int) / _pdd['deal'].astype(int)) * 100

    pdd['profit_win'] = _pdd['p_win'].values
    pdd['profit_loss'] = _pdd['p_loss'].values
    pdd['profit_hold'] = _pdd['p_hold'].values
    pdd['profit_cutloss'] = _pdd['p_cutloss'].values
    pdd['holding_period'] = _pdd['holding_period'].values
    pdd['profit_expected'] = _pdd['profit'].values
    pdd['profit_vni'] = _pdd['profit_vni'].values
    pdd['corr_deal_vni'] = _pdd['corr'].values

    pdd['si_total_time'] = _pdd['si_total_time']
    pdd['si_time_in_market'] = _pdd['si_time_in_market']
    pdd['si_deals'] = _pdd['si_deals']
    pdd['si_profit'] = _pdd['si_profit']
    pdd['si_cash_profit'] = _pdd['si_cash_profit']
    pdd['si_return'] = _pdd['si_return']

    return pdd.sort_values(['filter', "si_return"], ascending=[False, False])


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    evaluate = main()
    evaluate.to_csv('tuning/temp/evaluate_buy_2_sell.csv', index=False)
