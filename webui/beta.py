import pandas as pd
import numpy as np
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
#



# df_risk = pd.read_csv('preprocess_others_risk_indicators.csv')
# c_bins = [c for c in df_risk.columns if c.endswith('_bin')]
# c_downside = [c for c in df_risk.columns if not c.endswith('_bin') and c != 'quarter']
#
# tickers = sorted(list(set([c.split('_')[0] for c in c_downside])))
#
# df_result = df_risk[['quarter']].copy()
# for ticker in tickers:
#     beta = df_risk[f'{ticker}_downside_beta_bin']
#     dev = df_risk[f'{ticker}_downside_deviation_bin']
#     diff = (beta - dev).abs()
#     df_result[ticker] = np.where(diff < 3, np.ceil((beta + dev) / 2).astype(int), np.maximum(beta, dev))
#     df_risk[f'{ticker}_risk_rating'] = df_result[ticker]
#     c_bins.append(f'{ticker}_risk_rating')
#
# c_bins = sorted(c_bins)
# c_bins.insert(0, 'quarter')
# df_result.to_csv('risk_rating.csv', index=False)
# df_risk[c_bins].to_csv('risk_rating_bin.csv', index=False)
#
#
# df_risk = pd.read_csv('risk_rating.csv')
# risk_rating_bin = pd.read_csv('risk_rating_bin.csv')
#
# # Lấy dòng mới nhất
# latest_quarter = df_risk['quarter'].max()
# row_latest = df_risk[df_risk['quarter'] == latest_quarter]
# # Lấy tất cả cột kết thúc bằng '_bin'
# c_bins = [c for c in df_risk.columns if c.endswith('_bin')]
#
# c_bins = row_latest.columns.tolist()
# c_bins.remove('quarter')
# # Gộp tất cả giá trị bin vào một Series
# # all_bins = df_risk[c_bins].values.flatten()
# all_bins = row_latest[c_bins].values.flatten()
#
# # Đếm tần suất từng bin
# bin_counts = pd.Series(all_bins).value_counts().sort_index()
#
# # Vẽ biểu đồ
# plt.figure(figsize=(8, 5))
# plt.bar(bin_counts.index.astype(str), bin_counts.values)
# plt.title("Tần suất các giá trị BIN")
# plt.xlabel("BIN Value")
# plt.ylabel("Count")
# plt.tight_layout()
# plt.show()

# Danh sách ticker muốn xử lý
# tickers = ['ACB', 'TMP', 'VCB', 'FPT', 'VIC', 'MBB', 'HPG', 'DPG', 'TV2', 'HHS', 'PDR',
#            'TCH']
# market_ticker = 'VNINDEX'
#
# # Load dữ liệu thị trường
# market = pd.read_csv(f'{market_ticker}.csv')
# market['time'] = pd.to_datetime(market['time'])
# market = market[['time', 'close']].rename(columns={'close': 'market'})
#
# # Lọc khoảng thời gian
# cutoff_start = pd.to_datetime('2020-01-01')
# cutoff_end = pd.to_datetime('2025-05-01')
# df_market = market.query('time >= @cutoff_start and time <= @cutoff_end')
# df_market['market'] = pd.to_numeric(df_market['market'], errors='coerce')
#
# # Đặt index
# df_market.set_index('time', inplace=True)
#
# # Lặp qua từng cổ phiếu
# for tk in tickers:
#     stock = pd.read_csv(f'{tk}.csv')
#     stock['time'] = pd.to_datetime(stock['time'])
#     df_stock = stock[['time', 'Close']].rename(columns={'Close': 'stock'})
#     df_stock = df_stock.query('time >= @cutoff_start and time <= @cutoff_end')
#     df_stock['stock'] = pd.to_numeric(df_stock['stock'], errors='coerce')
#     df_stock.set_index('time', inplace=True)
#
#     # Merge dữ liệu với market
#     merged = pd.merge(df_stock, df_market, left_index=True, right_index=True).dropna()
#
#     # Nếu muốn resample theo tuần, dùng dòng sau:
#     merged = merged.resample('W-FRI').last().dropna()
#
#     # Tính returns
#     merged['stock_return'] = merged['stock'].pct_change()
#     merged['market_return'] = merged['market'].pct_change()
#     merged['log_stock_return'] = np.log(merged['stock_return'] + 1)
#     merged['log_market_return'] = np.log(merged['market_return'] + 1)
#     merged = merged.dropna()
#
#     # Beta log return
#     log_cov_matrix = np.cov(merged['log_stock_return'], merged['log_market_return'])
#     log_beta = log_cov_matrix[0, 1] / log_cov_matrix[1, 1]
#
#     # Beta và Adjusted Beta
#     cov_matrix = merged[['stock_return', 'market_return']].cov()
#     beta = cov_matrix.loc['stock_return', 'market_return'] / cov_matrix.loc['market_return', 'market_return']
#     adjusted_beta = 0.67 * beta + 0.33 * 1.0
#
#     # Downside Deviation
#     target = 0
#     downside_returns = np.minimum(merged['stock_return'] - target, 0)
#     downside_deviation = np.sqrt((downside_returns ** 2).mean())
#
#     # Downside Beta
#     down_market = merged[merged['market_return'] < 0]
#     down_cov = down_market[['stock_return', 'market_return']].cov()
#     downside_beta = down_cov.loc['stock_return', 'market_return'] / down_cov.loc['market_return', 'market_return']
#
#     # In kết quả
#     print(f"\n--- {tk} ---")
#     print(f"Beta (5y): {round(beta, 4)}")
#     print(f"Log Beta (5y): {round(log_beta, 4)}")
#     print(f"Adjusted Beta: {round(adjusted_beta, 4)}")
#     print(f"Downside Beta: {round(downside_beta, 4)}")
#     print(f"Downside Deviation: {round(downside_deviation, 4)}")


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Giả sử df_vnindex đã sorted theo 'time'
df = pd.read_csv('VNINDEX.csv')
df = df.sort_values('time').reset_index(drop=True)

# Tính các cột cần thiết
df['vni_0'] = df['close']
df['vni_p1w'] = df['close'].shift(5)   # tuần trước
df['vni_1w'] = df['close'].shift(-5)   # tuần sau
df['low_1w'] = df['low'].rolling(window=5).min().shift(-5)  # đáy của tuần sau

# Tính các tỉ lệ
df['ratio_0_p1w'] = df['vni_0'] / df['vni_p1w']
df['ratio_1w_0'] = df['vni_1w'] / df['vni_0']
df['drawdown_1w'] = df['low_1w'] / df['vni_0']

# Bỏ các dòng thiếu dữ liệu
df_clean = df.dropna(subset=['ratio_0_p1w', 'ratio_1w_0', 'drawdown_1w'])

# =========================
# 1. SCATTER PLOTS
# =========================
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.scatter(df_clean['ratio_0_p1w'], df_clean['ratio_1w_0'], alpha=0.6)
plt.xlabel('vni_0 / vni_p1w')
plt.ylabel('vni_1w / vni_0')
plt.title('Tương quan tuần sau vs tuần này')

plt.subplot(1, 2, 2)
plt.scatter(df_clean['ratio_0_p1w'], df_clean['drawdown_1w'], alpha=0.6)
plt.xlabel('vni_0 / vni_p1w')
plt.ylabel('vni_low_1w / vni_0')
plt.title('Tương quan đáy tuần sau vs tuần này')
plt.tight_layout()
plt.show()

# =========================
# 2. BINNING + STATISTICS
# =========================
bins = np.arange(0.95, 1.051, 0.01)
bins = np.insert(bins, 0, 0.0)  # thêm bin từ 0 -> 0.95

df_clean['bin'] = pd.cut(df_clean['ratio_0_p1w'], bins=bins)

# Tính trung bình và std cho mỗi bin
bin_stats = df_clean.groupby('bin').agg(
    mean_return=('ratio_1w_0', 'mean'),
    std_return=('ratio_1w_0', 'std'),
    mean_drawdown=('drawdown_1w', 'mean'),
    count=('drawdown_1w', 'count')
).reset_index()

# Hiển thị kết quả
from IPython.display import display
# import ace_tools as tools; tools.display_dataframe_to_user(name="Thống kê theo Bin", dataframe=bin_stats)
display(bin_stats)




# Tạo thêm các biến tính toán
df["ret_0"] = df["vni_0"] / df["vni_p1w"]
df["ret_1w"] = df["vni_1w"] / df["vni_0"]
df["low_drawdown"] = df["low_1w"] / df["vni_0"]

# ---------- 1. SCATTER PLOT ----------
plt.figure(figsize=(8,6))
scatter = plt.scatter(df["ret_0"], df["ret_1w"], c=df["low_drawdown"], cmap='viridis', alpha=0.7)
plt.colorbar(scatter, label="Low/VNI_0 (Đáy tuần vừa rồi)")
plt.axhline(1, color='gray', linestyle='--')
plt.axvline(1, color='gray', linestyle='--')
plt.xlabel("VNI_0 / VNI_p1w (Hiệu suất tuần hiện tại)")
plt.ylabel("VNI_1w / VNI_0 (Hiệu suất tuần kế tiếp)")
plt.title("Scatter: Hiệu suất tuần trước vs tuần sau")
plt.grid(True)
plt.tight_layout()
plt.show()

# ---------- 2. CHIA BIN ----------
bins = np.arange(0.95, 1.051, 0.01)
bins = np.insert(bins, 0, 0.0)
df["bin"] = pd.cut(df["ret_0"], bins=bins)

grouped = df.groupby("bin").agg(
    mean_next_week_ret = ("ret_1w", "mean"),
    median_next_week_ret = ("ret_1w", "median"),
    std_next_week_ret = ("ret_1w", "std"),
    win_rate = ("ret_1w", lambda x: np.mean(x > 1)),
    count = ("ret_1w", "count"),
    avg_drawdown = ("low_drawdown", "mean")
).reset_index()

# Hiển thị bảng kết quả
# from ace_tools import display_dataframe_to_user
# display_dataframe_to_user(name="Phân tích theo BIN", dataframe=grouped)

# Hiển thị kết quả
from IPython.display import display
# import ace_tools as tools; tools.display_dataframe_to_user(name="Thống kê theo Bin", dataframe=bin_stats)
display(grouped)
