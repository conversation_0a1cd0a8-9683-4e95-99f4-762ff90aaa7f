import io
import json
from datetime import datetime, timedelta
from functools import wraps

from joblib import Memory
import numpy as np
import pandas as pd
from core_utils.redis_cache import EvalRedis
from core_utils.base_eval import Simulation, AllEval, ShortSellEval, PreProcess, WeightEval
from core_utils.constant import JOBLIB_CACHE_DIR, FPATH

memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=5e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()


class ShortEvaluation(ShortSellEval):
    def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, cache_service=redis_cache):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         cache_service=cache_service)


class AllEvaluation(AllEval):
    def __init__(self, stock, data_frame, dict_filter, df_market=None, cutloss=0.15, cache_service=redis_cache):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         df_market=df_market, cache_service=cache_service)


class WeightEvaluation(WeightEval):
    def __init__(self, stock, df_all, dict_filter, weight, threshold_buy, threshold_sell, cutloss=0.15, lookback=5,
                 k_exp=0, cache_service=redis_cache):
        super().__init__(stock=stock, data_frame=df_all, dict_filter=dict_filter, cutloss=cutloss, weight=weight,
                         threshold_buy=threshold_buy, threshold_sell=threshold_sell, lookback=lookback, k_exp=k_exp,
                         cache_service=cache_service)


class Simulation_all(Simulation):
    def __init__(self, start_date='2017-01-01', end_date='2025-01-01', initial_assets=1e8, max_deals=10,
                 num_proc=8, report=False, combine=None, cache_service=memory, combine_block=False, df_deal=None):
        super().__init__(df_deals=df_deal, start_date=start_date, end_date=end_date, initial_assets=initial_assets,
                         max_deals=max_deals, num_proc=num_proc, report=report, cache_service=cache_service,
                         combine=combine, is_block_buy=combine_block)


class Simulation_weight(Simulation):
    def __init__(self, start_date='2017-01-01', end_date='2025-01-01', initial_assets=1e8, max_deals=10,
                 num_proc=8, report=False, combine=None, cache_service=memory, df_deal=None):
        super().__init__(df_deal=df_deal, start_date=start_date, end_date=end_date, initial_assets=initial_assets,
                         max_deals=max_deals, num_proc=num_proc, report=report, cache_service=cache_service,
                         combine=combine)

    # def preprocess_df_deals(self, df_deals):
    #     df = df_deals.query("time >= @self.start_date and time <= @self.end_date").copy()
    #     df_list = []
    #     for date, group in df.groupby('time'):
    #         if group.shape[0] >= self.max_deals:
    #             limit_weight = group['score'].sort_values(ascending=False).iloc[self.max_deals - 1]
    #             group = group[group['score'] >= limit_weight]
    #         df_list.append(group)
    #
    #     return pd.concat(df_list, ignore_index=True)

    def simulate_trading(self, deals, buffer_data_df, start_date, end_date, initial_assets, initial_slot, si_type,
                         report=False, detail=False):
        log = []
        buffer_data = buffer_data_df.iloc[[buffer_data_df['time'].searchsorted(end_date, side='right') - 1]]

        available_assets = initial_assets
        active_deals = []
        completed_deals = []
        skip_deals = []
        utilization = [0]

        profit = 0
        peak_deals = 0

        # time_in_market
        start_time = []
        end_time = []

        sell_time_remove = []

        state = {
            'available_assets': available_assets,
            'initial_slot': initial_slot,
            'initial_assets': initial_assets,
            'start_date': start_date,
            'end_date': end_date,
            'deal_date': start_date,
            'before_deal_date': start_date,
            'total_assets': initial_assets,
        }
        for i, deal in deals.iterrows():
            # check duplicate
            if deal['ticker'] in [active_deal['ticker'] for active_deal in active_deals]:
                continue
            # check n_slot
            # process complete deal
            keep_deals = []
            temp_done = []

            # Utilization
            state['deal_date'] = deal['time']
            utilize, state = self.calculate_utilization(active_deals, state, buffer_data_df)
            utilization.extend(utilize)

            # Update num_shares_to_sell if multi ticker deal sell same day
            if active_deals:
                df = pd.DataFrame(active_deals)
                if not df.empty:
                    df['num_shares_to_sell'] = df.groupby(['ticker', 'sell_time'])['num_shares_purchased'].transform(
                        'sum')
                active_deals = [row for _, row in df.iterrows()]

            for j, active_deal in enumerate(active_deals[:]):
                if active_deal['sell_time'] <= deal['time']:
                    temp_done.append(active_deal['ticker'])

                    # Calculate profit using check_to_take_profit_deal
                    delta = self.check_to_take_profit_deal(active_deal)
                    adj_sell_price = active_deal['sell_price'] * (1 - delta)
                    active_deal['profit'] = (adj_sell_price - active_deal['buy_price']) / active_deal['buy_price'] * 100

                    # Update available assets
                    profit += active_deal['profit']
                    # for sales  tax
                    available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.9975

                    completed_deals.append(active_deal)
                    sell_time_remove.append(active_deal['sell_time'])
                else:
                    keep_deals.append(active_deal)

            active_deals = keep_deals.copy()

            # end - time_in_market
            if (not active_deals) and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Check the budget for the new deal
            state['available_assets'] = available_assets
            investment_amount, investment_price, state = self.check_to_buy_new_deal(deal, active_deals, buffer_data_df,
                                                                                    state, si_type=si_type)

            if investment_amount:
                deal['investment_amount'] = investment_amount * 0.9985
                deal['num_shares_purchased'] = deal['investment_amount'] / investment_price
                # for buy tax
                available_assets -= investment_amount

                # start - time_in_market
                if not active_deals:
                    start_time.append(deal['time'])

                active_deals.append(deal)
                peak_deals = max(peak_deals, len(active_deals))

            else:
                skip_deals.append(deal)

        temp_done = []

        # Update num_shares_to_sell if multi ticker deal sell same day
        if active_deals:
            df = pd.DataFrame(active_deals)
            if not df.empty:
                df['num_shares_to_sell'] = df.groupby(['ticker', 'sell_time'])['num_shares_purchased'].transform(
                    'sum')
            active_deals = [row for _, row in df.iterrows()]

        for active_deal in (active_deals[:]):
            temp_done.append(active_deal['ticker'])
            if pd.to_datetime(active_deal['sell_time']) > pd.to_datetime(end_date):
                active_deal['sell_time'] = end_date
                active_deal['sell_price'] = buffer_data.at[buffer_data.index[0], active_deal['ticker']]
            delta = self.check_to_take_profit_deal(active_deal)
            adj_sell_price = active_deal['sell_price'] * (1 - delta)
            active_deal['profit'] = (adj_sell_price - active_deal['buy_price']) / active_deal['buy_price'] * 100

            profit += active_deal['profit']
            # for sales  tax
            available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.9975

            completed_deals.append(active_deal)
            sell_time_remove.append(active_deal['sell_time'])

        if sell_time_remove:
            end_time.append(max(sell_time_remove))
        else:
            end_time.append(end_date)
        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days - 1

        utilization = np.nanmean(utilization)
        df_comp = pd.DataFrame(completed_deals)
        if df_comp.empty:
            result = {
                'match_deals': 0,
                'total_time': total_time,
                'time_in_market': time_in_market,
                'profit': 0,
                'cash_profit': ((available_assets / initial_assets) - 1) * 100,
                'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
                'available_assets': available_assets,
                'utilization': utilization,
                'sharpe_ratio': 0,
                'win_deal': 0,
                'win_quarter': 0,
                'set_ticker': [],
                'set_quarter_ticker': [],
                'peak_number_deals': peak_deals,
            }
        else:
            df_comp['count'] = 1
            df_comp['win_count'] = df_comp['profit'].apply(lambda x: 1 if x > 0 else np.nan)
            df_comp_q = df_comp.groupby('quarter', as_index=False).agg(
                {'profit': 'sum', 'win_count': 'count', 'count': 'count'})

            win_deal = df_comp[df_comp['profit'] > 0].shape[0] / df_comp.shape[0]
            win_quarter = df_comp_q[df_comp_q['win_count'] / df_comp_q['count'] > 0.5].shape[0] / df_comp_q.shape[0]

            set_ticker = list(set([d['ticker'] for d in completed_deals]))
            set_quarter_ticker = list(set([(d['ticker'], d['quarter_fr']) for d in completed_deals]))

            result = {
                'match_deals': len(completed_deals),
                'total_time': total_time,
                'time_in_market': time_in_market,
                'profit': profit / len(completed_deals),
                'cash_profit': ((available_assets / initial_assets) - 1) * 100,
                'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
                'available_assets': available_assets,
                'utilization': utilization,
                'win_deal': win_deal * 100,
                'win_quarter': win_quarter * 100,
                'set_ticker': set_ticker,
                'set_quarter_ticker': set_quarter_ticker,
                'peak_number_deals': peak_deals,
            }
        if detail:
            result['completed_deals'] = pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame()
            result['skip_deals'] = pd.concat(skip_deals, axis=1).T if skip_deals else pd.DataFrame()
            result['log'] = '\n'.join(log)

        return result

    def simulation(self, seed, si_type='full_cash'):
        """
        Simulate trading strategy for all filter string in df_deals.
        We will use full cash with 10 deals.
        :param seed: random seed
        :param si_type: type of simulation ["full_cash", "adjust_cash"]
        :return: dictionary of profit
        """

        def shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
            pd_deals["rand_order"] = np.random.RandomState(seed).rand(len(pd_deals))  # Thêm cột random
            pd_deals = (pd_deals.groupby("time", group_keys=False)
                        .apply(lambda x: x.sort_values(["score", "rand_order"], ascending=[False, True]))
                        .reset_index(drop=True)).drop(columns=["rand_order"])
            return pd_deals

        result = {}
        np.random.seed(seed)

        start_period = (pd.to_datetime(self.start_date) + pd.Timedelta(seed, "d")).strftime("%Y-%m-%d")
        end_period = (pd.to_datetime(start_period) + pd.Timedelta(self.period, "d")).strftime("%Y-%m-%d")

        df_deals = self.df_deals.query("@start_period <= time <= @end_period").copy()
        df_buffers = self.df_buffers.loc[
            (self.df_buffers["time"] >= start_period) & (self.df_buffers["time"] <= end_period)]

        df_deals = shuffle_by_date(df_deals, seed)
        # print(f"seed: {seed}, start_period: {start_period}, end_period: {end_period}, df_deals.shape: {df_deals.shape}")
        #

        result['sum_weight'] = self.simulate_trading(deals=df_deals,
                                                     buffer_data_df=df_buffers, start_date=start_period,
                                                     end_date=end_period,
                                                     initial_assets=self.initial_assets, initial_slot=self.max_deals,
                                                     si_type=si_type, report=self.report)

        return result

    def simulate_details(self, deals, buffer_data_df, start_date, end_date, initial_assets, initial_slot, si_type):
        # init
        log = []
        buffer_data = buffer_data_df.iloc[[buffer_data_df['time'].searchsorted(end_date, side='right') - 1]]

        order_sell = 0
        order_buy = 0
        order_skip = 0
        deals['idx'] = deals.index
        deals[['status', 'order', 'slot', 'total_investment_amounts', 'available_cash', 'investment_amount',
               'cash_before_buy', 'cash_after_buy', 'cash_before_sell', 'cash_after_sell', 'order_sell']] = np.nan

        available_assets = initial_assets
        active_deals = []
        completed_deals = []
        skip_deals = []
        utilization = [0]

        profit = 0
        peak_deals = 0

        # time_in_market
        start_time = []
        end_time = []

        sell_time_remove = []

        state = {
            'available_assets': available_assets,
            'initial_slot': initial_slot,
            'initial_assets': initial_assets,
            'start_date': start_date,
            'end_date': end_date,
            'deal_date': start_date,
            'before_deal_date': start_date,
            'total_assets': initial_assets,
        }
        for i, deal in deals.iterrows():
            # check duplicate
            if deal['ticker'] in [active_deal['ticker'] for active_deal in active_deals]:
                deal['status'] = 'duplicate'
                deal['order'] = order_skip
                deal['total_investment_amounts'] = sum(
                    [active_deal['investment_amount'] for active_deal in active_deals])
                deal['available_cash'] = available_assets
                deal['slot'] = initial_slot - len(active_deals)

                order_skip += 1
                skip_deals.append(deal)
                continue
            # check n_slot
            # process complete deal
            keep_deals = []
            temp_done = []

            # Utilization
            state['deal_date'] = deal['time']
            utilize, state = self.calculate_utilization(active_deals, state, buffer_data_df)
            utilization.extend(utilize)

            # Update num_shares_to_sell if multi ticker deal sell same day
            if active_deals:
                df = pd.DataFrame(active_deals)
                if not df.empty:
                    df['num_shares_to_sell'] = df.groupby(['ticker', 'sell_time'])['num_shares_purchased'].transform(
                        'sum')
                active_deals = [row for _, row in df.iterrows()]

            for j, active_deal in enumerate(active_deals[:]):
                if active_deal['sell_time'] <= deal['time']:
                    temp_done.append(active_deal['ticker'])

                    # Calculate profit using check_to_take_profit_deal
                    delta = self.check_to_take_profit_deal(active_deal)
                    adj_sell_price = active_deal['sell_price'] * (1 - delta)
                    active_deal['profit'] = (adj_sell_price - active_deal['buy_price']) / active_deal['buy_price'] * 100

                    # Update available assets
                    profit += active_deal['profit']
                    active_deal['cash_before_sell'] = available_assets
                    # for sales  tax
                    available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.9975

                    active_deal['cash_after_sell'] = available_assets
                    active_deal['order_sell'] = order_sell
                    order_sell += 1

                    #  Add logs
                    log_entry = {
                        "action": "Sell",
                        "date": active_deal['sell_time'],
                        "ticker": active_deal['ticker'],
                        "filter": active_deal['sell_filter'],
                        "profit_percentage": round(active_deal['profit'], 2),
                        "num_of_shares_held": active_deal['num_shares_purchased'],
                        "daily_trading_volume": active_deal['Volume_sell'],
                        "buy_date": active_deal['time'],
                        "holding_days": active_deal['holding_period'],
                        "remaining_cash": round(available_assets, 2),
                        "score": round(active_deal['sell_score'], 2),
                        "utilization": utilization[-1],
                        "total_assets": state['total_assets'],
                        "active_deals": [
                            {
                                "ticker": d['ticker'],
                                "filter": d['filter'],
                                "investment": d['investment_amount'],
                                "investment_ratio": (d['investment_amount'] / state['total_assets']) * 100,
                                "num_of_shares_held": d['num_shares_purchased'],
                                "buy_date": d['time'],
                            }
                            for d in active_deals if d['ticker'] not in temp_done
                        ]
                    }
                    log.append(json.dumps(log_entry))

                    completed_deals.append(active_deal)
                    sell_time_remove.append(active_deal['sell_time'])
                else:
                    keep_deals.append(active_deal)

            active_deals = keep_deals.copy()

            # end - time_in_market
            if (not active_deals) and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Check the budget for the new deal
            state['available_assets'] = available_assets
            investment_amount, investment_price, state = self.check_to_buy_new_deal(deal, active_deals, buffer_data_df,
                                                                                    state,
                                                                                    si_type=si_type)

            if investment_amount:
                deal['investment_amount'] = investment_amount * 0.9985
                deal['num_shares_purchased'] = deal['investment_amount'] / investment_price

                deal['cash_before_buy'] = available_assets
                # for buy tax
                available_assets -= investment_amount

                # start - time_in_market
                if not active_deals:
                    start_time.append(deal['time'])

                deal['status'] = 'keep'
                deal['order'] = order_buy
                deal['cash_after_buy'] = available_assets
                # active_deals.append(deal)
                order_buy += 1
                #  Add logs
                log_entry = {
                    "action": "Buy",
                    "date": deal['time'],
                    "ticker": deal['ticker'],
                    "filter": deal['filter'],
                    "investment": round(investment_amount, 2),
                    "price": investment_price,
                    "num_of_shares_held": deal['num_shares_purchased'],
                    "daily_trading_volume": deal['Volume'],
                    "remaining_cash": round(available_assets, 2),
                    "score": round(deal['score'], 2),
                    "utilization": utilization[-1],
                    "total_assets": state['total_assets'],
                    "active_deals": [
                        {
                            "ticker": d['ticker'],
                            "filter": d['filter'],
                            "investment": d['investment_amount'],
                            "investment_ratio": (d['investment_amount'] / state['total_assets']) * 100,
                            "num_of_shares_held": d['num_shares_purchased'],
                            "buy_date": d['time'],
                        }
                        for d in active_deals
                    ]
                }
                log.append(json.dumps(log_entry))

                active_deals.append(deal)
                peak_deals = max(peak_deals, len(active_deals))

            else:
                deal['status'] = 'full_portfolio'
                deal['order'] = order_skip
                order_skip += 1

                skip_deals.append(deal)

            # update logs
            deal['total_investment_amounts'] = sum([active_deal['investment_amount'] for active_deal in active_deals])
            deal['available_cash'] = available_assets
            deal['slot'] = initial_slot - len(active_deals)

        # sell_time_remove = []
        empty_slot = initial_slot - len(active_deals)
        total_invest = sum([active_deal['investment_amount'] for active_deal in active_deals])

        temp_done = []

        # Update num_shares_to_sell if multi ticker deal sell same day
        if active_deals:
            df = pd.DataFrame(active_deals)
            if not df.empty:
                df['num_shares_to_sell'] = df.groupby(['ticker', 'sell_time'])['num_shares_purchased'].transform(
                    'sum')
            active_deals = [row for _, row in df.iterrows()]

        for active_deal in (active_deals[:]):
            temp_done.append(active_deal['ticker'])
            if pd.to_datetime(active_deal['sell_time']) > pd.to_datetime(end_date):
                active_deal['sell_time'] = end_date
                active_deal['sell_price'] = buffer_data.at[buffer_data.index[0], active_deal['ticker']]
            delta = self.check_to_take_profit_deal(active_deal)
            adj_sell_price = active_deal['sell_price'] * (1 - delta)
            active_deal['profit'] = (adj_sell_price - active_deal['buy_price']) / active_deal['buy_price'] * 100

            profit += active_deal['profit']
            active_deal['cash_before_sell'] = available_assets
            # for sales  tax
            available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.9975

            active_deal['cash_after_sell'] = available_assets
            active_deal['order_sell'] = order_sell
            order_sell += 1

            #  Add logs
            log_entry = {
                "action": "Sell",
                "date": active_deal['sell_time'],
                "ticker": active_deal['ticker'],
                "filter": active_deal['sell_filter'],
                "profit_percentage": round(active_deal['profit'], 2),
                "num_of_shares_held": active_deal['num_shares_purchased'],
                "daily_trading_volume": active_deal['Volume_sell'],
                "buy_date": active_deal['time'],
                "holding_days": active_deal['holding_period'],
                "remaining_cash": round(available_assets, 2),
                "score": round(active_deal['sell_score'], 2),
                "utilization": utilization[-1],
                "total_assets": state['total_assets'],
                "active_deals": [
                    {
                        "ticker": d['ticker'],
                        "filter": d['filter'],
                        "investment": d['investment_amount'],
                        "investment_ratio": (d['investment_amount'] / state['total_assets']) * 100,
                        "num_of_shares_held": d['num_shares_purchased'],
                        "buy_date": d['time'],
                    }
                    for d in active_deals if d['ticker'] not in temp_done
                ]
            }
            log.append(json.dumps(log_entry))

            # update logs
            active_deal['slot'] = empty_slot
            active_deal['total_investment_amounts'] = total_invest
            empty_slot += 1
            total_invest -= active_deal['investment_amount']

            completed_deals.append(active_deal)
            sell_time_remove.append(active_deal['sell_time'])

        if sell_time_remove:
            end_time.append(max(sell_time_remove))
        else:
            end_time.append(end_date)
        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days - 1

        utilization = np.nanmean(utilization)
        df_comp = pd.DataFrame(completed_deals)
        df_comp['count'] = 1
        df_comp['win_count'] = df_comp['profit'].apply(lambda x: 1 if x > 0 else np.nan)
        df_comp_q = df_comp.groupby('quarter', as_index=False).agg(
            {'profit': 'sum', 'win_count': 'count', 'count': 'count'})

        win_deal = df_comp[df_comp['profit'] > 0].shape[0] / df_comp.shape[0]
        win_quarter = df_comp_q[df_comp_q['win_count'] / df_comp_q['count'] > 0.5].shape[0] / df_comp_q.shape[0]

        set_ticker = list(set([d['ticker'] for d in completed_deals]))
        set_quarter_ticker = list(set([(d['ticker'], d['quarter_fr']) for d in completed_deals]))

        return {
            'match_deals': len(completed_deals),
            'total_time': total_time,
            'time_in_market': time_in_market,
            'profit': profit / len(completed_deals),
            'cash_profit': ((available_assets / initial_assets) - 1) * 100,
            'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
            'available_assets': available_assets,
            'utilization': utilization,
            'win_deal': win_deal * 100,
            'win_quarter': win_quarter * 100,
            'set_ticker': set_ticker,
            'set_quarter_ticker': set_quarter_ticker,
            'completed_deals': pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame(),
            'skip_deals': pd.concat(skip_deals, axis=1).T if skip_deals else pd.DataFrame(),
            'detail_log': '\n'.join(log)
        }

    def get_detail(self, s_type):
        np.random.seed(1)
        res = self.simulate_details(self.df_deals, self.df_buffers, self.start_date, self.end_date, self.initial_assets,
                                    self.max_deals, si_type=s_type)
        return res


class q():
    def __init__(self):
        pass

    def get(self):
        return None

    def put(self, a):
        pass

    def empty(self):
        return True


@memory.cache
def block_by_index():
    def _filter_and_group(df, col='P3M', threshold=20.0, comparison="greater"):
        try:
            condition = f"{col} >= {threshold}" if comparison == "greater" else f"{col} <= {threshold}"
            df_filtered = df.query(condition).copy()

            df_filtered['s_time'] = pd.to_datetime(df_filtered['time'])
            df_filtered['month'] = pd.to_datetime(df_filtered['s_time'].dt.strftime('%Y-%m'))
            df_filtered['type'] = comparison

            df_filtered['month_diff'] = round(df_filtered['month'].diff().dt.days / 31).fillna(0)
            df_filtered['group'] = (df_filtered['month_diff'] > 1).cumsum()

            grouped = df_filtered.groupby('group')
            df_filtered['start_group'] = grouped['s_time'].transform('first')
            df_filtered['end_group'] = grouped['s_time'].transform('last') + pd.Timedelta(days=90)
            df_filtered['mean_profit'] = grouped[col].transform('mean')
            df_filtered['max_profit'] = grouped[col].transform('max')
            df_filtered['min_profit'] = grouped[col].transform('min')

            df_filtered = df_filtered.drop_duplicates(subset=['group'], keep='first')

            df_filtered['group_diff'] = round(
                (df_filtered['start_group'] - df_filtered['end_group'].shift(1)).dt.days / 31).fillna(0)
            df_filtered['group'] = (df_filtered['group_diff'] > 1).cumsum()

            grouped_final = df_filtered.groupby('group')
            df_filtered['start_group'] = grouped_final['start_group'].transform('first')
            df_filtered['end_group'] = grouped_final['end_group'].transform('last')
            df_filtered['mean_profit'] = grouped_final['mean_profit'].transform('mean')
            df_filtered['max_profit'] = grouped_final['max_profit'].transform('max')
            df_filtered['min_profit'] = grouped_final['min_profit'].transform('min')

            df_filtered['start_group_q'] = df_filtered['start_group'].dt.to_period('Q').astype(str)
            df_filtered['end_group_q'] = df_filtered['end_group'].dt.to_period('Q').astype(str)

            return df_filtered.drop_duplicates(subset=['group'], keep='first')[
                ['start_group', 'end_group', 'start_group_q', 'end_group_q', 'type']
            ]
        except Exception:
            return None

    p_90 = 22.65
    p_10 = -14.3

    df_vnindex = pd.read_csv(f'{FPATH}/VNINDEX.csv')
    df_vnindex['P3M'] = 100 * (df_vnindex['O3M'] - 1)

    df_up = _filter_and_group(df_vnindex, col='P3M', threshold=p_90, comparison="greater")
    df_down = _filter_and_group(df_vnindex, col='P3M', threshold=p_10, comparison="less")

    return pd.concat([df_up, df_down], ignore_index=True)


if __name__ == "__main__":
    import os
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/webui", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)
    #
    FPATH = 'ticker_v1a'
    # ticker = 'TTA'
    # pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')

    filter = {
        "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2025-01-01') & (Price > 10000)",
        "_VolMax5Y": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
        "_VolMax5Y1": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 2*MA200) & (NP_P0 < 0.8*NP_P1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        "$VolMax5Y": "MA2, MA3, MA4, S13, SellTest, SellMaxVol",
        "$VolMax5Y1": "MA2, MA3, MA4, S13, SellTest, SellMaxVol, Exit1",
        "exit_under_profit_1m": "0.07",
        "~SellResistance": "{Init} &  (Close /Close_T1 < 1)  & (Close  <  0.96*Res_1Y) & (Volume > Volume_3M_P50) & (Close_T1/LO_3M_T1 > 1.35)",
        "#Exit1": "exit_under_profit('1M', 0.03)",

    }
    # eval_ticker = TickerEval(ticker, pdxx, filter, cutloss=0.15)
    # res_d = eval_ticker.eval_by_deal()
    # res_h = eval_ticker.eval_by_hit()
    # res_b = eval_ticker.get_deal_full(res_d)
    # res = eval_ticker.eval_short_sell()
    #
    # pd_deal = pd.read_csv('webui/pd_deal.csv').reset_index(drop=True)
    # c = '(BKMA200, VolMax5Y ) & (BKMA200, UnderBV)'
    # simulation = Simulation(df_deals=pd_deal, start_date='2007-02-02', initial_assets=1e8, max_deals=10, combine=c)
    import time

    #
    # now = time.time()
    # result = simulation.run(1, s_type='full_cash')
    # print(time.time() - now)
    # print(result)
    # now = time.time()
    # result1 = simulation.run_fast(10, s_type='adjust_cash')
    # result1 = simulation.run(2, s_type='full_cash')
    # print(time.time() - now)
    # print(result1)
    # print(time.time() - now)
    # now = time.time()
    # result = simulation.run_fast(2)
    # print(result)
    # print(time.time() - now)
