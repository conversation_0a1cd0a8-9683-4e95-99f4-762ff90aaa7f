import os
import sys

import streamlit as st

from webui.plot_service import PlotService
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/webui", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)
st.title("Trading Log Visualization Dashboard")

path = 'webui/'
# Tìm file log mới nhất
log_files = [path + f for f in os.listdir(path) if f.endswith('.jsonl')]
if not log_files:
    st.error("Không tìm thấy file log nào!")

latest_log = max(log_files, key=os.path.getctime)

# Khởi tạo PlotService
plot_service = PlotService(latest_log)

# Hiển thị thống kê tổng quan
st.header("Trading Statistics")
stats = plot_service.get_trade_statistics()

col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric("Total Deals", stats['total_trades'])
    st.metric("Win Rate", f"{stats['win_rate']:.2f}%")
with col2:
    st.metric("Winning Trades", stats['winning_trades'])
    st.metric("Losing Trades", stats['losing_trades'])
with col3:
    st.metric("Avg Profit", f"{stats['avg_profit']:.2f}%")
    st.metric("Max Profit", f"{stats['max_profit']:.2f}%")
with col4:
    st.metric("Min Profit", f"{stats['min_profit']:.2f}%")
    st.metric("Avg Holding Period", f"{stats['avg_holding_period']:.1f} days")

# Hiển thị biểu đồ tổng hợp
st.header("Trading Summary")
st.plotly_chart(plot_service.plot_trade_summary(), use_container_width=True)

# Hiển thị các biểu đồ chi tiết
st.header("Detailed Analysis")
tab1, tab2 = st.tabs([
    "Profit Distribution",
    "Holding Period"
])

with tab1:
    st.plotly_chart(plot_service.plot_profit_distribution(), use_container_width=True)

with tab2:
    st.plotly_chart(plot_service.plot_holding_period_distribution(), use_container_width=True)
