+++++
class Ticker_Evaluate(pd_ticker, dictPattern, weights):
    """
    processing for only a stock
    """
    def find_all_signal(pd_ticker, dictPattern, weights, threshold_buy, threshold_sell)
        """
        Find all signals and process them to find buy and sell dataframe based on weight
        return:
            df_buy: Dataframe{ticker, time, weight, Open_1D}
            df_sell: Dataframe{ticker, time, weight, Open_1D}
        """

        all_signal = [{ticker, time, weight, Open_1D}]
        # Find all data signals
        for pattern in dictPattern:
            all_signal.append(query all pattern signals)

        # concat all_signal to a dataframe and group by ['ticker', 'time', 'weight']

        df = all_signal.groupby(['ticker', 'time', 'weight'])

        # find df_buy, df_sell by weight
        df_buy = df where df[weight] > threshold_buy and sort by date
        df_sell = df where df[weight] < threshold_sell and sort by date

        return df,buy, df_sell

    def find_hits(df_buy, df_sell)
        """
            Find a pair of buy and sell
            The sell signals includes (sell signal by filter , cutloss, hold)
            input:
                df_buy, df_sell: from find_all_signal funcion
                df_buy: Dataframe{ticker, time, weight, Open_1D} sorted by date
                df_sell: Dataframe{ticker, time, weight, Open_1D} sorted by date
            return:
                result: Dataframe({ticker, buy_time, sell_time, profit, weight}) same length as df_buy
        """
        hits = [ticker, buy_time, sell_time, profit, weight]
        for buy in df_buy:
            for sell in df_sell:
                # check T + 3
                # check cutloss
                # check hold
                # check sell

            append(ticker, buy_time=buy[time], sell_time=sell[time], profit= sell[Open_1D] - buy[Open_1D], weight=buy[weight] to hits

        result = pd.concat(hits, axis=1)
        return result

++++
class Simulation:
    def sort_and_shuffle(df_pairs):
        """
        Sort data by date and shuffle each hit in a date with weight
        input:
            df_pairs (all buy-sell pairs of whole ticker): Dataframe [{ticker, buy_time, sell_time, profit, weight}]
        return:
            df_pairs: Sorted (by date) and Shuffled (weight in a date) -> Dataframe[{ticker, buy_time, sell_time, profit, weight}]
        """
        df_list = []
        sort df_pairs by date
        # Shuffle data a date by weight
        for d in days:
            sort and shuffle weight
            append to df_list

        # Concat df_list to Dataframe and return
        return df_result

    def simulate(df_pairs, initial_assets, n_slots):
        """
        input:
            df_pairs: all sell-buy pair of whole tickers. It sorted by date and ranked by weight (from Simulation.sort_and_shuffle) : Dataframe[{ticker, buy_time, sell_time, profit, weight}]
            initial_assets: 100m
            n_slots: 10
        return:
            si_return
        """
        active_hits = []
        completed_hits = []

        for pair_hit in df_pairs:
            today = pair_hit[buy_time]
        
            # Check dupplicate ticker in active_hits
            if ticker in current hold ticker:
                continue

            # Check sell
            for active_hit in active_hits:
                if active_hit[sell_time] is lower than today -> complete hit:
                    # Complete active_hit
                    append active_hit to completed_hits
                    remove active_hit from active_hits
              
            # Check the budget for buying a new hit
            if number of active_hits is lower than n_slot:
                # add pair_hit to active_hits


        return si_return based on completed_hits


++++
threshold_buy
threshold_sell
weights
dictPattern

def optimize(all_tickers, weights, threshold_buy, threshold_sell):
    """
    Step 1: Prepare data for simulation (Ticker_Evaluate class)
        1.1: Loop all_ticker to get ticker_pair
        1.2: Concat all ticker_pair to Dataframe

    Step 2: Process data and simulation (Simulation class)
        2.1: Sort prepared data in step 1 by date and and shuffle weight (shuffle data in each date by weight if There are equal-weight)
        2.2: Feed data to Simulation
    """
    # Step 1:
    pairs = [Dataframe{ticker, time, weight, Open_1D}]
    # 1.1 loop all_tickers to find buy,sell pairs each ticker
    for ticker in all_tickers:
        Load dataframe of ticker -> pd_ticker

        # call Ticker_Evaluate.find_hits function
        df_buy, df_sell = Ticker_Evaluate.find_all_signal(pd_ticker, dictPattern, weights, threshold_buy, threshold_sell)
        ticker_pair = Ticker_Evaluate.find_hits(df_buy, df_sell)

        append ticker_pair({ticker, buy_time, sell_time, profit, weight}) to pairs
    # 1.2
    df_pairs = pd.concat(pairs)

    # Step 2:
    # 2.1: Sort df_pair by date and and shuffle weight (shuffle data in each date by weight if There are equal-weight). Using
        Simulation.sort_and_shuffle funcion
    df_pairs =  Simulation.sort_and_shuffle(df_pairs)

    # 2.2:
    si_retun = Simulation.simulate(df_pairs=df_pairs , initial_assets=100m, n_slots=10)
