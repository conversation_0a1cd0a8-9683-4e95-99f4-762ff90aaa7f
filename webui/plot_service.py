import json
import os

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import plotly.subplots as sp
from collections import defaultdict
import heapq
from core_utils.constant import FPATH


class PlotService:
    def __init__(self, log_data):
        """
        Khởi tạo PlotService với đường dẫn file log
        
        Args:
            log_file_path (str): Đường dẫn đến file log.jsonl
        """
        # self.log_file_path = log_file_path
        self.df = self.load_logs(log_data)

    @staticmethod
    def load_logs(log_data_str: str):
        """
        Parse chuỗi JSONL (nhiều dòng JSON) thành DataFrame.
        - Mỗi dòng phải là JSON hợp lệ (double quotes).
        - Nếu có active_deals thì chỉ lấy số lượng.
        """
        logs = []
        for line in log_data_str.strip().split('\n'):
            if not line.strip():
                continue
            try:
                log = json.loads(line)
                if 'active_deals' in log:
                    log['total_active_deals'] = len(log['active_deals'])

                    # Sum investments by ticker
                    ticker_investments_ratio = defaultdict(float)
                    unique_tickers = set()
                    for deal in log['active_deals']:
                        ticker = deal.get('ticker')
                        unique_tickers.add(ticker)
                        investment = deal.get('investment_ratio', 0.0)
                        if ticker:
                            ticker_investments_ratio[ticker] += investment

                    # Sort by total investment and take top 10
                    top_10 = heapq.nlargest(10, ticker_investments_ratio.items(), key=lambda x: x[1])

                    # Recreate active_deals with summed investments
                    log['active_deals'] = [{'ticker': ticker, 'investment_ratio': investment} for ticker, investment in
                                           top_10]
                    log['total_active_tickers'] = len(unique_tickers)

                logs.append(log)
            except json.JSONDecodeError as e:
                raise ValueError(f"Lỗi khi đọc JSON dòng: {line}\nChi tiết: {e}")

        df = pd.DataFrame(logs)
        if not df.empty and 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df['quarter'] = pd.PeriodIndex(df['date'], freq='Q').astype(str)
        return df

    @staticmethod
    def load_vn_index():
        df = pd.read_csv(f'{FPATH}/VNINDEX.csv')
        df['date'] = pd.to_datetime(df['time'])
        df['vnindex'] = df['Close']
        return df

    @staticmethod
    def quarter_report(str_time):
        year, month, day = str_time.split('-')
        time = int(month) * 100 + int(day)
        if time <= 131:  # Start year - Jan 30th -> move after 1 day
            key = f'{int(year) - 1}Q3'
        elif 131 < time <= 431:  # Feb 1st - April 31st -> move after 1 day
            key = f'{int(year) - 1}Q4'
        elif 431 < time <= 731:  # May 1st - July 31st -> move after 1 day
            key = f"{year}Q1"
        elif 731 < time <= 1031:  # Aug 1st -October 31st -> move after 1 day
            key = f"{year}Q2"
        else:  # Nov 1st - End year
            key = f"{year}Q3"
        return key

    def plot_cash_utilization(self):
        """
        Vẽ biểu đồ tỷ lệ sử dụng vốn theo thời gian
        """
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=self.df['date'],
            y=self.df['utilization'] * 100,
            mode='lines',
            name='Cash Utilization'
        ))

        fig.update_layout(
            title='Cash Utilization Over Time',
            xaxis_title='Date',
            yaxis_title='Utilization (%)',
            hovermode='x unified'
        )

        return fig

    def plot_trading_volume(self):
        """
        Vẽ biểu đồ khối lượng giao dịch theo thời gian
        """
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=self.df['date'],
            y=self.df['daily_trading_volume'],
            name='Trading Volume'
        ))

        fig.update_layout(
            title='Daily Trading Volume',
            xaxis_title='Date',
            yaxis_title='Volume',
            hovermode='x unified'
        )

        return fig

    def plot_profit_distribution(self):
        """
        Vẽ biểu đồ phân phối lợi nhuận cho các giao dịch bán
        """
        sell_df = self.df[self.df['action'] == 'Sell']

        fig = go.Figure()
        fig.add_trace(go.Histogram(
            x=sell_df['profit_percentage'],
            nbinsx=500,
            name='Profit Distribution'
        ))

        fig.update_layout(
            title='Profit Distribution for Sell Trades',
            xaxis_title='Profit Percentage (%)',
            yaxis_title='Count',
            hovermode='x unified'
        )

        return fig

    def plot_holding_period_distribution(self):
        """
        Vẽ biểu đồ phân phối thời gian nắm giữ
        """
        sell_df = self.df[self.df['action'] == 'Sell']

        fig = go.Figure()
        fig.add_trace(go.Histogram(
            x=sell_df['holding_days'],
            nbinsx=500,
            name='Holding Period'
        ))

        fig.update_layout(
            title='Holding Period Distribution',
            xaxis_title='Holding Days',
            yaxis_title='Count',
            hovermode='x unified'
        )

        return fig

    def plot_trade_summary(self):
        """
        Vẽ biểu đồ tổng hợp các thông số quan trọng
        """
        # Tạo subplot với 3 biểu đồ
        fig = sp.make_subplots(
            rows=8, cols=1,
            subplot_titles=(
                'VNINDEX', 'Total Assets', 'Available Cash', 'Cash Utilization', 'Asset Growth Rate/Profit Quarter',
                'Win Rate Quarter', 'Total Tickers/Deals', 'Buy/Sell Count'),
            vertical_spacing=0.03,
            shared_xaxes=True
        )

        df = self.df.copy()

        # Handle for each deal
        df_deal_b = df[df['action'] == 'Buy'].copy().dropna(axis=1, how='all')
        df_deal_b['date'] = pd.to_datetime(df_deal_b['date'])
        df_deal_s = df[df['action'] == 'Sell'].copy()[
            ['ticker', 'buy_date', 'holding_days', 'profit_percentage']].rename(columns={'buy_date': 'date'})
        df_deal_s['date'] = pd.to_datetime(df_deal_s['date'])
        df_deal = df_deal_b.merge(df_deal_s, on=['ticker', 'date'], how='left')

        # Handle group for data
        df_date = df.groupby('date', as_index=False).agg({
            'total_assets': 'last',
            'utilization': 'last',
            'total_active_deals': 'last',
            'total_active_tickers': 'last',
            'active_deals': 'last'
        })

        # VNINDEX
        vnindex = self.load_vn_index()
        df_date['vnindex'] = df_date.merge(vnindex[['date', 'vnindex']], on='date', how='left')['vnindex']
        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['vnindex'], name='VNINDEX'),
            row=1, col=1
        )

        # Total Assets
        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['total_assets'], name='Total Assets'),
            row=2, col=1
        )

        # Available Cash
        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['total_assets'] * (1 - df_date['utilization']),
                       name='Available Cash'),
            row=3, col=1,

        )

        # Cash Utilization
        df_date['hover_utilize_details'] = df_date['active_deals'].apply(
            lambda deals: "<br>".join(
                [f"{d['ticker']}: {d['investment_ratio']:.3}%" for d in
                 deals]) if deals else "No deals"
        )
        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['utilization'] * 100, name='Cash Utilization',
                       customdata=df_date[['hover_utilize_details']],
                       hovertemplate=
                       'Date: %{x}<br>' +
                       'Utilize: %{y:.3f}%<br>' +
                       '%{customdata[0]}<extra></extra>'),
            row=4, col=1
        )

        # Quarter profit
        df_quarter = df_deal.copy()
        df_quarter['count_win'] = (df_quarter['profit_percentage'] > 0).astype(int)
        df_quarter['vnindex'] = df_quarter.merge(vnindex[['date', 'vnindex']], on='date', how='left')['vnindex']

        df_quarter = df_quarter.groupby(['quarter'], as_index=False).agg({
            'profit_percentage': 'mean',
            'count_win': 'mean',
            'total_assets': 'last',
            'vnindex': 'last',
            'date': 'last'
        }).rename(columns={'profit_percentage': 'quarter_profit', 'count_win': 'win_quarter',
                           'total_assets': 'asset_growth_rate', 'vnindex': 'vnindex_growth_rate'})
        df_quarter['growth_asset_rate'] = (df_quarter['asset_growth_rate'] - df_quarter['asset_growth_rate'].shift(1)) / \
                                          df_quarter['asset_growth_rate'].shift(1)
        df_quarter['growth_vnindex_rate'] = (df_quarter['vnindex_growth_rate'] - df_quarter[
            'vnindex_growth_rate'].shift(1)) / df_quarter['vnindex_growth_rate'].shift(1)

        df_deal = df_deal.merge(df_quarter[['quarter', 'quarter_profit', 'win_quarter']], on='quarter', how='left')

        fig.add_trace(
            go.Scatter(x=df_quarter['date'], y=df_quarter['growth_asset_rate'] * 100, name='Asset Growth Rate',
                       mode='lines+markers',
                       customdata=df_quarter[['quarter']],
                       hovertemplate=
                       'Quarter: %{customdata[0]}<br>' +
                       'Asset Growth: %{y:.1f}%' + '<extra></extra>'),
            row=5, col=1
        )

        fig.add_trace(
            go.Scatter(x=df_quarter['date'], y=df_quarter['growth_vnindex_rate'] * 100, name='VNI Growth Rate',
                       mode='lines+markers',
                       customdata=df_quarter[['quarter']],
                       hovertemplate=
                       'Quarter: %{customdata[0]}<br>' +
                       'VNI Growth: %{y:.1f}%' + '<extra></extra>'),
            row=5, col=1
        )

        fig.add_trace(
            go.Scatter(x=df_deal['date'], y=df_deal['quarter_profit'], name='Profit Quarter', fill='tozeroy',
                       mode='none',
                       customdata=df_deal[['quarter']],
                       hovertemplate=
                       'Quarter: %{customdata[0]}<br>' +
                       'Profit: %{y:.2f}%' + '<extra></extra>'),
            row=5, col=1,
        )

        fig.add_trace(
            go.Scatter(x=df_deal['date'], y=df_deal['win_quarter'] * 100, name='Win Quarter',
                       customdata=df_deal[['quarter']],
                       hovertemplate=
                       'Quarter: %{customdata[0]}<br>' +
                       'Win Rate: %{y:.1f}%' + '<extra></extra>'),
            row=6, col=1
        )

        fig.add_shape(
            type="line",
            x0=0,
            x1=1,
            y0=50,
            y1=50,
            xref="x6 domain",
            yref="y6",
            line=dict(color="red", width=1, dash="dash")
        )

        # Buy/Sell count
        action_counts = self.df.groupby(['date', 'action']).size().unstack(fill_value=0)

        # Add total actions
        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['total_active_deals'], name='Total Deals'),
            row=7, col=1
        )

        fig.add_trace(
            go.Scatter(x=df_date['date'], y=df_date['total_active_tickers'], name='Total Tickers'),
            row=7, col=1
        )

        # Add buy/sell actions
        fig.add_trace(
            go.Scatter(x=action_counts.index, y=action_counts['Buy'], name='Buy Actions',
                       mode='markers', marker=dict(color='Green', size=2)),
            row=8, col=1,
        )

        fig.add_trace(
            go.Scatter(x=action_counts.index, y=-action_counts['Sell'], name='Sell Actions',
                       mode='markers', marker=dict(color='Red', size=2)),
            row=8, col=1
        )

        fig.add_trace(
            go.Scatter(x=action_counts.index, y=action_counts['Buy'] - action_counts['Sell'], name='Net Deals'),
            row=8, col=1
        )

        fig.update_yaxes(autorange=True)
        fig.update_layout(
            height=1500,
            title_text="Trading Summary Dashboard",
            showlegend=True,
            barmode='group'

        )
        return fig

    def get_trade_statistics(self):
        """
        Tính toán và trả về các thống kê quan trọng về giao dịch
        """
        sell_df = self.df[self.df['action'] == 'Sell']

        stats = {
            'total_trades': len(sell_df),
            'winning_trades': len(sell_df[sell_df['profit_percentage'] > 0]),
            'losing_trades': len(sell_df[sell_df['profit_percentage'] <= 0]),
            'avg_profit': sell_df['profit_percentage'].mean(),
            'max_profit': sell_df['profit_percentage'].max(),
            'min_profit': sell_df['profit_percentage'].min(),
            'avg_holding_period': sell_df['holding_days'].mean(),
            'max_holding_period': sell_df['holding_days'].max(),
            'min_holding_period': sell_df['holding_days'].min(),
            'win_rate': len(sell_df[sell_df['profit_percentage'] > 0]) / len(sell_df) * 100
        }

        return stats


def main():
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/webui", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    path = 'webui/'
    log_files = [path + f for f in os.listdir(path) if f.endswith('.jsonl')]

    latest_log = max(log_files, key=os.path.getctime)
    with open(latest_log, 'r') as f:
        data = f.readlines()
        data_str = "".join(data)
    plot_service = PlotService(data_str)

    stats = plot_service.plot_trade_summary()

#
# main()
