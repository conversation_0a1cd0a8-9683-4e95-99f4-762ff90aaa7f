import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime
import plotly.subplots as sp


# Function to load CSV data
def load_data(fname,fpath='../ticker/'):
    df = pd.read_csv(fpath+fname.replace('.csv','').replace('*','')+'.csv',dtype={'time':str,'CMB signal':str}).query('time>="2016"')
    df['ymd'] = pd.to_datetime(df['time'])
    print(df.head())
    return df

# Function to plot candlestick and RSI
def plot_stock_data(df):
    fig = sp.make_subplots(rows=5, cols=1, shared_xaxes=True, vertical_spacing=0.05)

    # Candlestick subplot
    candlestick = go.Candlestick(x=df['ymd'],
                                  open=df['Open'],
                                  high=df['High'],
                                  low=df['Low'],
                                  close=df['Close'],
                                  name='Candlestick')

    fig.add_trace(candlestick, row=1, col=1)
    fig.update_yaxes(fixedrange=False)
    fig.update_layout(xaxis_rangeslider_visible=False)
    # Adjust y-axis range for Candlestick subplot based on x_range

    df['buy signal'] = ((df['CMB signal'].notnull())&(df['CMB signal']!='None')).astype(int)
    print(df[['ymd','CMB signal','buy signal']].tail())
    # RSI subplot
    fig.add_trace(go.Scatter(x=df['ymd'], y=df['RSI'], mode='lines', name='RSI'), row=2, col=1)
    fig.add_trace(go.Scatter(x=df['ymd'], y=df['buy signal'], mode='lines', name='Buy'), row=2, col=1)
    fig.update_yaxes(range=[0, 1], row=2, col=1)

    # CMB daily
    if 0:
        cmb_scatter = go.Scatter(x=df['ymd'], y=df['CMB'], mode='lines', name='CMB')
        fig.add_trace(cmb_scatter, row=3, col=1)
        fig.add_trace(go.Scatter(x=df['ymd'], y=df['CMB_Fast'], mode='lines', name='CMB_Fast'), row=3, col=1)
        fig.add_trace(go.Scatter(x=df['ymd'], y=df['CMB_Slow'], mode='lines', name='CMB_Slow'), row=3, col=1)
        fig.update_yaxes(range=[-1, 1.5], row=3, col=1)
#    rsi_scatter.update_yaxes(range=[-2, 2], row=2, col=1)

    DASH = {'CMB Fast':'dash','CMB Slow':'dot',
            'W-CMB Fast':'dash','W-CMB Slow':'dot',
            'M-CMB Fast':'dash','M-CMB Slow':'dot',
            }
    DASH = {}
    OPACITY = {'CMB Fast':0.8,'CMB Slow':0.6,
                    'W-CMB Fast':0.8,'W-CMB Slow':0.6,
                    'M-CMB Fast':0.8,'M-CMB Slow':0.6,
                    }
    COLOR = {'CMB':'rgba(128, 0, 0, .8)','CMB Fast':'rgba(128, 0, 0, 0.5)','CMB Slow':'rgba(128, 0, 0, 0.2)',
                'W-CMB':'rgba(0, 128, 0, .8)','W-CMB Fast':'rgba(0, 128, 0, 0.5)','W-CMB Slow':'rgba(0, 128, 0, 0.2)',
                'M-CMB':'rgba(0, 0, 128, .8)','M-CMB Fast':'rgba(0, 0, 128, 0.5)','M-CMB Slow':'rgba(0, 0, 128, 0.2)',
                }
    if 1:
        # CMB daily
        for col in ['CMB','CMB Fast','CMB Slow']:
            ii = df[col]==df[col]
            fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                        line=dict(dash=DASH.get(col,None),  # Set the line style to dashed
                                                width=2,
                                                color=COLOR.get(col,None), 
    #                                               opacity=OPACITY.get(col,1)
                                                )), row=3, col=1)
        fig.update_yaxes(range=[-1, 2], row=3, col=1)
    # CMB weekly
    for col in ['W-CMB','W-CMB Fast','W-CMB Slow']:
        ii = df[col]==df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                    line=dict(dash=DASH.get(col,None),  # Set the line style to dashed
                                            width=2,
                                            color=COLOR.get(col,None), 
#                                               opacity=OPACITY.get(col,1)
                                            )), row=4, col=1)
    pdxx = df[df['W-CMB-prevymd'].notnull()].copy()
    print("pdxx",pdxx.head())
#    print(pdxx[['ymd','W-CMB','W-CMB-prevymd','W-CMB-prevval']])
    for i in range(pdxx.shape[0]):
        x2,x1 = pdxx.iloc[i]['ymd'],pd.to_datetime(pdxx.iloc[i]['W-CMB-prevymd'])
        y2,y1 = pdxx.iloc[i]['W-CMB'],pdxx.iloc[i]['W-CMB-prevval']
        pdyy = pd.DataFrame({'ymd':[x1,x2],'trend':[y1,y2]})
 #       print(pdyy)
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False ,
                                    line=dict(color='rgba(128, 0, 0, .5)' )), row=4, col=1)

    fig.update_yaxes(range=[-1, 2], row=4, col=1)

    # CMB monthly
    for col in ['M-CMB','M-CMB Fast','M-CMB Slow']:
        ii = df[col]==df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,line=dict(dash=DASH.get(col,None),  # Set the line style to dashed
                                            width=2,
                                            color=COLOR.get(col,None), 
#                                               opacity=OPACITY.get(col,1)
                                            )), row=5, col=1)
    pdxx = df[df['M-CMB-prevymd'].notnull()].copy()
#    print(pdxx[['ymd','M-CMB','M-CMB-prevymd','M-CMB-prevval']])
    for i in range(pdxx.shape[0]):
        x2,x1 = pdxx.iloc[i]['ymd'],pd.to_datetime(pdxx.iloc[i]['M-CMB-prevymd'])
        y2,y1 = pdxx.iloc[i]['M-CMB'],pdxx.iloc[i]['M-CMB-prevval']
        pdyy = pd.DataFrame({'ymd':[x1,x2],'trend':[y1,y2]})
#        print(pdyy)
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False ,
                                    line=dict(color='rgba(128, 0, 0, .5)' )), row=5, col=1)
    fig.update_yaxes(range=[-1, 2], row=5, col=1)

#    fig.add_trace(rsi_scatter, row=2, col=1)
    fig.update_xaxes(showgrid=True, row=1, col=1)
    fig.update_xaxes(showgrid=True, row=2, col=1)
    fig.update_xaxes(showgrid=True, row=3, col=1)
    fig.update_xaxes(showgrid=True, row=4, col=1)
    fig.update_xaxes(showgrid=True, row=5, col=1)

    fig.update_layout(title='CMB',
                      xaxis_title='Date',
                      yaxis_title='Value',
                      height=1000,
                      width=1200,
                      )

    return fig

import numpy as np
# Main Streamlit app
def main():
    # Set the page configuration
    st.set_page_config(
        page_title="Wide Streamlit App",
        page_icon=":chart_with_upwards_trend:",
        layout="wide",  # Use the "wide" layout option
        initial_sidebar_state="collapsed"  # You can set the initial state of the sidebar
    )

    st.title("TA")

    # list of stock files
    import os
    stock_files = os.listdir('../ticker/')
    stock_files = [f[:-4] for f in stock_files if f.endswith('.csv')]

    # User input for selecting stock file
    stock_file = st.selectbox("Select a stock file:", stock_files)

    # Load data and plot visualization
    df = load_data(stock_file)
    print(stock_file)
    st.write(f"Displaying data for {stock_file.split('.')[0]}")
    st.plotly_chart(plot_stock_data(df))

if __name__ == "__main__":
    main()

