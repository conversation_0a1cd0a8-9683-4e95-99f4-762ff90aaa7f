from tdlib import *
# from datetime import datetime

# exec(open('talib.py').read())
# exec(open('tdlib.py').read())
from pathos.multiprocessing import ProcessingPool as Pool
import datetime

def multi_process(func, list_input, num_proc=7):
    with Pool(num_proc) as p:
        output = p.map(func, list_input)
    return output

def __main__(argv):
    if len(argv) < 2:
        ymd = datetime.datetime.today().strftime('%Y-%m-%d')
        # ymd = "2024-05-30"
    else:
        ymd = argv[1]
    if len(ymd) != 10:
        print("ymd={} , not formated correctly -> skipped update".format(ymd))
        return
    else:
        tic("update data " + str(ymd))
        overwrite = False
        if ymd.split('-')[-1] == '16' or ymd.split('-')[-1] == '01':  # update Fundamental analysis data at 16th every month
            overwrite = True

        update_data(ymd, overwrite=overwrite, START_DATE="2007-01-01", max_retry=1)
        toc('process ticker_v1a')
        # Process price data into pdxy
        list_downloaded_ticker = [f.replace('.csv', '') for f in os.listdir('../data/daily_latest/') if
                                  f.endswith('.csv')]
        pdidx = get_pdidx()
        def process_ticker_v1a(x):
            return process_ticker_v1(x, out_folder='../ticker_v1a', options={"len": 5}, pdidx=pdidx)

        if False:
            list_output = multi_process(process_ticker_v1, list_downloaded_ticker, num_proc=16)
        else:
            list_output = multi_process(process_ticker_v1a, list_downloaded_ticker, num_proc=16)
        toc("done")
    print("ymd", ymd)


import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)
sys.path.insert(0, current_dir)
__main__(sys.argv)
