"""
# Buy/Sell signal
Tín hiệu mua:
+ Khi giá cổ phiếu giảm so với đỉnh khoảng 50%.
1) Nếu giá giảm mạnh, giảm sàn liên tục, mất thanh khoản thì quan sát chỉ báo Composite index:
a. Cực mạnh:
+ Composite Index khung tháng âm.
+ Composite Index khung tuần bắt đầu uốn cong lên, giá cổ phiếu vẫn tiếp tục giảm.
+ Composite Index khung ngày cắt qua đường trung bình 13 ngày của chính nó.
⇨ Theo dõi kỹ có thể mua được giá sàn: Lợi nhuận kỳ vọng từ 20%-100%.
b. Mạnh:
+ Composite Index khung tháng vẫn ở khoảng giá trị 30-70.
+ Composite Index khung tuần uốn cong lên, giá cổ phiếu vẫn giảm sàn.
+ Composite Index khung ngày cắt qua đường trung bình 13 ngày của chính nó.
⇨ Theo dõi kỹ có thể mua được giá sàn: Lợi nhuận kỳ vọng từ 20%-100%.
2) Nếu giá giảm từ từ, không mất thanh khoản thì quan sát chỉ báo Composite Index:
a. Cực mạnh:
+ Composite Index khung tháng âm tạo đáy thứ nhất sau đó tăng lại và giảm tiếp.
+ Composite Index khung tuần tạo phân kỳ dương trong nhiều tuần (từ 10 tuần trở lên). Composite Index tạo đáy thứ 2 cao hơn hoặc bằng đáy 1 nhưng giá tạo đáy 2 thấp hơn đáy 1 hoặc ngược lại.
+ Composite Index khung ngày phân kỳ dương.
⇨ Mua khi thấy giá tăng + khối lượng tăng mạnh.
b. Mạnh:
+ Composite Index khung tháng âm
+ Composite Index khung tuần tạo phân kỳ dương trong nhiều tuần (từ 10 tuần trở lên). Composite Index tạo đáy thứ 2 cao hơn hoặc bằng đáy 1 nhưng giá tạo đáy 2 thấp hơn đáy 1 hoặc ngược lại.
+ Composite Index khung ngày phân kỳ dương.
⇨ Mua khi thấy giá tăng + khối lượng tăng mạnh.
Dấu hiệu phân kỳ dương này nếu xảy ra ở vùng hỗ trợ mạnh trong lịch sử, vùng giá giao dịch nhiều ngày hoặc vùng có khối lượng vô cùng lớn thì càng hiệu quả.
Tín hiệu bán:
Khi giá cổ phiếu tăng mạnh, 2-10 lần. Giá càng tăng mạnh, phân kỳ càng hiệu quả.
1) Nếu giá tăng mạnh, tăng trần liên tục, thanh khoản thấp thì quan sát chỉ báo Composite index:
c. Cực mạnh:
+ Composite Index khung tháng vượt 100
+ Composite Index khung tuần bắt đầu uốn cong xuống, giá cổ phiếu vẫn tiếp tục tăng.
+ Composite Index khung ngày cắt qua đường trung bình 13 ngày của chính nó.
⇨ Bán giá trần.
3) Nếu giá tăng từ từ trong nhiều tháng, không mất thanh khoản thì quan sát chỉ báo Composite Index:
+ Composite Index khung tháng trên 100 tạo đỉnh sau đó giảm và tạo đỉnh kế tiếp thấp hơn đỉnh trước.
+ Composite Index khung tuần tạo phân kỳ âm trong nhiều tuần (từ 10 tuần trở lên). Composite Index tạo đỉnh thứ 2 thấp hơn hoặc bằng đỉnh thứ 1 nhưng giá tạo đỉnh 2 cao hơn đỉnh 1.
+ Composite Index khung ngày phân kỳ âm.
⇨ Bán cây trần. Bán vào phiên khớp lệnh lớn. Bán cây sàn đầu tiên. Bán khi mất trendline.
"""

def legacy_load_amidata():
    def toYMD(s,dt_format="%d/%m/%y %H:%M:%S"):
        if (len(s)<10)&(dt_format=="%d/%m/%y %H:%M:%S"):
            s = s + " 00:00:00"
        return datetime.strptime(s,dt_format).strftime("%Y%m%d")

    data_raw = pd.read_csv('../data/ami.csv',dtype={'Ticker':str,
                                                'Date/Time':str})
    list_ticker = [t for t in data_raw['Ticker'].unique() if len(t)==3 or (t=="VNINDEX")]
    data = data_raw[data_raw['Ticker'].isin(list_ticker)].copy()
    data['ymd']=data['Date/Time'].map(lambda x: toYMD(x,dt_format="%d/%m/%y %H:%M:%S"))
    del data['Date/Time']
    return data

def legacy_process_ami_data():
       data = legacy_load_amidata()
       list_ticker = [t for t in data['Ticker'].unique() if len(t)==3 or (t=="VNINDEX")]
       for ticker in tqdm(list_ticker):
              pdraw = data.query('ticker=={}'.format(ticker))
              pd00 = compute_indicators(resample_data(pdraw,n=1))
              pd01 = compute_indicators(resample_data(pdraw,n=5))
              pd02 = compute_indicators(resample_data(pdraw,n=20))
              pd00a = detect_trend(pd00,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
              pd01a = detect_trend(pd01,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
              pd02a = detect_trend(pd02,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
              col_trend = ['trend','length','prevymd','prevval','peak-T1','peak-T2']
              pd03 = (pd00
              .merge(pd00a[['ymd']+col_trend].rename(columns={c:'CMB-'+c for c in col_trend}),on='ymd',how='left')
              .merge(pd01[['ymd','CMB','CMB-Fast','CMB-Slow']]\
                     .rename(columns={'CMB':'W-CMB','CMB-Fast':'W-CMB-Fast','CMB-Slow':'W-CMB-Slow'}),on='ymd',how='left')
              .merge(pd01a[['ymd']+col_trend].rename(columns={c:'W-CMB-'+c for c in col_trend}),on='ymd',how='left')
              .merge(pd02[['ymd','CMB','CMB-Fast','CMB-Slow']]\
                     .rename(columns={'CMB':'M-CMB','CMB-Fast':'M-CMB-Fast','CMB-Slow':'M-CMB-Slow'}),on='ymd',how='left')
              .merge(pd02a[['ymd']+col_trend].rename(columns={c:'M-CMB-'+c for c in col_trend}),on='ymd',how='left')
                     )
              pd03['CMB signal'] = CMB_signal(pd03).values
              #if(len(pd03['CMB signal'].unique())>1):
              #   display(pd03[pd03['CMB signal']!='None'])
              pd03.to_csv(f'../legacy_ami_ticker/{ticker}.csv',index=False)

def legacy_process_ticker(ticker,cname_time='time',folder_in='../data/2023-08-28/',folder_out='../ticker'):
    pdraw = pd.read_csv(f'{folder_in}/{ticker}.csv',dtype={'time':str}).rename(columns={'high':'High',
    'low':'Low','open':'Open','close':'Close','volume':'Volume'})
    pd00 = compute_indicators(resample_data(pdraw,n=1,cname_time=cname_time))
    pd01 = compute_indicators(resample_data(pdraw,n=5,cname_time=cname_time))
    pd02 = compute_indicators(resample_data(pdraw,n=20,cname_time=cname_time))
    pd00a = detect_trend(pd00,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
    pd01a = detect_trend(pd01,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
    pd02a = detect_trend(pd02,'CMB',max_length=16).query('(length>0)&(((prevval<0)&(trend=="bottom"))|((prevval>0.8)&(trend=="top")))')
    col_trend = ['trend','length','prevymd','prevval','peak-T1','peak-T2']
    pd03 = (pd00
    .merge(pd00a[[cname_time]+col_trend].rename(columns={c:'CMB-'+c for c in col_trend}),on=cname_time,how='left')
    .merge(pd01[[cname_time,'CMB','CMB Fast','CMB Slow']]\
            .rename(columns={'CMB':'W-CMB','CMB Fast':'W-CMB Fast','CMB Slow':'W-CMB Slow'}),on=cname_time,how='left')
    .merge(pd01a[[cname_time]+col_trend].rename(columns={c:'W-CMB-'+c for c in col_trend}),on=cname_time,how='left')
    .merge(pd02[[cname_time,'CMB','CMB Fast','CMB Slow']]\
            .rename(columns={'CMB':'M-CMB','CMB Fast':'M-CMB Fast','CMB Slow':'M-CMB Slow'}),on=cname_time,how='left')
    .merge(pd02a[[cname_time]+col_trend].rename(columns={c:'M-CMB-'+c for c in col_trend}),on=cname_time,how='left')
            )
    pd03['CMB signal'] = CMB_signal(pd03).values
    #if(len(pd03['CMB signal'].unique())>1):
    #   display(pd03[pd03['CMB signal']!='None'])
    # check output folder
    if not os.path.exists(folder_out):
        os.makedirs(folder_out)
    pd03.to_csv(f'{folder_out}/{ticker}.csv',index=False)
    return pd03

def legacy_compute_signal_v1(pdx1,cname_time='time'):
    """ CMB signal 
    # C_H12M < 0.5 - giá cổ phiếu giảm so với đỉnh (12 tháng) khoảng 50%.
    # C_H1W < 0.8 - Giá giảm mạnh, giảm sàn liên tục, mất thanh khoản (20% trong vòng 1 tuần)
    # 1) Nếu giá giảm mạnh, giảm sàn liên tục, mất thanh khoản thì quan sát chỉ báo Composite index:
    # W_CMB_Peak = -1 # Composite Index khung tuần bắt đầu uốn cong lên, giá cổ phiếu vẫn tiếp tục giảm. (!) lag?
    # D_CMB_XFast < 3 , D_CMB > D_CMB_Fast # Composite Index khung ngày cắt qua đường trung bình 13 ngày của chính nó.
    # ⇨ Theo dõi kỹ có thể mua được giá sàn: Lợi nhuận kỳ vọng từ 20%-100%.
    # if M-CMB < 0 -> Buy1X
    # if 0 <= M_CMB < 0.7 -> Buy1S
    # Buy2
    # C_H12M < 0.5 - giá cổ phiếu giảm so với đỉnh (12 tháng) khoảng 50%.
    # C_H1M < 0.9 - giá giảm từ từ, không mất thanh khoản
    # W_CMB_Step > 0 Phân kỳ dương
    # W_CMB_LEN >= 10 Composite Index khung tuần tạo phân kỳ dương trong nhiều tuần (từ 10 tuần trở lên).
    # W_CMB_LAG < 3 phân kỳ vừa được tạo trong vòng 3 periods
    # D_CMB_Step > 0
    # D_CMB_LEN > 5
    # D_CMB_LAG < 5
    #*** M_CMB_peak_T2<0 & M_CMB_peak_T1>M_CMB _> Buy2X Composite Index khung tháng âm tạo đáy thứ nhất sau đó tăng lại và giảm tiếp.
    #*** M_CMB<0 _> Buy2S Composite Index khung tháng âm
    """
    pdxy = pdx1.copy()
    buy1 = ((pdxy['C_H12M']<0.5)&
            (pdxy['C_H1W']<0.8)&
            (pdxy['W_CMB_Peak']==-1)&
            (pdxy['D_CMB_XFast']<3)&(pdxy['D_CMB']>pdxy['D_CMB_Fast']))
    pdxy['B1'] = (buy1 & (pdxy['M_CMB']<0.7)).astype(int) + (buy1 & (pdxy['M_CMB']<0)).astype(int)
    buy2 = ((pdxy['C_H12M']<0.5)&
            (pdxy['C_H1M']<0.9)&
            (pdxy['W_CMB_Step']>0)&(pdxy['W_CMB_LEN']>=10)&(pdxy['W_CMB_LAG']<3)&
            (pdxy['D_CMB_Step']>0)&(pdxy['D_CMB_LEN']>=5)&(pdxy['D_CMB_LAG']<5))
    buy2X = buy2 & (pdxy['M_CMB_Peak_T2']<0) & (pdxy['M_CMB_Peak_T1']>pdxy['M_CMB'])
    buy2S = buy2 & (pdxy['M_CMB']<0)
    pdxy['B2'] = buy2S.astype(int)
    pdxy.loc[buy2X,'B2'] = 2    
    return pdxy[[cname_time,'B1','B2']]

