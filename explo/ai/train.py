# 4. Train XGBOOST
import xgboost as xgb


def train_xgboost(pdx, cname_features, cname_label, cname_tvt,
                  params=None):
    ''' pdx : dataframe
        cname_features : list of column names
        cname_label : column name for label (0/1)
        cname_tvt : column name for train, validation, test ('train'/'val'/'test')
    Output:
        model
    '''
    if params is None:
        params = {'tree_method': 'hist',
                  'max_bin': 256,
                  'max_depth': 7,
                  'min_child_weight': 10,
                  'subsample': .8,
                  'colsample_bytree': .8,
                  'learning_rate': .01,
                  'n_estimators': 1000,
                  'predictor': 'cpu_predictor',
                  'verbosity': 0,
                  'eval_metric': ['error', 'auc'],
                  'early_stopping_rounds': 100,
                  'gamma': .1,
                  'lambda': 1.0,
                  'alpha': 0.5,
                  }
    # split data
    pdx_train = pdx.query(f'{cname_tvt}=="train"')
    pdx_val = pdx.query(f'{cname_tvt}=="val"')

    # train model
    model = xgb.XGBClassifier(**params)
    eval_set = [(pdx_train[cname_features], pdx_train[cname_label]), (pdx_val[cname_features], pdx_val[cname_label])]

    model.fit(pdx_train[cname_features], pdx_train[cname_label], eval_set=eval_set, verbose=params['verbosity'])

    return model

if __name__ == "__main__":

    ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
    cname_label = ['pattern_label']

    cname_features = [column for column in df_map.columns if column not in (ignore_cols + cname_label)]

    model = train_xgboost(df_map, cname_features=cname_features, cname_label='pattern_label', cname_tvt='tvt')
    model.save_model("tuning/sell/model/xgb_model.json")