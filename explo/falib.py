import inspect
import sys
import traceback
from functools import lru_cache
from datetime import datetime

import numpy as np
import pandas as pd


# def capture_stderr(err_description = '', api_path = './config.yaml'):
#     def wrapper(func):
#         def wrapped_args(*args):
#             # Create a StringIO object to capture stderr
#             error_message = ''
#             try:
#                 result = func()
#                 return result
#                 # If there is an error message, you can handle it here
#             except Exception as e:
#                 # Access more detailed information about the exception
#                 err_type = type(e).__name__
#                 err_message = e.args
#                 print(f'Error Type: {err_type}')
#                 print(f'Error Message: {e}')
#                 print("Traceback:")
#                 traceback.print_tb(e.__traceback__)
#                 # Retrieve the last n lines of code from the traceback
#                 stack = traceback.extract_tb(sys.exc_info()[2])
#                 #for filename, line_number, function, line in stack[-1]:
#                 line_number = stack[-1].lineno
#                 source_code = inspect.getsourcelines(inspect.getmodule(inspect.currentframe()))[0]
#                 err_code = source_code[line_number-5: line_number]
#                 message = message_compose(err_type, err_message, err_code, err_description)
#                 reply = chat_with_chatgpt(api_path, message)
#                 print(reply)
#         return wrapped_args
#     return wrapper

# def quarter_report(time):
#     year, month, day = time.split('-')
#     time = int(month) * 100 + int(day)
#     if time <= 120:  # Start year - Jan 20th
#         key = f'{int(year) - 1}Q3'
#     elif 120 < time <= 420:  # Jan 21st - April 20th
#         key = f'{int(year) - 1}Q4'
#     elif 420 < time <= 720:  # April 21st - July 20th
#         key = f"{year}Q1"
#     elif 720 < time <= 1020:  # July 21st -October 20th
#         key = f"{year}Q2"
#     else:  # October 21st - End year
#         key = f"{year}Q3"
#
#     return key

def error_handle(func):
    def inner_function(*args, **kwargs):
        try:
            func(*args, **kwargs)
        except Exception as e:
            err_type = type(e).__name__
            print(f'Error Type: {err_type} | Error Message: {e}')
            traceback.print_tb(e.__traceback__)
            # Retrieve the last n lines of code from the traceback
            stack = traceback.extract_tb(sys.exc_info()[2])
            # for filename, line_number, function, line in stack[-1]:
            line_number = stack[-1].lineno
            source_code = inspect.getsourcelines(inspect.getmodule(inspect.currentframe()))[0]
            err_code = source_code[line_number - 5: line_number]
            print(f"Traceback: {err_code}")

    return inner_function


def quarter_report(str_time, pd_fa):
    year, month, day = str_time.split('-')
    time = int(month) * 100 + int(day)
    if time <= 131:  # Start year - Jan 30th -> move after 1 day
        key = f'{int(year) - 1}Q3'
        next_key = f'{int(year) - 1}Q4'
    elif 131 < time <= 431:  # Feb 1st - April 31st -> move after 1 day
        key = f'{int(year) - 1}Q4'
        next_key = f"{year}Q1"
    elif 431 < time <= 731:  # May 1st - July 31st -> move after 1 day
        key = f"{year}Q1"
        next_key = f"{year}Q2"
    elif 731 < time <= 1031:  # Aug 1st -October 31st -> move after 1 day
        key = f"{year}Q2"
        next_key = f"{year}Q3"
    else:  # Nov 1st - End year
        key = f"{year}Q3"
        next_key = f"{year}Q4"

    # check if the new report has been released
    df_next = pd_fa[(pd_fa["quarter"] == next_key)]
    if not df_next.empty:
        next_release_id = df_next['release_date'].iat[0]
        if datetime.strptime(str_time, '%Y-%m-%d') >= datetime.strptime(next_release_id, '%Y-%m-%d'):
            return next_key
    return key


def before_key(key):
    year, q = key.split('Q')
    q = int(q) - 1
    if q == 0:
        year = int(year) - 1
        q = 4
    return f'{year}Q{q}'


def after_key(key):
    year, q = key.split('Q')
    q = int(q) + 1
    if q == 5:
        year = int(year) + 1
        q = 1
    return f'{year}Q{q}'


##############
@error_handle
def release_report_date(df_00, df_fa, df_quarter):
    df_quarter['ID_Release'] = np.nan
    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        pb = df_fa[(df_fa["quarter"] == key)].head(1)

        try:
            release_date = pb['release_date'].values[0]
            idx = df_00[df_00['time'] == release_date].index
            while not idx.values:
                release_date = (pd.to_datetime(release_date) + pd.Timedelta(1, "d")).strftime('%Y-%m-%d')
                idx = df_00[df_00['time'] == release_date].index

            df_quarter.at[i, "ID_Release"] = idx
        except Exception as e:
            err_type = type(e).__name__
            print(f'Error Type: {err_type} | Error Message: {e}')


@error_handle
def ROE_v1(df_fa, df_quarter):
    """Mean, Sum or Min in 1, 3, 5, 10 years"""

    def process_miss_report(df, c_col):
        """If data miss quarter report data, it will get year report data"""
        df_list = []
        for date, group in df.groupby('yearReport'):
            group_slice = group[group['lengthReport'] != 5].copy()
            if np.nansum(group_slice[c_col].values) == 0:
                # group = df[(df['yearReport'] == date) & (df['lengthReport'] == 5)]
                group_slice = group[group['lengthReport'] == 5].copy()
            df_list.append(group_slice)
        return pd.concat(df_list, axis=0)[c_col] if len(df_list) != 0 else []

    cols = ['ROE3Y', 'ROE5Y', 'ROE10Y', 'ROE_Min3Y', 'ROE_Min5Y', 'ROE_Min10Y', 'ROIC3Y', 'ROIC5Y', 'ROIC10Y',
            'ROIC_Min3Y', 'ROIC_Min5Y', 'ROIC_Min10Y', 'CF_OA_3Y', 'CF_OA_5Y', 'CF_Invest_3Y', 'CF_Invest_5Y']
    df_quarter[cols] = np.nan
    if df_fa is None:
        return
    # merger TCBS to VCI fundamentals data
    if 'roe' in df_fa.columns:
        df_fa['ROE (%)'] = df_fa[['roe', 'ROE (%)']].mean(axis=1)
    if 'from_sale' in df_fa.columns:
        for i in range(0, df_fa.shape[0]):
            if np.isnan(df_fa.loc[i, 'Net cash inflows/outflows from operating activities']):
                df_fa.loc[i, 'Net cash inflows/outflows from operating activities'] = df_fa.loc[i, 'from_sale'] * 1e9

    for i in range(0, df_quarter.shape[0]):
        year, q = df_quarter['quarter'].iat[i].split('Q')
        if year == '2024':
            pass
        if int(q) == 4:
            year = str(int(year) + 1)

        roe_3ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 3) & (
                    df_fa['yearReport'] < int(year))], 'ROE (%)')
        roe_5ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 5) & (
                    df_fa['yearReport'] < int(year))], 'ROE (%)')
        roe_10ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 10) & (
                    df_fa['yearReport'] < int(year))], 'ROE (%)')

        roic_3ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 3) & (
                    df_fa['yearReport'] < int(year))], 'ROIC (%)')
        roic_5ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 5) & (
                    df_fa['yearReport'] < int(year))], 'ROIC (%)')
        roic_10ys = process_miss_report(
            df_fa[(df_fa['lengthReport'].isin([4, 5])) & (df_fa['yearReport'] >= int(year) - 10) & (
                    df_fa['yearReport'] < int(year))], 'ROIC (%)')
        # Not include current year

        # cashflow_3ys = process_miss_report(
        #     df_fa[(df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))],
        #     'Net cash inflows/outflows from operating activities')
        #
        # cashflow_5ys = process_miss_report(
        #     df_fa[(df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))],
        #     'Net cash inflows/outflows from operating activities')
        #
        # cashflow_inv_3ys = process_miss_report(
        #     df_fa[(df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))],
        #     'Purchase of fixed assets')
        #
        # cashflow_inv_5ys = process_miss_report(
        #     df_fa[(df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))],
        #     'Purchase of fixed assets')

        # Using year report
        cashflow_3ys = df_fa[
            (df_fa['lengthReport'] == 5) & (df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))][
            'Net cash inflows/outflows from operating activities'].values
        cashflow_5ys = df_fa[
            (df_fa['lengthReport'] == 5) & (df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))][
            'Net cash inflows/outflows from operating activities'].values

        cashflow_inv_3ys = df_fa[
            (df_fa['lengthReport'] == 5) & (df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))][
            'Purchase of fixed assets'].values
        cashflow_inv_5ys = df_fa[
            (df_fa['lengthReport'] == 5) & (df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))][
            'Purchase of fixed assets'].values

        # Using quarter report
        # cashflow_3ys = df_fa[(df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))][
        #     'Net cash inflows/outflows from operating activities'].values
        # cashflow_5ys = df_fa[(df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))][
        #     'Net cash inflows/outflows from operating activities'].values
        #
        # cashflow_inv_3ys = df_fa[(df_fa['yearReport'] >= int(year) - 3) & (df_fa['yearReport'] < int(year))][
        #     'Purchase of fixed assets'].values
        # cashflow_inv_5ys = df_fa[(df_fa['yearReport'] >= int(year) - 5) & (df_fa['yearReport'] < int(year))][
        #     'Purchase of fixed assets'].values

        # Include current year
        # oshares_5ys = df_fa[(((df_fa['yearReport'] >= int(year) - 5) & (df_fa['lengthReport'] >= q)) | (
        #             df_fa['yearReport'] >= int(year) - 4)) & (df_fa['yearReport'] <= int(year))][
        #     'Outstanding Share (Mil. Shares)'].values

        df_quarter.at[i, 'ROE3Y'] = np.nanmean(roe_3ys) if not np.isnan(roe_3ys).all() else np.nan
        df_quarter.at[i, 'ROE5Y'] = np.nanmean(roe_5ys) if not np.isnan(roe_5ys).all() else np.nan
        df_quarter.at[i, 'ROE10Y'] = np.nanmean(roe_10ys) if not np.isnan(roe_10ys).all() else np.nan
        df_quarter.at[i, 'ROE_Min3Y'] = np.nanmin(roe_3ys) if not np.isnan(roe_3ys).all() else np.nan
        df_quarter.at[i, 'ROE_Min5Y'] = np.nanmin(roe_5ys) if not np.isnan(roe_5ys).all() else np.nan
        df_quarter.at[i, 'ROE_Min10Y'] = np.nanmin(roe_10ys) if not np.isnan(roe_10ys).all() else np.nan

        df_quarter.at[i, 'ROIC3Y'] = np.nanmean(roic_3ys) if not np.isnan(roic_3ys).all() else np.nan
        df_quarter.at[i, 'ROIC5Y'] = np.nanmean(roic_5ys) if not np.isnan(roic_5ys).all() else np.nan
        df_quarter.at[i, 'ROIC10Y'] = np.nanmean(roic_10ys) if not np.isnan(roic_10ys).all() else np.nan
        df_quarter.at[i, 'ROIC_Min3Y'] = np.nanmin(roic_3ys) if not np.isnan(roic_3ys).all() else np.nan
        df_quarter.at[i, 'ROIC_Min5Y'] = np.nanmin(roic_5ys) if not np.isnan(roic_5ys).all() else np.nan
        df_quarter.at[i, 'ROIC_Min10Y'] = np.nanmin(roic_10ys) if not np.isnan(roic_10ys).all() else np.nan

        df_quarter.at[i, 'CF_OA_3Y'] = np.nansum(cashflow_3ys) if not np.isnan(cashflow_3ys).all() else np.nan
        df_quarter.at[i, 'CF_OA_5Y'] = np.nansum(cashflow_5ys) if not np.isnan(cashflow_5ys).all() else np.nan

        df_quarter.at[i, 'CF_Invest_3Y'] = np.nansum(cashflow_inv_3ys) if not np.isnan(
            cashflow_inv_3ys).all() else np.nan
        df_quarter.at[i, 'CF_Invest_5Y'] = np.nansum(cashflow_inv_5ys) if not np.isnan(
            cashflow_inv_5ys).all() else np.nan


@error_handle
def trailing_v1(df_fa, df_quarter):
    def prev_4quarter(quarter):
        quarters = [quarter]
        year, q = quarter.split('Q')
        while (len(quarters) < 4):
            q = int(q) - 1
            if q == 0:
                year = int(year) - 1
                q = 4
            quarters.append(f'{year}Q{q}')
        return quarters

    # Preprocessing data
    df_fa = df_fa.copy()
    df_fa['tax_rate'] = (df_fa['Business income tax - current'] + df_fa['Business income tax - deferred']) / df_fa[
        'Profit before tax']
    df_fa['tax_rate'] = df_fa['tax_rate'].clip(upper=1.0).fillna(0.0)

    df_fa['NOPAT'] = df_fa['EBIT (Bn. VND)'] * (1 - df_fa['tax_rate'])

    df_fa['Invested Capital'] = df_fa["OWNER'S EQUITY(Bn.VND)"] + df_fa["Long-term borrowings (Bn. VND)"] - df_fa[
        "Cash and cash equivalents (Bn. VND)"]

    df_fa['ROIC'] = df_fa['NOPAT'] / df_fa['Invested Capital']
    df_fa['ROIC'] = df_fa['ROIC'].replace([float('inf'), -float('inf')], pd.NA)

    df_fa['ROE'] = (df_fa['Net Profit For the Year'] / df_fa["OWNER'S EQUITY(Bn.VND)"])

    # Init
    cols = ['ROE_Trailing', 'ROIC_Trailing', 'ROIC_Trailing_v1']

    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        quarters = prev_4quarter(key)
        roes = []
        roic = []
        roic_v1 = []
        for j in range(len(quarters)):
            try:
                df_q = df_fa[df_fa["quarter"] == quarters[j]].head(1)
                roes.append(df_q['ROE'].values[0] if not df_q.empty else np.nan)
                roic.append(df_q['ROIC'].values[0] if not df_q.empty else np.nan)
                roic_v1.append(df_q['ROIC (%)'].values[0] if not df_q.empty else np.nan)

            except Exception as e:
                err_type = type(e).__name__
                print(
                    f' Ticker: {df_fa["ticker"].values[0]} | Quarter: {quarters[j]} | Error Type: {err_type} | Error Message: {e}')

        df_quarter.at[i, 'ROE_Trailing'] = np.nansum(roes) if not np.isnan(roes).all() else np.nan
        df_quarter.at[i, 'ROIC_Trailing'] = np.nansum(roic) if not np.isnan(roic).all() else np.nan
        df_quarter.at[i, 'ROIC_Trailing_v1'] = np.nansum(roic_v1) if not np.isnan(roic_v1).all() else np.nan


@error_handle
def quarter_8_v1(df_fa, df_quarter):
    def prev_8quarter(quarter):
        quarters = [quarter]
        year, q = quarter.split('Q')
        while (len(quarters) < 8):
            q = int(q) - 1
            if q == 0:
                year = int(year) - 1
                q = 4
            quarters.append(f'{year}Q{q}')
        return quarters

    cols_np = ['NP_R', 'NP_P0', 'NP_P1', 'NP_P2', 'NP_P3', 'NP_P4', 'NP_P5', 'NP_P6', 'NP_P7']
    cols_re = ['Revenue_P0', 'Revenue_P1', 'Revenue_P2', 'Revenue_P3', 'Revenue_P4', 'Revenue_P5', 'Revenue_P6',
               'Revenue_P7']
    cols_gpm = ['GPM_P0', 'GPM_P1', 'GPM_P2', 'GPM_P3', 'GPM_P4', 'GPM_P5', 'GPM_P6', 'GPM_P7']
    cols = cols_np + cols_re + cols_gpm
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        quarters = prev_8quarter(key)
        for j in range(len(quarters)):

            try:
                df = df_fa[df_fa["quarter"] == quarters[j]].head(1)
                # Net Profit
                df_quarter.at[i, f'NP_P{j}'] = df['Attribute to parent company (Bn. VND)'].values[
                    0] if not df.empty else np.nan
                # Revenue
                df_quarter.at[i, f'Revenue_P{j}'] = df['Revenue (Bn. VND)'].values[
                    0] if not df.empty else np.nan
                # GPM
                df_quarter.at[i, f'GPM_P{j}'] = df['Gross Profit Margin (%)'].values[
                    0] if not df.empty else np.nan

            except Exception as e:
                err_type = type(e).__name__
                print(
                    f' Ticker: {df_fa["ticker"].values[0]} | Quarter: {quarters[j]} | Error Type: {err_type} | Error Message: {e}')

        df_quarter.at[i, 'NP_R'] = np.nan
        if df_quarter.at[i, 'NP_P4'] != 0:
            df_quarter.at[i, 'NP_R'] = df_quarter.at[i, 'NP_P0'] / df_quarter.at[i, 'NP_P4'] - 1


@error_handle
def selected_quarter_v1(df_fa, df_quarter):
    def prev_selected_quarters(quarter):
        selected_indices = {0, 4}
        result = {}
        year, q = quarter.split('Q')
        year, q = int(year), int(q)

        for i in range(5):  # Only need to go up to the 4th previous quarter
            if i in selected_indices:
                result[i] = f'{year}Q{q}'
            q -= 1
            if q == 0:
                year -= 1
                q = 4

        return result

    fa_cols = ['Current Ratio', 'ROA (%)', 'EBIT Margin (%)', 'Net Profit Margin (%)', 'Cash Ratio', 'Quick Ratio',
               'Financial Leverage', 'Asset Turnover', 'Fixed Asset Turnover', 'Days Sales Outstanding',
               'Days Inventory Outstanding', 'Days Payable Outstanding', 'Cash Cycle', 'Inventory Turnover',
               '(ST+LT borrowings)/Equity', 'Debt/Equity', 'Fixed Asset-To-Equity', "Owners' Equity/Charter Capital",
               'Revenue YoY (%)', 'Interest Coverage']

    root_cols = ['CR', 'ROA', 'EBITM', 'NPM', 'CashR', 'QuickR', 'FinLev', 'AssetTurn', 'FAssetTurn', 'DSO', 'DIO',
                 'DPO', 'CashCycle', 'InvTurn', 'STLTDebt_Eq', 'Debt_Eq', 'FAsset_Eq', 'OwnEq_Cap', 'Revenue_YoY',
                 'IntCov']

    cols = []
    for clmn in root_cols:
        cols.append(f"{clmn}_P0")
        cols.append(f"{clmn}_P4")

    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        quarters = prev_selected_quarters(key)
        for k, v in quarters.items():
            try:
                df = df_fa[df_fa["quarter"] == v].head(1)
                for col, fa_col in zip(root_cols, fa_cols):
                    try:
                        df_quarter.at[i, f'{col}_P{k}'] = df[fa_col].values[0] if not df.empty else np.nan
                    except Exception as e:
                        err_type = type(e).__name__
                        print(
                            f' Ticker: {df_fa["ticker"].values[0]} | errorType: {err_type} | errorMessage: {e}')

            except Exception as e:
                err_type = type(e).__name__
                print(
                    f' Ticker: {df_fa["ticker"].values[0]} | Quarter: {v} | errorType: {err_type} | errorMessage: {e}')


@error_handle
def devidends(df_fa, df_quarter):
    @lru_cache(maxsize=None)
    def dividend_quarter(quarter, num_years):
        quarters = []
        year, q = map(int, quarter.split('Q'))
        for _ in range(num_years + 1):
            quarters.append(f'{year}Q5' if q == 4 else f'{year - 1}Q5')
            year -= 1

        return quarters

    cols = ['Dividend_Min3Y', 'Dividend_1Y', 'Dividend_3Y']
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        quarters_1y = dividend_quarter(key, 1)
        quarters_3y = dividend_quarter(key, 3)

        if df_fa[df_fa["quarter"].isin(quarters_1y[:-1])].empty:
            quarters_1y = quarters_1y[1:]
            quarters_3y = quarters_3y[1:]
        else:
            quarters_1y.pop()
            quarters_3y.pop()

        df_1y = df_fa[df_fa["quarter"].isin(quarters_1y)].drop_duplicates(subset=['quarter'])
        dividends_1y = -(df_1y['Dividends paid'] / df_1y['Outstanding Share (Mil. Shares)']).values
        dividends_1y[np.isinf(dividends_1y)] = np.nan

        df_quarter.at[i, 'Dividend_1Y'] = np.nansum(dividends_1y) if not np.isnan(dividends_1y).all() else np.nan

        df_3y = df_fa[df_fa["quarter"].isin(quarters_3y)].drop_duplicates(subset=['quarter'])
        dividends_3y = -(df_3y['Dividends paid'] / df_3y['Outstanding Share (Mil. Shares)']).values
        dividends_3y[np.isinf(dividends_3y)] = np.nan

        df_quarter.at[i, 'Dividend_3Y'] = np.nansum(dividends_3y) if not np.isnan(dividends_3y).all() else np.nan
        df_quarter.at[i, 'Dividend_Min3Y'] = np.nanmin(dividends_3y) if not np.isnan(dividends_3y).all() else np.nan


@error_handle
def quarter_4_v1(df_fa, df_quarter):
    def prev_4quarter(quarter):
        quarters = [quarter]
        year, q = quarter.split('Q')
        while (len(quarters) < 5):
            q = int(q) - 1
            if q == 0:
                year = int(year) - 1
                q = 4
            quarters.append(f'{year}Q{q}')
        return quarters

    cols_cf = ['CF_OA_P0', 'CF_OA_P1', 'CF_OA_P2', 'CF_OA_P3', 'CF_OA_P4']
    cols_in = ['CF_Invest_P0', 'CF_Invest_P1', 'CF_Invest_P2', 'CF_Invest_P3', 'CF_Invest_P4']

    cols = cols_cf + cols_in
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        quarters = prev_4quarter(key)
        for j in range(len(quarters)):
            try:
                df = df_fa[df_fa["quarter"] == quarters[j]].head(1)
                # CF from operating activities
                df_quarter.at[i, f'CF_OA_P{j}'] = df['Net cash inflows/outflows from operating activities'].values[
                    0] if not df.empty else np.nan

                # CF from investing activities
                df_quarter.at[i, f'CF_Invest_P{j}'] = df['Purchase of fixed assets'].values[
                    0] if not df.empty else np.nan

            except Exception as e:
                err_type = type(e).__name__
                print(
                    f' Ticker: {df_fa["ticker"].values[0]} | Quarter: {quarters[j]} | Error Type: {err_type} | Error Message: {e}')


@error_handle
def finance_indicator_v1(df_fa, df_quarter):
    # Dividend yield (%) - Debt/Equity
    fa_cols = ['Dividend yield (%)', 'EPS (VND)', 'BVPS (VND)', 'Outstanding Share (Mil. Shares)',
               'TOTAL ASSETS (Bn. VND)', 'Long-term liabilities (Bn. VND)', 'Accounts receivable (Bn. VND)',
               'Short-term borrowings (Bn. VND)', 'EBITDA (Bn. VND)']

    cols = ['DY', 'EPS_P0', 'BVPS', 'OShares', 'totalAsset_P0', 'LtDebt_P0', 'AR_P0', 'StDebt_P0', 'EBITDA_P0']

    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        pb = df_fa[(df_fa["quarter"] == key)].head(1)
        for col, fa_col in zip(cols, fa_cols):
            try:
                df_quarter.at[i, col] = np.mean(pb[fa_col])
            except Exception as e:
                err_type = type(e).__name__
                print(
                    f' Ticker: {df_fa["ticker"].values[0]} | Quarter: {i} | Error Type: {err_type} | Error Message: {e}')


@error_handle
def finance_indicator_cal_v1(df_fa, df_quarter):
    cols = ['Cash_P0']
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        pb = df_fa[(df_fa["quarter"] == key)].head(1)
        df_quarter.at[i, 'Cash_P0'] = np.mean(pb['Cash and cash equivalents (Bn. VND)'] + pb[
            'Short-term investments (Bn. VND)'])


@error_handle
def price_indicator_v1(pdraw, df_fa, df_quarter):
    cols = ['PB', 'PE', 'PS', 'PCF', 'EVEB']
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        pb = df_fa[(df_fa["quarter"] == key)].head(1)

        if pb.empty:
            pb = pd.concat([pb, pd.Series([np.nan])])

        pd_time = pdraw[pdraw['quarter'] == key].copy()
        price_value = (pb['Market Capital (Bn. VND)'].values / pb['Outstanding Share (Mil. Shares)'].values) \
            if pb['Outstanding Share (Mil. Shares)'].values != 0 else np.nan

        diff = abs(pd_time['Price'] - price_value)
        if not pd.isna(diff).all():
            min_idx = diff.idxmin(axis=0)
            report_date = pd_time.loc[min_idx:min_idx]
        else:
            # workaround for abnormal
            report_date = pd_time.iloc[-1:]
        close_value = report_date['Close'].values[0]

        if close_value != 0:
            df_quarter.at[i, 'PB'] = pb['P/B'].values / close_value
            df_quarter.at[i, 'PE'] = pb['P/E'].values / close_value
            # df_quarter.at[i, 'PE'] = df_quarter.at[i, 'OShares'] / np.sum(
            #     df_quarter.loc[i, ['NP_P0', 'NP_P1', 'NP_P2', 'NP_P3']].ffill())
            df_quarter.at[i, 'PS'] = pb['P/S'].values / close_value
            df_quarter.at[i, 'PCF'] = pb['P/Cash Flow'].values / close_value
            df_quarter.at[i, 'EVEB'] = pb['EV/EBITDA'].values / close_value

    # pdraw[col].iat[i] = pdraw['Close'].iat[i] * dict[key][col]


@error_handle
def f_score_v1(df_fa, df_quarter):
    def pre_same_quarter(quarter):
        y, q = quarter.split('Q')
        y = int(y) - 1
        return f'{y}Q{q}'

    def cal_score(c_year, p_year):

        if c_year.empty or p_year.empty:
            return np.nan

        fscore = 0
        # Profitability
        # income
        fscore += int(c_year['Attribute to parent company (Bn. VND)'].values > p_year[
            'Attribute to parent company (Bn. VND)'].values)
        # ocf
        fscore += int(c_year['Net cash inflows/outflows from operating activities'].values > 0)
        # roa
        fscore += int(
            (c_year['Attribute to parent company (Bn. VND)'] / c_year['TOTAL ASSETS (Bn. VND)']).values > (
                    p_year['Attribute to parent company (Bn. VND)'] / p_year[
                'TOTAL ASSETS (Bn. VND)']).values)  # ROA

        # quality of earning
        fscore += int((c_year['Net cash inflows/outflows from operating activities'].values > c_year[
            'Attribute to parent company (Bn. VND)'].values))

        # Leverage, Liquidity and Source of Funds
        # long-term debt
        fscore += int((c_year['Long-term borrowings (Bn. VND)'] / c_year['TOTAL ASSETS (Bn. VND)']).values < (
                p_year['Long-term borrowings (Bn. VND)'] / p_year['TOTAL ASSETS (Bn. VND)']).values)
        # current ratio - liquidity
        fscore += int(c_year['Current Ratio'].values > p_year['Current Ratio'].values)
        # number of shares
        fscore += int(
            c_year['Outstanding Share (Mil. Shares)'].values <= p_year['Outstanding Share (Mil. Shares)'].values)

        # Operating Efficiency
        # gross margin
        fscore += int(c_year['Gross Profit Margin (%)'].values > p_year['Gross Profit Margin (%)'].values)
        # turnover
        fscore += int((c_year['Net Sales'].sum() / c_year['TOTAL ASSETS (Bn. VND)']).values > (
                p_year['Net Sales'].sum() / p_year['TOTAL ASSETS (Bn. VND)']).values)

        return fscore

    cols = ['FSCORE', 'FSCORE_P1']
    df_quarter[cols] = np.nan
    if df_fa is None:
        return

    key_latest = df_quarter['quarter'].iat[-1]
    for i in range(0, df_quarter.shape[0]):
        key = df_quarter['quarter'].iat[i]
        # Report didn't release on time
        if (key == key_latest) and (df_fa[(df_fa["quarter"] == key)].head(1).empty):
            key = before_key(key)

        c_year_p0 = df_fa[(df_fa["quarter"] == key)].head(1)
        p_year_p0 = df_fa[(df_fa["quarter"] == pre_same_quarter(key))].head(1)

        key_previous = before_key(key)
        c_year_p1 = df_fa[(df_fa["quarter"] == key_previous)].head(1)
        p_year_p1 = df_fa[(df_fa["quarter"] == pre_same_quarter(key_previous))].head(1)

        df_quarter.at[i, 'FSCORE'] = cal_score(c_year_p0, p_year_p0)
        df_quarter.at[i, 'FSCORE_P1'] = cal_score(c_year_p1, p_year_p1)


if __name__ == "__main__":
    from vnstock3 import Vnstock
    from datetime import datetime
    import vnstock

    stock = Vnstock().stock(symbol='AAS', source='VCI')
    # stock = Vnstock().stock(source='TCBS', symbol='AAS')
    ratio_vci = stock.finance.ratio(period='quarter', lang='en')
    income_statement_vci = stock.finance.income_statement(period='quarter', lang='en')
    balance_sheet_vci = stock.finance.balance_sheet(period='quarter', lang='en')
    cash_flow_vci = stock.finance.cash_flow(period='quarter', lang='en')

    pd_ticker = vnstock.listing_companies()
    pd_ticker_1 = stock.listing.all_symbols()
    pd_ticker_2 = stock.listing.symbols_by_industries()


    def load_ticker(ticker, in_folder='../data/daily_latest'):
        pdraw = pd.read_csv(f'{in_folder}/{ticker}.csv', dtype={'time': str}).rename(columns={'high': 'High',
                                                                                              'low': 'Low',
                                                                                              'open': 'Open',
                                                                                              'close': 'Close',
                                                                                              'volume': 'Volume'})
        pdraw = pdraw.drop_duplicates(subset=['time'], keep='last').reset_index(drop=True).sort_values('time')
        return pdraw

        # tickers = ["VNINDEX"]
        # tickers = ['VN30', 'DGC', 'VCB', 'VNM', 'HSG']
        # for ticker in tickers:
        # # data = datetime.now().strftime("%m-%d")
        # stock = Vnstock().stock(source='TCBS', symbol=ticker)
        # stock.update_symbol(ticker)
        # print(ticker)
        # cash_flow_tcbs = stock.finance.cash_flow(period='quarter')
        # ratio = stock.finance.ratio(period='quarter')
        # ratio[['year', 'quarter']] = ratio[['year', 'quarter']].astype(int)
        # cash_flow_tcbs[['year', 'quarter']] = cash_flow_tcbs[['year', 'quarter']].astype(int)
        # # ratio['periods'] = ratio['year'].astype(str) + '-' + ratio['quarter'].astype(str)
        # # cash_flow_tcbs['periods'] = cash_flow_tcbs['year'].astype(str) + '-' + cash_flow_tcbs['quarter'].astype(str)
        # tcbs = pd.merge(cash_flow_tcbs, ratio, on=['year', 'quarter'], how='outer')
        # # income_statement = stock.finance.income_statement(period='quarter')
        # # balance_sheet = stock.finance.balance_sheet(period='quarter')
        # stock = Vnstock().stock(symbol=ticker, source='VCI')
        # # df_ratio = stock.finance.ratio(period='quarter', lang='en')
        # # df_ratio.columns = df_ratio.columns.droplevel(0)
        # cash_flow = stock.finance.cash_flow(period='quarter', lang='en')
        # #
        # tcbs['periods'] = tcbs['year'].astype(str) + '-' + tcbs['quarter'].astype(str)
        # cash_flow['periods'] = cash_flow['yearReport'].astype(str) + '-' + cash_flow['lengthReport'].astype(str)
        # df = pd.merge(cash_flow, tcbs, on='periods', how='outer').sort_values('periods', ascending=False).reset_index(
        #     drop=True)
        # df['lengthReport'] = df['quarter']
        # df.drop(['quarter'], axis=1, inplace=True)

        stock = Vnstock(show_log=False).stock(symbol=ticker, source='VCI')
        stock.listing.symbols_by_industries()
        ratio_vci = stock.finance.ratio(period='quarter', lang='en')
        income_statement_vci = stock.finance.income_statement(period='quarter', lang='en')
        balance_sheet_vci = stock.finance.balance_sheet(period='quarter', lang='en')
        cash_flow_vci = stock.finance.cash_flow(period='quarter', lang='en')

        # finance_quarter_index_v.columns = finance_quarter_index_v.columns.droplevel(0)
        #
        # stock = Vnstock().stock(source='TCBS')
        # price = stock.quote.history(symbol=ticker, start='2007-01-01', end='2024-12-31', interval='1D', count_back=None)
        # price = price.drop_duplicates().reset_index(drop=True)
        # price['time'] = pd.to_datetime(price['time'], unit='s').dt.tz_localize('UTC')
        # # price.to_excel(f"temp/price_{ticker}_tcsb_vi.xlsx")
        #
        # stock = Vnstock().stock(source='VCI')
        # price_1 = stock.quote.history(symbol=ticker, start='2007-01-01', end='2024-12-31', interval='1D',
        #                               count_back=None)
        # # price_1.to_excel(f"temp/price_{ticker}_vci_vi.xlsx")
        # price_VCI = stock.quote.history(symbol=ticker, start='2007-01-01', end='2024-12-31')
        #
        # price_VCI__1 = stock.quote.history(symbol=ticker, start='2007-01-01', end='2024-12-31', interval='1D',
        #                                    count_back=None)
        #
        # # pd.merge(cash_flow, ratio, on=['quarter', 'year'], how='outer')
        # pass

        # cash_flow = stock.finance.cash_flow(period='year')
        # price = stock.quote.history(start='2007-01-01', end='2024-12-31')

        # finance_quarter_index_v.to_excel(f"temp/ratio_{ticker}_{data}_vi.xlsx")
        # income_statement.to_excel(f"temp/income_{ticker}_{data}_vi.xlsx")
        # balance_sheet.to_excel(f"temp/balance_{ticker}_{data}_vi.xlsx")
        # cash_flow.to_excel(f"temp/cash_flow_{ticker}_{data}_vi.xlsx")
        # price.to_excel(f"temp/price_{ticker}_{data}_vi.xlsx")
