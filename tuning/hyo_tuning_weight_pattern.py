import json
import logging
import os
import pickle
import re
import sys
import warnings
from datetime import timedelta

import numpy as np
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime
from webui.utils import Simulation_weight, WeightEvaluation
from core_utils.constant import JOBLIB_CACHE_DIR

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host="*************")

# redis_cache = EvalRedis()
RANKING = 'ranking_point'
HYO_NAME = 'hyperopt_weight_50'
SI_TYPE = 'full_cash_not_fix_slot'


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def quarter_report(str_time):
    year, month, day = str_time.split('-')
    time = int(month) * 100 + int(day)
    if time <= 131:  # Start year - Jan 30th -> move after 1 day
        key = f'{int(year) - 1}Q3'
    elif 131 < time <= 431:  # Feb 1st - April 31st -> move after 1 day
        key = f'{int(year) - 1}Q4'
    elif 431 < time <= 731:  # May 1st - July 31st -> move after 1 day
        key = f"{year}Q1"
    elif 731 < time <= 1031:  # Aug 1st -October 31st -> move after 1 day
        key = f"{year}Q2"
    else:  # Nov 1st - End year
        key = f"{year}Q3"
    return key


def evaluate(dictFilter, weights, lookback, exp, thres_buy=1, thres_sell=-1, cutloss=0.15, eval_type='buy'):
    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = WeightEvaluation(ticker, pdxx, dictFilter, cutloss=cutloss, weight=weights,
                                           lookback=lookback, k_exp=exp, threshold_buy=thres_buy,
                                           threshold_sell=thres_sell,
                                           cache_service=redis_cache)
            if eval_type == 'buy':
                res_b = eval_ticker.get_eval_weight_hit()
                return res_b
        except Exception as error:
            logging.error(f"Error: {ticker}: {error}")
            print(f"Error: {ticker}: {error}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    return lres


def eval_filter_by_weight(dictFilter, weights, lookback, exp, thres_buy=1, thres_sell=-1, CUTLOSS=0.15,
                          eval_type='buy'):
    lres = evaluate(dictFilter, weights, lookback, exp, thres_buy, thres_sell, CUTLOSS, eval_type)

    try:
        df = pd.concat([res for res in lres if res is not None and not res.empty], axis=0). \
            sort_values('time', ascending=True).reset_index(drop=True)

        # if df.shape[0] < 1000:
        #     print(f"Too few deals: {df.shape[0]}")
        #     raise Exception

        df['p_win'] = df['p_sell'].where(df['p_sell'] > 0, np.nan)
        df['p_loss'] = df['p_sell'].where(df['p_sell'] <= 0, np.nan)
        df['holding_period'] = (pd.to_datetime(df['sell_time']) - pd.to_datetime(df['time'])).dt.days
        df["quarter"] = pd.to_datetime(df['time']).dt.to_period('Q').astype(str)
        df["quarter_fr"] = df['time'].apply(lambda x: quarter_report(x))

        _df_q = df.groupby(['quarter'], as_index=False).agg({
            'p_win': 'count',
            'p_loss': 'count',
            'p_cutloss': 'count',
        })
        _df_q.rename(columns={'p_win': 'Win', 'p_loss': 'Loss', 'p_cutloss': 'Cutloss'}, inplace=True)
        _df_q_tail = _df_q.tail(9).iloc[:-1]

        start_date, end_date = parse_time(dictFilter)
        simulation = Simulation_weight(start_date=start_date, end_date=end_date, initial_assets=50e9, max_deals=10)
        si_result = simulation.run_fast(df, iterate=30, s_type=SI_TYPE)

        si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
                   'si_return_std', 'si_utilization', 'si_ticker_diversity']
        si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                          'return_std', 'utilization', 'set_ticker']
        si_res = {}
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            si_res[si_col] = si_result['sum_weight'][si_result_col]

        # detail_result = simulation.get_detail_result()

        return {
            'hit': df.shape[0],
            '%win': df['p_win'].count() / df.shape[0] * 100,
            '%loss': df['p_loss'].count() / df.shape[0] * 100,
            '%cutloss': df['p_cutloss'].count() / df.shape[0] * 100,
            'p_win': df['p_win'].mean(),
            'p_loss': df['p_loss'].mean(),
            'p_cutloss': df['p_cutloss'].mean(),
            '%wing_quarter': (_df_q[(_df_q['Win'] > (_df_q['Loss'] + _df_q['Cutloss']))].shape[0] / _df_q.shape[
                0]) * 100,
            '%wing_block_8q': (_df_q_tail[(_df_q_tail['Win'] > (_df_q_tail['Loss'] + _df_q_tail['Cutloss']))].shape[0] /
                               _df_q_tail.shape[0]) * 100,
            # 'completed_deals': detail_result['completed_deals'],
            # 'skip_deals': detail_result['skip_deals'],
            'si_result': si_res

        }

    except Exception as e:
        print(e)
        return {}


if __name__ == "__main__":
    import random

    random.seed(123)

    Init = "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')"
    filter = {
        "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
        "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TL3M": "BearDvg2, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M",
        "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
        "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
        "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
        "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
        "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
        "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "Init": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
        "_BKMA200": f"{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
        "_TrendingGrowth": f"{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
        "_TL3M": f"{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
        "_BuySupport": f"{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
        "_RSILow30": f"{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
        "_UnderBV": f"{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
        "_SuperGrowth": f"{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
        "_SurpriseEarning": f"{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
        "_Conservative": "(Volume*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
        "_BullDvg": f"{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
        "_VolMax1Y": f"{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
        "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
        "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
        "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
        "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
        "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
        "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
        "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
        "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
        "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
        "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
        "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
        "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
        "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
    }


    def objective(params):
        thres_buy = 1
        thres_sell = -1
        weights = {}
        for k, _ in filter.items():
            if f'w_{k[1:].lower()}' in params.keys():
                weights[k[1:]] = params[f'w_{k[1:].lower()}']

        # w_lookback = params['w_lookback']
        w_lookback = 10
        w_exp = params['w_exp']

        res = eval_filter_by_weight(filter, weights=weights, thres_buy=thres_buy, thres_sell=thres_sell,
                                    lookback=w_lookback, exp=w_exp, CUTLOSS=0.15, eval_type='buy')
        si_res = res.get('si_result', {})

        si_return = si_res.get("si_return", -np.inf)
        win_deal = res.get("%win", -np.inf)

        loss = si_return + win_deal

        logging.info("----------------------------------------------------------------------------------------------")
        logging.info(f'weights: {weights}')
        logging.info(
            f'hit: {res.get("hit")}, si_deal: {si_res.get("si_deals")}, si_return: {si_return}, win_hit: {win_deal}, '
            f'wing_quarter: {res.get("%wing_quarter")}, wing_8quarter: {res.get("%wing_block_8q")}, '
            f'utilization: {si_res.get("si_utilization")}, diversity: {si_res.get("si_ticker_diversity")}, loss:{loss}')
        logging.info(f"\n")

        log_data = {
            "look_back": w_lookback,
            "w_exp": w_exp,
            "hit": res.get("hit"),
            "si_deal": si_res.get("si_deals"),
            "si_return": si_return,
            "win_hit": win_deal,
            "wing_quarter": res.get("%wing_quarter"),
            "wing_8quarter": res.get("%wing_block_8q"),
            "utilization": si_res.get("si_utilization"),
            "diversity": si_res.get("si_ticker_diversity"),
            "loss": loss
        }
        log_data.update(weights)

        # Đọc dữ liệu cũ nếu file tồn tại
        LOG_FILE = f"tuning/{HYO_NAME}.csv"
        if os.path.exists(LOG_FILE):
            df = pd.read_csv(LOG_FILE)
        else:
            df = pd.DataFrame(columns=list(log_data.keys()))

        # Thêm dữ liệu mới
        df = df._append(log_data, ignore_index=True)

        # Ghi vào file CSV
        df.to_csv(LOG_FILE, index=False)

        print("----------------------------------------------------------------------------------------------")
        print(f'weights: {weights}, look_back: {w_lookback}, w_exp: {w_exp}')
        print(
            f'hit: {res.get("hit")}, si_deal: {si_res.get("si_deals")}, si_return: {si_return}, win_hit: {win_deal}, '
            f'wing_quarter: {res.get("%wing_quarter")}, wing_8quarter: {res.get("%wing_block_8q")}, '
            f'utilization: {si_res.get("si_utilization")}, diversity:{si_res.get("si_ticker_diversity")}, loss: {loss}')
        print(f"\n")
        return {'loss': -loss, 'status': STATUS_OK}


    # define space
    search_space = {
        # "w_lookback": hp.quniform(f"w_lookback", 5, 20, 1),
        "w_exp": hp.quniform(f"w_exp", 0, 1, 0.05)
    }
    init_vals = {
        # "w_lookback": 10,
        "w_exp": 1
    }
    for k in filter.keys():
        if k.startswith('_'):
            search_space[f"w_{k.lower()[1:]}"] = hp.quniform(f"w_{k.lower()[1:]}", 0, 2, 0.1)
            init_vals[f"w_{k.lower()[1:]}"] = 1
        elif k.startswith('~'):
            search_space[f"w_{k.lower()[1:]}"] = hp.quniform(f"w_{k.lower()[1:]}", -2, 0, 0.1)
            init_vals[f"w_{k.lower()[1:]}"] = -1

    # objective(init_vals)

    trial_name = f"tuning/{HYO_NAME}.pkl"
    if os.path.exists(trial_name):

        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate([init_vals])

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
