import logging
import os
import pickle
import re
import json
import sys
import warnings
from datetime import timedelta
from functools import lru_cache

import numpy as np
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime
from core_utils.base_eval import TickerEval, PreProcess, Simulation

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
# redis_cache = EvalRedis(host="*************")
redis_cache = EvalRedis()
HYO_NAME = 'hyo_complex_score_init_50b_v2'
SI_TYPE = 'adjust_cash'
PROFILE = 'selected_selected_init_50b_25slot_cs_v2'
INIT_ASSETS = 50e9
INIT_SLOTS = 25

NUM_PROCS = 30
def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


@lru_cache(maxsize=10)
def evaluate(tuple_filter, CUTLOSS=0.15):
    print("not cache")
    filters = dict(tuple_filter)
    buy_filter = [k[1:] for k in filters.keys() if k.startswith('_')]

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, filters, cutloss=CUTLOSS, cache_service=redis_cache)
            res = eval_ticker.get_deal()
            return res
        except Exception as error:
            print(f"Error: {ticker}: {error}")
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = NUM_PROCS
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    lres = [res for res in lres if res is not None and not res.empty]
    pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)
    # pd_deal['sell_time'] = pd_deal['sell_time'].dt.strftime('%Y-%m-%d')
    # if pd_deal.shape[0] < 150:
    #     print(pd_deal.shape[0])
    #     raise Exception("Not enough deals")

    df_process = PreProcess()
    pd_deal = df_process.deals(pd_deal)

    # group find multideal in same day

    pd_deal[buy_filter] = 0
    grouped = pd_deal.groupby(["time", "ticker"])
    result = grouped.apply(lambda group: group.assign(**{filter: 1 for filter in group['filter']}))

    # df_list = []
    # for date, group in pd_deal.groupby(["time", "ticker"]):
    #     for _, g in group.iterrows():
    #         group[g['filter']] = 1
    #     df_list.append(group)
    # result = pd.concat(df_list, ignore_index=True)
    return result


def eval_filter_all_v2(dictFilter, ranking, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def score_func(df, weight):
        bias = 1
        conditions = {
            "w1": df['D_RSI'] / df['D_RSI_T1W'] > 1,
            "w2": df['Close'] / df['VAP1W'] > 1,
            "w3": df['D_MFI'] / df['D_MFI_T1W'] > 1,
            "w4": df['D_MACD'] / df['D_MACD_T1W'] > 1,
            "w5": (df['Close'] / df['LO_3M_T1'] > 0.97) & (df['Close'] / df['LO_3M_T1'] < 1.2),
            "w6": df['Volume'] / df['Volume_1M'] > 1,
            "w7": df['D_RSI_Max1W'] / df['D_RSI_Max3M'] > 1,
            "w8": df['FSCORE'] / 9,
            "w9": df['BKMA200'],
            "w10": df['BullDvg'],
            "w11": df['BuySupport'],
            "w12": df['Conservative'],
            "w13": df['RSILow30'],
            "w14": df['SuperGrowth'],
            "w15": df['SurpriseEarning'],
            "w16": df['TL3M'],
            "w17": df['TrendingGrowth'],
            "w18": df['UnderBV'],
            "w19": df['VolMax1Y'],
            "w20": df['T3P4'],
            "w21": bias,
        }

        # df['ranking_score'] = sum(
        #         v.astype(float) * weight[k] if isinstance(v, bool) else v * weight[k] for k, v in conditions.items()
        #     )
        df['ranking_score'] = sum(weight[k] * v for k, v in conditions.items())
        return df

    try:
        pd_deal = evaluate(tuple(sorted(dictFilter.items())), CUTLOSS)

        start_date, _ = parse_time(dictFilter)
        simulation = Simulation(start_date=start_date, initial_assets=INIT_ASSETS, max_deals=INIT_SLOTS,
                                cache_service=memory, num_proc=NUM_PROCS)

        # df_combine_deals = pd_deal.merge(df_ranking[['filter', 'ranking_point']], on='filter', how='left')
        df_combine_deals = score_func(pd_deal, ranking)
        si_result = simulation.run_fast(df_deals=df_combine_deals, iterate=50, s_type=SI_TYPE,
                                        r_col='ranking_score', handle='combine')

        si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
                   'si_return_std', 'si_utilization', 'si_win_deal', 'si_win_quarter', 'si_ticker_diversity',
                   'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker']
        si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                          'return_std', 'utilization', 'win_deal', 'win_quarter', 'set_ticker',
                          'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker']
        si_res = {}
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            si_res[si_col] = si_result['Combine'][si_result_col]

        return {
            'si_return': si_res['si_return'],
            'si_std': si_res['si_return_std'],
            'si_deal': si_res['si_deals'],
            'si_utilization': si_res['si_utilization'],
            'si_diversity': si_res['si_ticker_diversity'],
            'si_quarter_ticker_diversity': si_res['si_quarter_ticker_diversity'],
            'si_win_quarter': si_res['si_win_quarter'],
            'si_win_deal': si_res['si_win_deal'],
            'status': STATUS_OK
        }
    except Exception as e:
        print(f"Error: {e}")
        return {}


if __name__ == "__main__":

    import random

    random.seed(0)


    def objective(params):

        configFilters = json.loads(open("config.json", "r").read())
        dictFilter = json.loads(configFilters[PROFILE]['filter'])
        if 'Init' in dictFilter.keys():
            for key, value in dictFilter.items():
                dictFilter[key] = value.replace("{Init}", dictFilter['Init'])
        print(dictFilter)

        result = eval_filter_all_v2(dictFilter, params)

        si_return = result.get("si_return", -np.inf)
        windeal = result.get("si_win_deal", -np.inf)

        loss = si_return + windeal

        log_data = {
            "si_return": result.get("si_return"),
            "si_std": result.get("si_std"),
            "si_deal": result.get("si_deal"),
            "si_utilization": result.get("si_utilization"),
            "si_diversity": result.get("si_diversity"),
            "si_quarter_ticker_diversity": result.get("si_quarter_ticker_diversity"),
            "si_win_quarter": result.get("si_win_quarter"),
            "si_win_deal": result.get("si_win_deal"),
            "loss": loss
        }
        log_data.update(params)

        # Đọc dữ liệu cũ nếu file tồn tại
        LOG_FILE = f"tuning/{HYO_NAME}.csv"
        if os.path.exists(LOG_FILE):
            df = pd.read_csv(LOG_FILE)
        else:
            df = pd.DataFrame(columns=list(log_data.keys()))

        # Thêm dữ liệu mới
        df = df._append(log_data, ignore_index=True)

        # Ghi vào file CSV
        df.to_csv(LOG_FILE, index=False)

        print("----------------------------------------------------------------------------------------------")
        print(f"ranking: {params}")
        print(
            f'si_deal: {result.get("si_deal")}, si_return: {si_return}, si_win_deal: {windeal}, '
            f'si_win_quarter: {result.get("si_win_quarter")}, si_quarter_ticker_diversity: {result.get("si_quarter_ticker_diversity")}, '
            f'utilization: {result.get("si_utilization")}, si_diversity: {result.get("si_diversity")}, loss:{loss}')
        print(f"\n")
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0, 1, 0.05),
        'w3': hp.quniform('w3', 0, 1, 0.05),
        'w2': hp.quniform('w2', 0, 1, 0.05),
        'w4': hp.quniform('w4', 0, 1, 0.05),
        'w5': hp.quniform('w5', 0, 1, 0.05),
        'w6': hp.quniform('w6', 0, 1, 0.05),
        'w7': hp.quniform('w7', 0, 1, 0.05),
        'w8': hp.quniform('w8', 0, 1, 0.05),
        'w9': hp.quniform('w9', 0, 1, 0.05),
        'w10': hp.quniform('w10', 0, 1, 0.05),
        'w11': hp.quniform('w11', 0, 1, 0.05),
        'w12': hp.quniform('w12', 0, 1, 0.05),
        'w13': hp.quniform('w13', 0, 1, 0.05),
        'w14': hp.quniform('w14', 0, 1, 0.05),
        'w15': hp.quniform('w15', 0, 1, 0.05),
        'w16': hp.quniform('w16', 0, 1, 0.05),
        'w17': hp.quniform('w17', 0, 1, 0.05),
        'w18': hp.quniform('w18', 0, 1, 0.05),
        'w19': hp.quniform('w19', 0, 1, 0.05),
        'w20': hp.quniform('w20', 0, 1, 0.05),
        'w21': hp.quniform('w21', -1, 1, 0.05),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 1,
        'w4': 1,
        'w5': 1,
        'w6': 1,
        'w7': 1,
        'w8': 1,
        'w9': 1,
        'w10': 1,
        'w11': 1,
        'w12': 1,
        'w13': 1,
        'w14': 1,
        'w15': 1,
        'w16': 1,
        'w17': 1,
        'w18': 1,
        'w19': 1,
        'w20': 1,
        'w21': -1,
    }]
    # objective

    trial_name = f"tuning/{HYO_NAME}.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=2000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(2000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
