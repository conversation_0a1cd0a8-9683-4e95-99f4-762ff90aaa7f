import logging
import os
import pickle
import re
import warnings

import numpy as np
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.redis_cache import EvalRedis

logging.basicConfig(filename='monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime, timedelta
from core_utils.base_eval import TickerEval, PreProcess
from core_utils.constant import JOBLIB_CACHE_DIR

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def eval_filter_all_v2(dictFilter, CUTLOSS=1):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS, cache_service=redis_cache)
            res_s = eval_ticker.eval_short_sell()

            return res_s
        except Exception as error:
            # print(f"Error: {ticker}: {error}")
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 1
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    try:
        lres = [res for res in lres if res is not None and not res.empty]

        if not lres:
            raise ValueError("No valid results to concatenate")

        pd_short = pd.concat(lres, axis=0).reset_index(drop=True)
        if pd_short.shape[0] < 200:
            print(f'deal: {pd_short.shape[0]}')
            raise ValueError("Insufficient data")

        # Process dataframe
        df_process = PreProcess()
        pd_short = df_process.shortsell(pd_short)

        # short deal dataframe
        _pds_d = df_process.group_by(pd_short, ['filter'])
        _pdd_q = df_process.group_by(pd_short, ['filter', "quarter"])
        pd_histogram_quarter = {}
        for f in _pdd_q['filter'].unique():
            data = _pdd_q[_pdd_q['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Hold_Win', 'count_hold_loss': 'Hold_Loss',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Hold', 'Hold_Win', 'Hold_Loss', 'Cutloss']]

        _pds_d['win_quarter'] = 0
        _pds_d['winblock_8quarter'] = 0

        for filter, df in pd_histogram_quarter.items():
            # win_quarter
            win = df[(df['Win'] + df['Hold_Win']) > (
                    df['Loss'] + df['Cutloss'] + df['Hold_Loss'])].shape[0] / df.shape[0]
            _pds_d.loc[_pds_d['filter'] == filter, 'win_quarter'] = win

            # winblock_8quarter
            df_tail = df.tail(8)
            win_tail = df_tail[(df_tail['Win'] + df_tail['Hold_Win']) > (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Hold_Loss'])].shape[0] / df_tail.shape[0]

            _pds_d.loc[_pds_d['filter'] == filter, 'winblock_8quarter'] = win_tail

        profit_expected = _pds_d['profit'].values
        win_deal = (_pds_d['count_win'].astype(int) / _pds_d['deal'].astype(int)) * 100
        deal = _pds_d['deal'].values

        win_quarter = _pds_d['win_quarter'].values * 100
        win_8_quarter = _pds_d['winblock_8quarter'].values * 100

        penalty = 1
        # P1W, P2W, P3W, P1M, P2M, P3M, P6M
        px = [_pds_d['P1W'].values[0], _pds_d['P2W'].values[0], _pds_d['P3W'].values[0], _pds_d['P1M'].values[0],
              _pds_d['P2M'].values[0], _pds_d['P3M'].values[0], _pds_d['P6M'].values[0]]
        px_count = [x for x in px if x > 0]
        penalty *= max(len(px_count), 1) / 7

        # windeal, win_quarter
        if (win_quarter[0] < 50) or (win_8_quarter[0] < 50) or (win_deal[0] < 50):
            penalty *= np.min([win_quarter[0], win_8_quarter[0], win_deal[0]]) / 50
            print(
                f"win_quarter: {_pds_d['win_quarter'].values[0]}, winblock_8quarter: {_pds_d['winblock_8quarter'].values[0]}, windeal: {win_deal[0]}")

        logging.info(
            f"win_quarter: {_pds_d['win_quarter'].values[0]}, winblock_8quarter: {_pds_d['winblock_8quarter'].values[0]}, windeal: {win_deal[0]}")
        logging.info(
            f"P1W: {_pds_d['P1W'].values[0]}, P2W: {_pds_d['P2W'].values[0]}, P3W: {_pds_d['P3W'].values[0]}, P1M: {_pds_d['P1M'].values[0]}, P2M: {_pds_d['P2M'].values[0]}, P3M: {_pds_d['P3M'].values[0]}, P6M: {_pds_d['P6M'].values[0]}")

        print(
            f"P1W: {_pds_d['P1W'].values[0]}, P2W: {_pds_d['P2W'].values[0]}, P3W: {_pds_d['P3W'].values[0]}, P1M: {_pds_d['P1M'].values[0]}, P2M: {_pds_d['P2M'].values[0]}, P3M: {_pds_d['P3M'].values[0]}, P6M: {_pds_d['P6M'].values[0]}")

        if profit_expected[0] > 0:
            penalty *= max(len(px_count), 1) / 7
        else:
            penalty /= max(len(px_count), 1) / 7

        return {
            'profit_expected': profit_expected[0],
            'win_deal': win_deal[0],
            'deal': deal[0],
            'penalty': penalty,
            'status': STATUS_OK
        }

    except:
        return {
            'profit_expected': np.inf,
            'win_deal': np.inf,
            'deal': 1,
            'penalty': -1,
            'status': STATUS_OK
        }


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        # w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (PB > {w1}*PB_MA5Y + {w2}*PB_SD5Y) & (NP_P0 /NP_P1 < {w3})  &  (Close < {w4}*Res_1Y)  & (Volume  > {w5}*Volume_3M_P50)  & (Close_T1W > {w6}*VAP1M)  & (D_RSI > {w7})",
        }
        logging.info(filter)

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * win_deal * penalty / 1000

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.85, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.85, 1.3, 0.01),
        'w3': hp.quniform('w3', 0.65, 1, 0.01),
        'w4': hp.quniform('w4', 0.85, 1.3, 0.01),
        'w5': hp.quniform('w5', 1.2, 2.2, 0.01),
        'w6': hp.quniform('w6', 0.85, 1.3, 0.01),
        'w7': hp.quniform('w7', 0.1, 0.5, 0.01),
    }

    init_vals = [{
        'w1': 1,  # Hệ số nhân của PB_MA5Y
        'w2': 1,  # Hệ số nhân của PB_SD5Y
        'w3': 0.85,  # Tỷ lệ NP_P0 / NP_P1
        'w4': 0.96,  # Hệ số nhân của Res_1Y
        'w5': 1.4,  # Hệ số nhân của Volume_3M_P50
        'w6': 1,  # Hệ số nhân của VAP1M
        'w7': 0.32,  # Giá trị ngưỡng của D_RSI
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_SellResistance1Y.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
