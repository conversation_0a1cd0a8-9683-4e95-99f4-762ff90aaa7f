import logging
import os
import pickle
import sys
import warnings
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.base_eval import TickerEval, PreProcess
from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()

def eval_filter_all_v2(dictFilter, CUTLOSS=1, skip=None):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    if skip is None:
        skip = []

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS, cache_service=redis_cache)
            res_s = eval_ticker.eval_short_sell()
            return res_s
        except Exception as error:
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    try:
        lres = [res for res in lres if res is not None and not res.empty]

        if not lres:
            raise ValueError("No valid results to concatenate")

        pd_short = pd.concat(lres, axis=0).reset_index(drop=True)
        if pd_short.shape[0] < 200:
            print(f'deal: {pd_short.shape[0]}')
            raise ValueError("Insufficient data")

        # Process dataframe
        df_process = PreProcess()
        pd_short = df_process.shortsell(pd_short)

        # short deal dataframe
        _pds_d = df_process.group_by(pd_short, ['filter'])
        _pdd_q = df_process.group_by(pd_short, ['filter', "quarter"])
        pd_histogram_quarter = {}
        for f in _pdd_q['filter'].unique():
            data = _pdd_q[_pdd_q['filter'] == f]
            data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                                 'count_hold_win': 'Hold_Win', 'count_hold_loss': 'Hold_Loss',
                                 'count_cutloss': 'Cutloss'}, inplace=True)

            pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Hold', 'Hold_Win', 'Hold_Loss', 'Cutloss']]

        _pds_d['win_quarter'] = 0
        _pds_d['winblock_8quarter'] = 0

        for filter, df in pd_histogram_quarter.items():
            # win_quarter
            win = df[(df['Win'] + df['Hold_Win']) > (
                    df['Loss'] + df['Cutloss'] + df['Hold_Loss'])].shape[0] / df.shape[0]
            _pds_d.loc[_pds_d['filter'] == filter, 'win_quarter'] = win

            # winblock_8quarter
            df_tail = df.tail(8)
            win_tail = df_tail[(df_tail['Win'] + df_tail['Hold_Win']) > (
                    df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Hold_Loss'])].shape[0] / df_tail.shape[0]

            _pds_d.loc[_pds_d['filter'] == filter, 'winblock_8quarter'] = win_tail

        profit_expected = _pds_d['profit'].values
        win_deal = (_pds_d['count_win'].astype(int) / _pds_d['deal'].astype(int)) * 100
        deal = _pds_d['deal'].values

        win_quarter = _pds_d['win_quarter'].values * 100
        win_8_quarter = _pds_d['winblock_8quarter'].values * 100

        penalty = 1
        # P1W, P2W, P3W, P1M, P2M, P3M, P6M
        px = [_pds_d['P1W'].values[0], _pds_d['P2W'].values[0], _pds_d['P3W'].values[0], _pds_d['P1M'].values[0],
              _pds_d['P2M'].values[0], _pds_d['P3M'].values[0], _pds_d['P6M'].values[0]]
        px_count = [x for x in px if x > 0]
        penalty *= max(len(px_count), 1) / 7

        # windeal, win_quarter
        if (win_quarter[0] < 50) or (win_8_quarter[0] < 50) or (win_deal[0] < 50):
            penalty *= np.min([win_quarter[0], win_8_quarter[0], win_deal[0]]) / 50
            print(
                f"win_quarter: {_pds_d['win_quarter'].values[0]}, winblock_8quarter: {_pds_d['winblock_8quarter'].values[0]}, windeal: {win_deal[0]}")

        logging.info(
            f"win_quarter: {_pds_d['win_quarter'].values[0]}, winblock_8quarter: {_pds_d['winblock_8quarter'].values[0]}, windeal: {win_deal[0]}")
        logging.info(
            f"P1W: {_pds_d['P1W'].values[0]}, P2W: {_pds_d['P2W'].values[0]}, P3W: {_pds_d['P3W'].values[0]}, P1M: {_pds_d['P1M'].values[0]}, P2M: {_pds_d['P2M'].values[0]}, P3M: {_pds_d['P3M'].values[0]}, P6M: {_pds_d['P6M'].values[0]}")

        print(
            f"P1W: {_pds_d['P1W'].values[0]}, P2W: {_pds_d['P2W'].values[0]}, P3W: {_pds_d['P3W'].values[0]}, P1M: {_pds_d['P1M'].values[0]}, P2M: {_pds_d['P2M'].values[0]}, P3M: {_pds_d['P3M'].values[0]}, P6M: {_pds_d['P6M'].values[0]}")

        if profit_expected[0] > 0:
            penalty *= max(len(px_count), 1) / 7
        else:
            penalty /= max(len(px_count), 1) / 7

        return {
            'profit_expected': profit_expected[0],
            'win_deal': win_deal[0],
            'deal': deal[0],
            'penalty': penalty,
            'status': STATUS_OK
        }

    except:
        return {
            'profit_expected': np.inf,
            'win_deal': np.inf,
            'deal': 1,
            'penalty': -1,
            'status': STATUS_OK
        }

class SellPatternTuner:
    def __init__(self):
        self.patterns = {
            'MA21': {
                'search_space': {
                    'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
                    'w2': hp.quniform('w2', 0.8, 1.3, 0.01),
                    'w3': hp.quniform('w3', 0.8, 1.3, 0.01),
                    'w4': hp.quniform('w4', 0.8, 1.3, 0.01),
                    'w5': hp.quniform('w5', -15, 10, 1),
                    'w6': hp.quniform('w6', 0.8, 1.3, 0.01),
                },
                'init_vals': [{
                    'w1': 1,
                    'w2': 1,
                    'w3': 0.95,
                    'w4': 1,
                    'w5': 0,
                    'w6': 0.95
                }],
                'filter_template': "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (MA20/MA50<{w1}) & (MA20_T1/MA50_T1>{w2})  &  (D_RSI/D_RSI_T1W < {w3}) & (Close < {w4}*VAP1M)  & (D_MACDdiff< {w5}) & (Close/Close_T1W < {w6})",
                'skip': ['MZG', 'RYG', 'ECO', 'AIG']
            },
            'MA31': {
                'search_space': {
                    'w1': hp.quniform('w1', 0.85, 1.2, 0.01),
                    'w2': hp.quniform('w2', 0.85, 1.2, 0.01),
                    'w3': hp.quniform('w3', 0.85, 1.2, 0.01),
                    'w4': hp.quniform('w4', 0.8, 1.05, 0.01),
                    'w5': hp.quniform('w5', 0.8, 1.05, 0.01),
                    'w6': hp.quniform('w6', 0.2, 0.7, 0.01),
                    'w7': hp.quniform('w7', -15, 10, 1),
                },
                'init_vals': [{
                    'w1': 1,
                    'w2': 1,
                    'w3': 0.98,
                    'w4': 0.95,
                    'w5': 0.95,
                    'w6': 0.5,
                    'w7': 0
                }],
                'filter_template': "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (MA10/MA200<{w1}) & (MA10_T1/MA200_T1>{w2}) & (Close < {w3}*VAP3M)  & (Close/Close_T1W < {w4})& (D_RSI/D_RSI_T1W < {w5}) & (D_RSI < {w6}) & (D_MACDdiff< {w7})",
                'skip': ['ECO', 'AIG', 'MZG', 'RYG', 'TT6', 'TSA', 'AVG', 'BGE']
            },
            # Thêm các pattern khác tương tự
        }

    def objective(self, params, pattern_name):
        pattern = self.patterns[pattern_name]
        skip = pattern['skip']
        
        filter = {
            "_PS1": "Price < 0",
            "~SellPattern": pattern['filter_template'].format(**params)
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}

    def tune_pattern(self, pattern_name, max_evals=1000):
        if pattern_name not in self.patterns:
            raise ValueError(f"Pattern {pattern_name} not found")

        pattern = self.patterns[pattern_name]
        trial_name = f"tuning/trials_{pattern_name}.pkl"

        if os.path.exists(trial_name):
            trials = pickle.load(open(trial_name, "rb"))
        else:
            trials = generate_trials_to_calculate(pattern['init_vals'])

        best = fmin(
            fn=lambda params: self.objective(params, pattern_name),
            space=pattern['search_space'],
            algo=tpe.suggest,
            max_evals=max_evals,
            trials=trials,
            rstate=np.random.default_rng(42),
            early_stop_fn=no_progress_loss(700),
            trials_save_file=trial_name
        )
        print(f"Best parameters for {pattern_name}:", best)
        return best

    def tune_multiple_patterns(self, pattern_names, max_evals=1000):
        results = {}
        for pattern_name in pattern_names:
            print(f"\nTuning pattern: {pattern_name}")
            results[pattern_name] = self.tune_pattern(pattern_name, max_evals)
        return results

if __name__ == "__main__":
    tuner = SellPatternTuner()
    
    # Tune một pattern
    # tuner.tune_pattern('MA21')
    
    # Tune nhiều pattern
    patterns_to_tune = ['MA21', 'MA31']
    results = tuner.tune_multiple_patterns(patterns_to_tune)
    print("\nFinal results:", results) 