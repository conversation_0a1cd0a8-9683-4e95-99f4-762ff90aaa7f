import os
import re
import warnings

import numpy as np
from pathos.multiprocessing import ProcessingPool as Pool
from tabulate import tabulate
import time

warnings.simplefilter(action='ignore')
import pandas as pd
from report.utils import get_indicator, eval_lookback_filter, filter_functions
from datetime import datetime

FPATH = 'ticker_v1a/'


def find_sell(df_price, list_buy_index, list_sell_index, list_sell_reason, cutloss=0.15):
    """
    Find sell for pair (buy_pattern - sell reason)
    Arguments:
    - df_price: data frame with columns ymd, Close, Open
    - list_buy_index: list of Buy indexes in df_price
    - list_sell_index: list of Sell indexes in df_price
    - list_sell_reason: list of sell reasons
    - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
    Output:
    - list_deal_index: list of first buy indexes for each deal
    - list_deal_buy_price:  list of buying price (open price of T+1)
    - list_deal_sell_index: list of first sell indexes for each deal
    - list_deal_sell_price: list of selling price (open price of T+1)
    - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
    - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
    """
    # Initialize signal list with "none"
    list_signal = [None] * df_price.shape[0]

    # Mark buy signals
    for i in list_buy_index:
        list_signal[i] = "buy"

    # Mark sell signals with corresponding reasons
    for i, j in zip(list_sell_index, list_sell_reason):
        list_signal[i] = j

    # Initialize output lists
    list_deal_index = []
    list_deal_buy_price = []
    list_deal_sell_index = []
    list_deal_sell_price = []
    list_deal_profit = []
    list_deal_result = []
    list_deal_market_price = []

    current_status = None  # Status can be None or "buy"

    for i in range(df_price.shape[0]):
        close = df_price['Close'].iloc[i]
        price = df_price['Open'].iloc[i + 1] if (i < df_price.shape[0] - 1) else close

        if current_status is None:  # Look for the buy signal
            if list_signal[i] != "buy":
                continue
            if i < df_price.shape[0] - 1:  # Do not evaluate deal happens today
                list_deal_index.append(i)
                list_deal_buy_price.append(price)
                current_status = "buy"

        elif current_status == "buy":  # Look for the cutloss or sell signal

            if i == df_price.shape[0] - 1:  # Reaching the end
                list_deal_sell_index.append(i)
                list_deal_sell_price.append(price)
                list_deal_result.append("hold")
                list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
                current_status = None
                list_deal_market_price.append(
                    df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0)
                continue

            if i < list_deal_index[-1] + 3:
                continue

            if close < list_deal_buy_price[-1] * (1 - cutloss):  # Cutloss
                list_deal_sell_index.append(i)
                list_deal_sell_price.append(price)
                list_deal_result.append("cutloss")
                list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
                current_status = None
                list_deal_market_price.append(
                    df_price['VNINDEX'].iloc[i + 1] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0)
                continue

            if list_signal[i] != "buy" and list_signal[i] is not None:  # Sell signal
                list_deal_sell_index.append(i)
                list_deal_sell_price.append(price)
                list_deal_result.append(list_signal[i])
                list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
                current_status = None
                list_deal_market_price.append(
                    df_price['VNINDEX'].iloc[i + 1] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0)

    return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
            list_deal_result, list_deal_market_price)


def parse_start_time(dict_filter):
    dates = ['2020-01-01']
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    return min_date_str


def simulation(deals, start_date='2017-01-01', initial_assets=1e8, max_deals=10):
    """
    Simulate trading strategy
    """
    available_assets = initial_assets
    deals = deals.copy().sort_values('time', ascending=True)
    active_deals = []
    completed_deals = []
    miss_deals = []
    profit = 0
    # time_in_market
    start_time = []
    end_time = []

    for i, deal in deals.iterrows():
        # process complete deal
        keep_deals = []
        sell_time_remove = []
        for j, active_deal in enumerate(active_deals[:]):
            if active_deal['sell_time'] <= deal['time']:
                profit += active_deal['profit']
                # for tax
                available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.998
                completed_deals.append(active_deal)
                sell_time_remove.append(active_deal['sell_time'])
            else:
                keep_deals.append(active_deal)

        active_deals = keep_deals.copy()

        # end - time_in_market
        if (not active_deals) and sell_time_remove:
            end_time.append(max(sell_time_remove))

        # Check the budget for the new deal
        if len(active_deals) < max_deals:
            investment_amount = (available_assets / (max_deals - len(active_deals)))
            deal['investment_amount'] = investment_amount
            # for tax
            available_assets -= investment_amount * 1.001

            # start - time_in_market
            if not active_deals:
                start_time.append(deal['time'])
            active_deals.append(deal)

        else:
            miss_deals.append(deal)

    for active_deal in (active_deals[:]):
        profit += active_deal['profit']
        available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100))
        completed_deals.append(active_deal)

    today = datetime.today().strftime('%Y-%m-%d')
    if active_deals:
        end_time.append(today)
    time_in_market = sum(
        [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
    total_time = (pd.to_datetime(today) - pd.to_datetime(start_date)).days
    return {
        'match_deals': len(completed_deals),
        'total_time': total_time,
        'time_in_market': time_in_market,
        'profit': profit / len(completed_deals),
        'cash_profit': ((available_assets / initial_assets) - 1) * 100,
        'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
        'available_assets': available_assets,
        'completed_deals': pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame(),
        'miss_deals': pd.concat(miss_deals, axis=1).T if miss_deals else pd.DataFrame(),
    }


def first_eval(pdxx, dictFilter, CUTLOSS=0.15):
    """
    Evaluate filter for a ticker
    Input:
        pdxx: ticker daily series
        dictFilter: dictionary of filters
        CUTLOSS: cutloss threshold
    Output:
        pdy: dataframe of latest hit
    """
    pd_all = pdxx.copy()

    for f in dictFilter:
        if f.startswith("*"):
            pd_all[f[1:]] = get_indicator(f[1:], pd_all)

    # Apply sell filters
    s_cols = ['time', 'ticker', 'Close', 'Volume', 'Sell_filter']
    now = pd_all.iloc[-1:].copy()
    now['Sell_filter'] = 'Hold'
    sell_data = [now]
    for f in dictFilter:
        if f.startswith("~") and (f in filter_functions):
            sell_data.append(eval_lookback_filter(f, pd_all))

    for f in dictFilter:
        if f.startswith('~'):
            pd_Sell_filtered = pd_all.query(f'({dictFilter[f]})').copy()
            pd_Sell_filtered['Sell_filter'] = f[1:]
            sell_data.append(pd_Sell_filtered)

    pd_sell = pd.concat(sell_data, axis=0).sort_values('time', ascending=True)
    pd_sell = pd_sell[s_cols]
    s_filters = pd_sell['Sell_filter'].unique()

    # Apply buy filters
    b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Volume', 'P1W', 'P1M', 'P3M', 'P1Y', 'P2Y']
    buy_data = []
    for f in dictFilter:
        if f.startswith("_") and (f in filter_functions):
            buy_data.append(eval_lookback_filter(f, pd_all))

    for f in dictFilter:
        if not (f.startswith('_')) or (f in filter_functions):
            continue
        pd_buy_filtered = pd_all.query(f'({dictFilter[f]})').copy()
        pd_buy_filtered['filter'] = f[1:]
        buy_data.append(pd_buy_filtered)
    _buy_data = [data for data in buy_data if not data.empty]
    pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]

    for period in ['1W', '1M', '3M', '1Y', '2Y']:
        pd_buy[f"P{period}"] = 100. * (pd_buy[f"C{period}"] - 1) * (
                pd_buy[f"L{period}"] > 1 - CUTLOSS) + (-100. * CUTLOSS) * (pd_buy[f"L{period}"] <= 1 - CUTLOSS)
    pd_buy['hit'] = 1
    pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])
    pd_buy = pd_buy[b_cols]

    return pd_sell, pd_buy


def first_eval_all(dictFilter, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            res = first_eval(pdxx, dictFilter, CUTLOSS=CUTLOSS)
            return res
        except Exception as e:
            print(f"Error: {ticker}: {e}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 15
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    pd_buy = pd.concat([res[1] for res in lres if res is not None and not res[1].empty], axis=0)
    pd_sell = pd.concat([res[0] for res in lres if res is not None and not res[0].empty], axis=0)

    return pd_sell, pd_buy


def eval_filter_ticker_v3(pdxx, pd_sell, pd_buy, dictFilter, CUTLOSS=0.1):
    """
    Evaluate filter for a ticker
    Input:
        pdxx: ticker daily series
        dictFilter: dictionary of filters
        CUTLOSS: cutloss threshold
    Output:
        pdy: dataframe of latest hit
    """
    pd_all = pdxx.copy()
    ticker = pd_all['ticker'].iloc[0]
    pd_buy = pd_buy[pd_buy['ticker'] == ticker]
    pd_sell = pd_sell[pd_sell['ticker'] == ticker]
    # Apply sell filters
    s_cols = ['time', 'ticker', 'Close', 'Volume', 'Sell_filter']
    sell_data = [pd_sell]

    for f in dictFilter:
        if f.startswith('~'):
            pd_Sell_filtered = pd_all.query(f'({dictFilter[f]})').copy()
            pd_Sell_filtered['Sell_filter'] = f[1:]
            sell_data.append(pd_Sell_filtered)

    pd_sell = pd.concat(sell_data, axis=0).sort_values('time', ascending=True)
    pd_sell = pd_sell[s_cols]
    # s_filters = pd_sell['Sell_filter'].unique()

    # Evaluate by deal
    sell_indexes = list(pd_sell.index)
    sell_reasons = list(pd_sell['Sell_filter'])
    buy_reasons = list(pd_buy['filter'].unique())

    deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                 'profit_vni']
    l_pd_deal = []
    for buy_reason in buy_reasons:
        buy_reason_indexes = list(pd_buy[pd_buy['filter'] == buy_reason].index)
        result = find_sell(pd_all[['time', 'Close', 'Open', 'VNINDEX']], buy_reason_indexes, sell_indexes, sell_reasons,
                           CUTLOSS)
        deal_time = pd_all.loc[result[0]]['time'].values
        ticker = pd_all.loc[result[0]]['ticker'].values
        deal_sell_time = pd_all.loc[result[2]]['time'].values
        pd_deal_pa = pd.DataFrame({'ticker': ticker,
                                   'time': deal_time,
                                   'buy_price': result[1],
                                   'sell_price': result[3],
                                   'profit': result[4],
                                   'sell_filter': result[5],
                                   'sell_time': deal_sell_time,
                                   'profit_vni': result[6],
                                   })
        pd_deal_pa['filter'] = buy_reason

        l_pd_deal.append(pd_deal_pa)
    pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

    return pd_deal


def eval_filter_all_v3(pd_sell, pd_buy, dictFilter, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            res = eval_filter_ticker_v3(pdxx, pd_sell.copy(), pd_buy.copy(), dictFilter, CUTLOSS=CUTLOSS)
            return res
        except Exception as e:
            print(f"Error: {ticker}: {e}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    #     lres.append(res)

    num_procs = 15
    lres = []
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    # Process dataframe
    pd_deal = pd.concat([res for res in lres if res is not None and not res.empty], axis=0).reset_index(drop=True)

    dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'count_cutloss': 'count', 'n_month': 'sum',
            'count_hold': 'count', 'count_win': 'count', 'count_loss': 'count', 'count_sell': 'count',
            'n_quarter': 'sum', 'sum_profit': 'sum',
            }

    # pd_deal
    pd_deal['profit'] = pd_deal['profit'] * 100
    pd_deal['profit_vni'] = pd_deal['profit_vni'] * 100
    for idx, result in enumerate(pd_deal['sell_filter'].values):
        if f'p_{result}' not in pd_deal.columns:
            pd_deal[f'p_{result}'] = np.nan
        pd_deal.loc[idx, f'p_{result}'] = pd_deal.loc[idx, 'profit']
    p_sell_columns = [f for f in pd_deal.columns if (f.startswith('p_') and f not in ['p_cutloss', 'p_hold'])]
    pd_deal["p_sell_pattern"] = pd.concat([pd_deal[p_sell_column].dropna() for p_sell_column in p_sell_columns])

    pd_deal['count_hold'] = pd_deal['p_hold'].copy()
    if "p_hold" not in pd_deal.columns:
        pd_deal['p_hold'] = 0
    if "p_cutloss" not in pd_deal.columns:
        pd_deal['p_cutloss'] = 0

    pd_deal['p_win'] = pd_deal['p_sell_pattern'].agg(lambda x: x if x > 0 else np.nan)
    pd_deal['p_loss'] = pd_deal['p_sell_pattern'].agg(lambda x: x if x <= 0 else np.nan)

    pd_deal['count_win'] = pd_deal['p_win'].copy()
    pd_deal['count_loss'] = pd_deal['p_loss'].copy()
    pd_deal['count_hold'] = pd_deal['p_hold'].copy()
    pd_deal['count_cutloss'] = pd_deal['p_cutloss'].copy()
    pd_deal['holding_period'] = (pd.to_datetime(pd_deal['sell_time']) - pd.to_datetime(pd_deal['time'])).dt.days
    pd_deal['deal'] = 1
    for ticker in pd_deal['ticker'].unique():
        pd_deal.loc[pd_deal['ticker'] == ticker, 'corr'] = pd_deal[pd_deal['ticker'] == ticker]['profit'].corr(
            pd_deal[pd_deal['ticker'] == ticker]['profit_vni'])

    # deals eval
    _pdd = pd_deal.groupby(['filter'], as_index=False) \
        .agg({f: dAgg.get(f, 'mean') for f in list(pd_deal.columns)
              if (f not in ['filter', 'ticker', 'time', 'Close', 'sell_time', 'sell_filter']) and (
                      "time" not in f)})
    # Simulation deal
    result = {}
    start_date = parse_start_time(dictFilter)
    for b_pattern in pd_deal['filter'].unique():
        result[b_pattern] = simulation(pd_deal[pd_deal['filter'] == b_pattern], start_date)

    _pdd[["si_deals", "si_total_time", "si_time_in_market, si_cash_profit, si_profit", "si_return"]] = np.nan
    for pattern, value in result.items():
        _pdd.loc[_pdd['filter'] == pattern, 'si_deals'] = value['match_deals']
        _pdd.loc[_pdd['filter'] == pattern, 'si_total_time'] = value['total_time']
        _pdd.loc[_pdd['filter'] == pattern, 'si_time_in_market'] = value['time_in_market']
        _pdd.loc[_pdd['filter'] == pattern, 'si_cash_profit'] = value['cash_profit']
        _pdd.loc[_pdd['filter'] == pattern, 'si_profit'] = value['profit']
        _pdd.loc[_pdd['filter'] == pattern, 'si_return'] = value['return']

    pdd = _pdd[['filter', 'deal']].copy()
    pdd['%win_deal'] = (_pdd['count_win'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%loss_deal'] = (_pdd['count_loss'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%hold_deal'] = (_pdd['count_hold'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd["%cutloss_deal"] = (_pdd['count_cutloss'].astype(int) / _pdd['deal'].astype(int)) * 100

    # pdd['profit_win'] = _pdd['p_win'].values
    # pdd['profit_loss'] = _pdd['p_loss'].values
    # pdd['profit_hold'] = _pdd['p_hold'].values
    pdd['profit_cutloss'] = _pdd['p_cutloss'].values
    pdd['holding_period'] = _pdd['holding_period'].values
    pdd['profit_expected'] = _pdd['profit'].values

    # pdd['profit_vni'] = _pdd['profit_vni'].values
    # pdd['corr_deal_vni'] = _pdd['corr'].values
    # pdd['n_half'] = _pdd['n_half']
    # pdd['entropy'] = _pdd['entropy']
    # pdd['si_total_time'] = _pdd['si_total_time']
    # pdd['si_time_in_market'] = _pdd['si_time_in_market']
    pdd['si_deals'] = _pdd['si_deals']
    pdd['si_profit'] = _pdd['si_profit']
    pdd['si_cash_profit'] = _pdd['si_cash_profit']
    pdd['si_return'] = _pdd['si_return']

    return pdd


def append_empty_rows(dataframe, n):
    for _ in range(n):
        dataframe.loc[len(dataframe)] = pd.Series(dtype='float64')


def random_range(start, end, step):
    return round(np.random.choice(list(np.arange(start, end + step, step))), 4)


def random_int(start, end):
    return np.random.randint(start, end + 1)


def random_log(start, end):
    return round(np.random.choice(list(np.logspace(start, end, num=20))), 4)


def get_score(value, origin):
    score = 0

    if value > 10 + origin:
        score += 10
    elif value > 5 + origin:
        score += 5
    elif value > origin:
        score += 2
    elif value == origin:
        score += 1
    elif value < -10 + origin:
        score -= 10
    elif value < -5 + origin:
        score -= 5

    return score


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/test", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    filter = {
        "_T2P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7)  & (C_H1M<0.9)  &  (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=3)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >=1)  &  (M_CMB_LAG <=4)",
        "_T2P5X": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (C_H3M<0.8)  & (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=4)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >0)  &  (M_CMB_LAG <=4)",
        "_TL3M": "(HI_3M_T1/LO_3M_T1<1.3) & (Volume > Volume_3M_P90)& (ROE5Y>0.1) & (PE<20) & (PB < 1.5) & (FSCORE > 4) & (NP_P0 > 1.1*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
        "_PKDM8": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (M_CMB_Step>0)  &  (M_CMB_LEN>=8)  &  (M_CMB_LAG==1)",
        "_T3P4": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R>0)&(PE<10)&(C_H2Y<0.7) & (C_H2Y>0.5)",
        "_T3P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R<0)&(PE<5)&(C_H2Y<0.6)&(C_H2Y>0.3)",
        "_T3P6": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
        "_T3P6V1": "((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
        "_BKMA200": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>480) & (MA50/MA200>1.1) & (MA10/MA200<1.2) & (ROE5Y >0.1) & (PE <20) & (NP_P0 > NP_P1)",
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15)  &  (Close/MA200<1)  &  (Close_T1/MA200_T1>1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        "~MA4": "Close > 1.3*MA200 & (NP_P0 < 0.8*NP_P1)",
        "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"

    }

    result = []
    np.random.seed((os.getpid() * int(time.time())) % 123456789)

    for time in range(50):
        print(time)
        turn_filter = {}
        if time == 0:
            pd_sell, pd_buy = first_eval_all(filter)
        else:
            w1 = random_range(150, 500, 1)
            w2 = random_range(0.8, 1.5, 0.01)
            w3 = random_range(0.8, 1.5, 0.01)
            w4 = random_range(0.09, 0.11, 0.005)
            w5 = random_range(1, 1.4, 0.01)
            turn_filter[
                f"~test"] = f"((C_L3M>={w1}) & (Close < {w2}*MA200) &(M_CMB_Step<0) & (M_CMB_LEN>={w4}) & (M_CMB_LAG>0)& (M_CMB_LAG<={w5})"

        evaluate = eval_filter_all_v3(pd_sell, pd_buy, turn_filter)

        # "~S232": "((C_L3M>=1.2) & (Close < 2*MA200) & (Close_1W > MA200))&(Close<= MA200)&(Close_1W > MA200)",
        # S232 = f"((C_L3M>={w1}) & (Close < {w2}*MA200) & (Close_1W > {w3}*MA200))",
        # (C_L3M >= 1.2) & (Close < 3 * MA200) & (Close >= 2 * MA200)

        # turn_filter = {
        #     "_test": f"((Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>{w1}) & (MA50/MA200>{w2}) & (MA10/MA200<{w3}) & (ROE5Y >0.1) & (PE <20) & (NP_P0 > NP_P1)",
        # "~S231": f"((C_L3M>={w1}) & (Close < {w2}*MA200) &(M_CMB_Step<0) & (M_CMB_LEN>={w4}) & (M_CMB_LAG>0)& (M_CMB_LAG<={w5})"
        # "_TL3M": f"((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (HI_3M_T1/LO_3M_T1<{w1}) & (Volume > {w2}*Volume_3M_P90)& (ROE5Y>{w3}) & (PE<{w4}) & (PB < {w5}) & (FSCORE > 4) & (NP_P0 > {w6}*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
        # "~S232": f"((C_L3M>={w1}) & (Close < {w2}*MA200) & (Close_1W > {w3}*MA200))",
        # "~S221": f"(C_L3M>={w1})&(Close < {w2}*MA200) & (Close >= {w3}*MA200) &  (W_CMB_Step<0) & (W_CMB_LEN>={w4}) & (W_CMB_LAG>0)& (W_CMB_LAG<={w5}) ",
        # "~test": f"(C_L3M>={w1})&(Close > {w2}*MA200) & (D_CMB_Step<0) & (D_CMB_LEN>={w4}) & (D_CMB_LAG>0)& (D_CMB_LAG<={w5})"
        # }
        evaluate.insert(0, 'test_filter', turn_filter.get('~test'))
        append_empty_rows(evaluate, 1)

        # evaluate['score'] = 0
        # # Ranking
        # sum_score = 0
        # for i in range(evaluate.shape[0]):
        #     score = 0
        #     score += get_score(evaluate['%win_deal'].iloc[i], evaluate['origin_win_deal'].iloc[i])
        #     score += get_score(evaluate['profit_expected'].iloc[i], evaluate['origin_profit_expected'].iloc[i])
        #
        #     evaluate['score'].iloc[i] = score
        #     sum_score += score
        #
        # evaluate['score'].iloc[-1] = sum_score

        result.append(evaluate)

    result = pd.concat(result)
    output_path = 'test/temp/find_sell_pattern_s231.csv'
    result.to_csv(output_path, mode='a', header=not os.path.exists(output_path), index=False)
