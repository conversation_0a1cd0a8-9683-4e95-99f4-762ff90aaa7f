import os

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathos.multiprocessing import ProcessingPool as Pool
from IPython.core.display_functions import display

from tuning.eval_utils import TickerEval
from tuning.utils import get_bin_stats_2d, get_bin_stats
import seaborn as sns


def label_data(df):
    """
    Label data to identify bottoms as described.
    """
    df['Label'] = 0
    for i in range(5, len(df) - 5):
        if df['Low'][i] == df['Low'][i - 2:i + 3].min() and df['Low'][i] == df['Low'][i - 5:i + 6].min():
            df.at[df.index[i], 'Label'] = 1
    return df


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]
    # iV = ((pdxy['Volume'] * pdxy['Price']).rolling(20).mean() > 500e6)
    pdxy[cname_tvt] = 'other'
    iA = pdxy['time'] < YMD_test
    iB = ~iA
    iTest = pdxy['ticker'].isin(list_ticker_test)
    iTrain = ~iTest
    pdxy.loc[iA & iTrain, cname_tvt] = 'train'
    pdxy.loc[iB & iTrain, cname_tvt] = 'val'
    pdxy.loc[iTest, cname_tvt] = 'test'
    # pdxy.loc[iV & iA & iTest, cname_tvt] = 'test1'
    # pdxy.loc[iV & iB & iTest, cname_tvt] = 'test2'
    return pdxy


def handle_input_data(pdxy, len_series):
    pdxy = pdxy.copy()
    pdxy["x"] = np.nan
    pdxy['x'] = pdxy['x'].astype('object')
    for i in range(len_series, pdxy.shape[0]):
        pdxy.at[i, "x"] = list((pdxy['Close'][i - len_series: i]).values)
    return pdxy["x"]


def prepare_pdxy(list_processed_ticker, in_folder='ticker_v1a/'):
    ## combine all ticker data into one file
    ll = []
    for ticker in list_processed_ticker:
        pd_xy = load_data(f'{in_folder}/{ticker}.csv', start=2014)
        pd_xy['Close_T20'] = handle_input_data(pd_xy, 20)
        pd_xy['min_T20'] = pd_xy['Close'].rolling(20).min()
        pd_xy['EMA'] = pd_xy['Close'].ewm(span=20, adjust=False).mean()
        # Bollinger Bands
        std = pd_xy['Close'].rolling(20).std()
        pd_xy['BB_lower'] = pd_xy['MA20'] - (std * 2)

        for time in range(1, 6):
            col = f"minL{time}"
            pd_xy[col] = pd_xy['Close'].rolling(time).min()
            # pd_xy[col] = pd_xy['Close'].rolling(20).apply(lambda x: 1 if (x.min() == x[20 - 1 - time: 20]).any() else 0)
        for name, w in zip(['minW5D', 'minW10D', 'minW20D'], [11, 21, 41]):
            pd_xy[name] = pd_xy['Close'].rolling(w, center=True).min()
            # pd_xy[name] = pd_xy['Close'].rolling(w, center=True).apply(lambda x: 1 if x[w // 2] == x.min() else 0,
            #                                                            raw=True)

        pd_xy['Y1'] = 0.0
        for i in range(1, pd_xy.shape[0]):
            if pd_xy.at[i, 'minW10D'] == 1:
                pd_xy.at[i, 'Y1'] = 0.8
                pd_xy.at[i + 1, 'Y1'] = 0.9
                pd_xy.at[i + 2, 'Y1'] = 1

        pd_xy['label1'] = np.where(pd_xy['minL1'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label2'] = np.where(pd_xy['minL2'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label3'] = np.where(pd_xy['minL3'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label4'] = np.where(pd_xy['minL4'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label5'] = np.where(pd_xy['minL5'] == pd_xy['minW5D'], 1, 0)

        pd_xy.dropna(subset="Close_T20", inplace=True)
        pd_xy.ffill(inplace=True)
        ll.append(pd_xy)

    pdxy = pd.concat(ll, axis=0, ignore_index=True).reset_index(drop=True)  #
    pdxy = pdxy.dropna(axis=1, how='all')  # drop columns with all NaN

    # pdxy.replace(np.inf, np.nan, inplace=True)
    # pdxy.replace(-np.inf, np.nan, inplace=True)
    # pdxy.dropna(inplace=True)

    cname_feature = [f for f in pdxy.columns if f not in ['time', 'ticker', 'ymd', 'quarter',
                                                          'Open', 'Close', 'High', 'Low', 'Close_TT20',
                                                          'L1W', 'L2W', 'L3w', 'L1M', 'L2M', 'L3M', 'L1Y', 'L2Y', 'H1W',
                                                          'H2W', 'H3W', 'H1M', 'H2M', 'H3M', 'H1Y', 'H2Y', 'C1W', 'C2W',
                                                          'C3W', 'C1M', 'C2M', 'C3M', 'C1Y', 'C2Y']]

    cname_tvt = 'tvt'
    split_tvt_v1(pdxy, cname_tvt)
    list_eval = get_list_eval(pdxy, cname_tvt=cname_tvt)
    list_label = ['minW5D', 'minW10D', 'minW20D', 'minL1', 'minL2', 'minL3', 'minL4', 'minL5', 'label1',
                  'label2', 'label3', 'label4', 'label5', 'Y1']
    return {'pdxy': pdxy,
            'cname_feature': cname_feature,
            'list_label': list_label,
            'list_eval': list_eval,
            }


def eval_filter_all_v2(dictFilter, CUTLOSS=0.1):
    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS)
            res = eval_ticker.get_buy_signals()
            return res
        except Exception as error:
            print(f"Error: {ticker}: {error}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 1
    with Pool(num_procs) as p:
        # lres = p.amap(eval, list_processed_ticker)
        lres = p.map(eval, list_processed_ticker)
        # lres.extend(lres.get())

    lres = [res for res in lres if res is not None and not res.empty]
    pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)

    return pd_deal


filter = {
    "_VolMax5Y": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
    "_VolMax5Y": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>0.972*Volume_Max5Y_High) & (ROE5Y>0.031)&(PE<16)& (NP_P0 > 1.192*NP_P1) & (PE >4.403)&(ID_Current-Volume_Max5Y_ID<=110)&(PB<7.773)",
    "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
    "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
    "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
    "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
    # "~SellMaxVol": f"(Close < Volume_Max5Y_Low) &(ID_Current-Volume_Max5Y_ID<=120)",
    "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"
}

import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning/sell", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)


def p1():
    FPATH = "ticker_v1a/"
    data_path = 'tuning/sell/data.csv'
    duration = 120
    ignore = 2021
    Tx = 3

    df_buy = eval_filter_all_v2(filter)
    # df_xy.to_csv(data_path)
    # df_buy = pd.read_csv(data_path)
    df_buy = df_buy.query(f"time > '{ignore + 1}' | time < '{ignore}'")
    step = int(df_buy.shape[0] / 1000)
    df_buy = df_buy.iloc[::step, :].reset_index(drop=True)

    df_curr_ticker = None
    df_sell = []
    df_temp = []
    for idx_buy, data in df_buy.iterrows():
        ticker = data['ticker']
        if df_curr_ticker is None or ticker != df_curr_ticker['ticker'].values[0]:
            if df_temp:
                df_temp = pd.concat(df_temp, axis=0)
                df_sell.append(df_temp)
            df_curr_ticker = pd.read_csv(f'{FPATH}/{ticker}.csv')
            df_temp = []

        df_curr_ticker['buy_index'] = idx_buy
        start = df_curr_ticker[df_curr_ticker['time'] == data['time']].index[0] + Tx
        end = start + duration if start + duration < df_curr_ticker.shape[0] else df_curr_ticker.shape[0]
        temp = df_curr_ticker.iloc[start:end].copy()
        temp['hold_session'] = pd.DataFrame(range(Tx, temp.shape[0] + Tx)).values
        df_temp.append(temp)

    df_sell = pd.concat(df_sell, axis=0).reset_index(drop=True)
    df_maps = df_sell.groupby(['time', 'ticker'], as_index=False).agg({'buy_index': lambda x: ','.join(x.astype(str))})
    df_sell = df_sell.drop_duplicates(subset=['time', 'ticker'], keep='first').reset_index(drop=True)
    df_sell.drop(columns=['buy_index'], inplace=True)
    df_sell = df_sell.merge(df_maps, on=['time', 'ticker'], how='left')

    df_maps = df_sell[['time', 'ticker', 'buy_index']].copy()
    df_maps['buy_index'] = df_maps['buy_index'].apply(lambda x: x.split(',')).copy()
    df_maps = df_maps.explode('buy_index')
    df_maps['buy_index'] = df_maps['buy_index'].astype(int)
    df_maps['sell_index'] = df_maps.index
    df_maps.sort_values(['buy_index', 'sell_index'], inplace=True)
    df_maps = df_maps[['buy_index', 'sell_index']]
    df_maps.reset_index(drop=True, inplace=True)

    df_maps.merge(df_buy[['buy_index', 'time']], on='buy_index', how='left')
    split_tvt(df_maps, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01')
    get_list_eval(df_maps, cname_tvt='tvt')
    # returns = {'df_buy': df_buy,
    #  'df_sell': df_sell,
    #  'df_maps': df_maps,
    #  'list_eval': list_eval,
    #  }

    list_eval = get_list_eval(df_maps, cname_tvt='tvt')
    dataset = {
        'df_buy': df_buy,
        'df_sell': df_sell,
        'df_maps': df_maps,
        'list_eval': list_eval,
    }


def pattern_label(pd_y):
    sell_filter = {
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        "~SellMaxVol": " (Close < Volume_Max5Y_Low) &(ID_Current-Volume_Max5Y_ID<=120)",
        "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"
    }

    # Apply sell filters
    pd_y['pattern_label'] = 0

    for f in sell_filter:
        if f.startswith('~'):
            try:
                index = pd_y.query(f'({sell_filter[f]})').index
                pd_y.loc[index, 'pattern_label'] = 1
            except Exception as e:
                pass


def extend_columns(df, column):
    if column not in df.columns:
        df[column] = np.nan


def first():
    FPATH = "ticker_v1a/"
    data_path = 'tuning/sell/data.csv'
    duration = 120
    ignore = 2021
    # p1()

    df_buy = pd.read_csv("tuning/sell/df_buy.csv")
    df_sell = pd.read_csv("tuning/sell/df_sell.csv")
    df_map = pd.read_csv("tuning/sell/df_map.csv")
    list_eval = get_list_eval(df_map, cname_tvt='tvt')

    dataset = {
        'df_buy': df_buy,
        'df_sell': df_sell,
        'df_maps': df_map,
        'list_eval': list_eval,
    }

    pattern_cfeature = ['PE', 'MA20', 'MA50', 'MA200', 'MA20_T1', 'MA50_T1', 'Close', 'Close_T1', 'NP_P0', 'NP_P1',
                        'C_L1W', 'D_CMB', 'D_CMB_Peak_T1', 'D_CMB_XFast', 'BVPS', 'VAP1M', 'Volume_Max5Y_Low']

    df_sell['sell_index'] = df_sell.index
    df_buy['buy_index'] = df_buy.index
    df_map = df_map.merge(df_buy[['buy_index'] + pattern_cfeature], on='buy_index', how='left', suffixes=('_x', '_y'))
    df_map = df_map.merge(df_sell[['sell_index', 'time'] + pattern_cfeature], on='sell_index', how='left',
                          suffixes=('_x', '_y'))

    # df_map['holding_time'] = (pd.to_datetime(df_map['time_y']) - pd.to_datetime(df_map['time_x'])).dt.days
    df_map['holding_session'] = 0
    for unit in df_map['buy_index'].unique():
        df_map.loc[df_map['buy_index'] == unit, 'holding_session'] = pd.DataFrame(
            range(3, 3 + len(df_map.loc[df_map['buy_index'] == unit]))).values

    ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt']
    cname_label = ['pattern_label']

    cname_features = [column for column in df_map.columns if column not in (ignore_cols + cname_label)]


import xgboost as xgb


def train_xgboost(pdx, cname_features, cname_label, cname_tvt,
                  params=None):
    ''' pdx : dataframe
        cname_features : list of column names
        cname_label : column name for label (0/1)
        cname_tvt : column name for train, validation, test ('train'/'val'/'test')
    Output:
        model
        pred
    '''
    if params is None:
        params = {'tree_method': 'hist',
                  'max_bin': 256,
                  'max_depth': 7,
                  'min_child_weight': 10,
                  'subsample': .8,
                  'colsample_bytree': .8,
                  'learning_rate': .01,
                  'n_estimators': 1000,
                  'predictor': 'cpu_predictor',
                  'verbosity': 0,
                  'eval_metric': ['error', 'auc'],
                  'early_stopping_rounds': 100,
                  'gamma': .1,
                  'lambda': 1.0,
                  'alpha': 0.5,
                  }
    # split data
    pdx_train = pdx.query(f'{cname_tvt}=="train"')
    pdx_val = pdx.query(f'{cname_tvt}=="val"')

    # train model
    model = xgb.XGBClassifier(**params)
    eval_set = [(pdx_train[cname_features], pdx_train[cname_label]), (pdx_val[cname_features], pdx_val[cname_label])]

    model.fit(pdx_train[cname_features], pdx_train[cname_label], eval_set=eval_set, verbose=params['verbosity'])

    eval_results = {}
    evals_result = model.evals_result()

    # # plot loss error over iteration
    # fig, ax = plt.subplots(figsize=(10, 6))
    # ax.plot(model.evals_result()['validation_0']['error'], label='val')
    # ax.plot(model.evals_result()['training']['logloss'], label='train')
    # ax.set_xlabel('Epoch')
    # ax.set_ylabel('Logloss')
    # ax.set_title('Loss Curve')
    # ax.legend()
    # plt.show()

    # eval

    for data, evals_result in evals_result.items():
        for eval_name, log in evals_result.items():
            eval_results[(data, eval_name)] = log
    # plot auc curve
    fig, ax = plt.subplots(figsize=(10, 6))
    for data, eval_name in eval_results:
        ax.plot(eval_results[(data, eval_name)], label=f'{data}_{eval_name}')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('AUC')
    ax.set_title('AUC Curve')
    ax.legend()
    plt.show()
    # predict
    y_pred = model.predict_proba(pdx[cname_features])[:, 1]

    # best_ntree = model.get_booster().best_ntree_limit
    # if (xgb.__version__ >= '1.4'):
    #     y_pred = model.predict_proba(pdx_test[cname_features], iteration_range=(0, best_ntree))[:, 1]
    # else:
    #     y_pred = model.predict_proba(pdx_test[cname_features], ntree_limit=best_ntree)[:, 1]
    return model, y_pred

def ai_hit(df_pred, threshold):
    # duration = 120
    #
    # # df_buy['buy_index'] = df_buy.index
    #
    # df_hit = []
    # df_all = self.df_all.copy()
    # df_buy = self.df_buy.copy()
    # df_buy['buy_index'] = df_buy.index
    # df_all['sell_index'] = df_all.index
    # df_all['buy_index'] = np.nan
    # for idx_buy, curr_buy in self.df_buy.iterrows():
    #     start = df_all[df_all['time'] == curr_buy['time']].index[0] + 3
    #     end = start + duration if start + duration < df_all.shape[0] else df_all.shape[0]
    #
    #     df_sell = df_all.iloc[start:end].copy()
    #     df_sell['buy_index'] = idx_buy
    #     df_sell.loc[:, 'holding_session'] = list(range(3, 3 + len(df_sell)))
    #     df_sell.loc[:, 'sell_profit'] = ((df_sell['Open_1D'] / curr_buy['Open_1D']) - 1)
    #     df_hit.append(df_sell)
    #     # df_hit.append(df_sell.assign(**curr_buy))
    #
    #
    # df_sell = pd.concat(df_hit, axis=0).reset_index(drop=True)
    #
    # temp_cfeature = list(set([c_f.replace('_x', '').replace('_y', '') for c_f in self.cfeature]))
    # b_cfeature = [f for f in temp_cfeature if f in df_buy.columns]
    # s_cfeature = [f for f in temp_cfeature if f in df_sell.columns]
    # df_hit = df_buy[b_cfeature + ['buy_index', 'time']].merge(df_sell[s_cfeature + ['buy_index', 'time']], on=['buy_index'], how='left', suffixes=('_x', '_y'))

    # Predict
    # ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
    # cname_label = ['pattern_label']
    # df_hit['pred'] = self.model.predict_proba(df_hit[self.cfeature])[:, 1]

    # Find first hit
    df_result = []
    for idx_buy, d_buy in df_buy.iterrows():
        df_label = df_pred.loc[(df_pred['buy_index'] == idx_buy)].copy()

        d = {}
        for i in range(df_label.shape[0]):
            d_sell = df_label.iloc[i]
            if d_sell['pred'] > threshold:
                d = {
                    'ticker': d_sell['ticker'],
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'time': d_sell['time_x'],
                    'sell_time': d_sell['time_x'],
                    'holding_session': d_sell['holding_session'],
                    'profit': d_sell['sell_profit'],
                    'sell_reason': 'ml'
                }
                break

            if i == df_label.shape[0] - 1:
                d = {
                    'ticker': d_sell['ticker'],
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'time': d_sell['time_x'],
                    'sell_time': d_sell['time_x'],
                    'holding_session': d_sell['holding_session'],
                    'profit': d_sell['sell_profit'],
                    'sell_reason': np.nan
                }

        df_result.append(pd.DataFrame(data=d, index=[d_buy['buy_index']]))
    df_result = pd.concat(df_result, axis=0).reset_index(drop=True)

    return df_result


if __name__ == '__main__':
    FPATH = "ticker_v1a/"
    data_path = 'tuning/sell/data.csv'
    duration = 120
    ignore = 2021
    # p1()
    # eval_filter_all_v2(filter)

    df_buy = pd.read_csv("tuning/sell/df_buy.csv")
    df_sell = pd.read_csv("tuning/sell/df_sell.csv")
    df_map = pd.read_csv("tuning/sell/df_map.csv")
    list_eval = get_list_eval(df_map, cname_tvt='tvt')

    dataset = {
        'df_buy': df_buy,
        'df_sell': df_sell,
        'df_maps': df_map,
        'list_eval': list_eval,
    }

    # 1.1 Prepare features for dataset (df_sell)

    pattern_cfeature = ['MA20', 'MA50', 'MA200', 'MA20_T1', 'MA50_T1', 'Close', 'Close_T1', 'NP_P0', 'NP_P1',
                        'C_L1W', 'D_CMB', 'D_CMB_Peak_T1', 'D_CMB_XFast', 'BVPS', 'VAP1M', 'Volume_Max5Y_Low',
                        'Open_1D',
                        'PB', 'PE'] + \
                       ['MA5Y_PE', 'MA1Y_PE', 'MA3M_PE', 'SD5Y_PE', 'SD1Y_PE', 'SD3M_PE', 'MA5Y_PB', 'MA1Y_PB',
                        'MA3M_PB',
                        'SD5Y_PB', 'SD1Y_PB', 'SD3M_PB']
    # ['PE_MA5Y', 'PE_MA1Y', 'PE_MA3M', 'PE_SD5Y', 'PE_SD1Y', 'PE_SD3M', 'PB_MA5Y','PB_MA1Y', 'PB_MA3M','PB_SD5Y', 'PB_SD1Y', 'PB_SD3M']

    df_sell['sell_index'] = df_sell.index
    df_buy['buy_index'] = df_buy.index
    df_map = df_map.merge(df_buy[['buy_index'] + pattern_cfeature], on='buy_index', how='left',
                          suffixes=('_buy', '_sell'))
    df_map = df_map.merge(df_sell[['sell_index', 'time', 'pattern_label_filter'] + pattern_cfeature], on='sell_index',
                          how='left',
                          suffixes=('_buy', '_sell'))

    df_map['holding_session'] = 0
    for unit in df_map['buy_index'].unique():
        df_map.loc[df_map['buy_index'] == unit, 'holding_session'] = pd.DataFrame(
            range(3, 3 + len(df_map.loc[df_map['buy_index'] == unit]))).values

    df_map['sell_profit'] = df_map['Open_1D_sell'] / df_map['Open_1D_buy'] - 1

    df_map['label'] = 0
    df_map['label_filter'] = np.nan
    hold_index = df_map.sort_values(['ticker', 'holding_session'], ascending=[False, False]).drop_duplicates(
        subset=['ticker', 'buy_index'], keep='first').index
    df_map.loc[hold_index, "label"] = 1
    df_map.loc[hold_index, "label_filter"] = 'hold'

    pattern_index = df_map.query(f"pattern_label == 1").index
    df_map.loc[pattern_index, "label"] = 1
    df_map.loc[pattern_index, "label_filter"] = df_map.loc[pattern_index, "pattern_label_filter"]

    cutloss = -0.15
    cutloss_index = df_map.query(f"sell_profit < {cutloss}").index
    df_map.loc[cutloss_index, "label"] = 1
    df_map.loc[cutloss_index, "label_filter"] = 'cutloss'

    interest_rate = 0.07
    df_map['cash_profit'] = 100 * ((1 + df_map['sell_profit']) * (
            interest_rate * ((122 - df_map['holding_session']) / 240)) + df_map['sell_profit'])

    # display(get_bin_stats(df_map, 'PE_buy', 'sell_profit'))
    df_map['PE_z'] = (df_map['PE_sell'] - df_map['MA1Y_PE_sell']) / df_map['SD1Y_PE_sell']

    pdx = df_map.sort_values(['label', 'sell_index'], ascending=[False, True]).drop_duplicates(
        subset=['ticker', 'buy_index'], keep='first')

    # interest_rate = 0.07
    # pdx['cash_profit'] = 100 * ((1 + pdx['sell_profit']) * (
    #         interest_rate * ((122 - pdx['holding_session']) / 240)) + pdx['sell_profit'])

    display(get_bin_stats_2d(pdx, 'PE_buy', 'PE_z', 'sell_profit'))
