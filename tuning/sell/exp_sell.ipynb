#%%
import sys
import os

current_dir = '/home/<USER>/dev/ta/kaffa_v2/tuning/sell'
current_dir = current_dir.replace("/tuning/sell", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)
import random
# exec(open('../utils.py').read())
# exec(open('../eval_utils.py').read())


from tuning.eval_utils import *
from tuning.utils import *

random.seed(123)
from IPython.core.display_functions import display


#%%
# 1 - function prepare for data
def get_buy_signal(dictFilter, CUTLOSS=0.15):
    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS)
            res = eval_ticker.get_buy_signals()
            return res
        except Exception as error:
            print(f"Error: {ticker}: {error}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
    num_procs = 8
    print(len(list_processed_ticker))
    with Pool(num_procs) as p:
        # lres = p.amap(eval, list_processed_ticker)
        lres = p.map(eval, list_processed_ticker)
        # lres.extend(lres.get())

    lres = [res for res in lres if res is not None and not res.empty]
    pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)

    return pd_deal


def split_tvt(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]
    # iV = ((pdxy['Volume'] * pdxy['Price']).rolling(20).mean() > 500e6)
    pdxy[cname_tvt] = 'other'
    iA = pdxy['time'] < YMD_test
    iB = ~iA
    iTest = pdxy['ticker'].isin(list_ticker_test)
    iTrain = ~iTest
    pdxy.loc[iA & iTrain, cname_tvt] = 'train'
    pdxy.loc[iB & iTrain, cname_tvt] = 'val'
    pdxy.loc[iTest, cname_tvt] = 'test'
    # pdxy.loc[iV & iA & iTest, cname_tvt] = 'test1'
    # pdxy.loc[iV & iB & iTest, cname_tvt] = 'test2'
    return pdxy


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def pattern_label(pd_y):
    sell_filter = {
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        # "~SellMaxVol": " (Close < Volume_Max5Y_Low) &(ID_Current-Volume_Max5Y_ID<=120)",
        "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"
    }

    # Apply sell filters
    pd_y['pattern_label'] = 0

    for f in sell_filter:
        if f.startswith('~'):
            try:
                index = pd_y.query(f'({sell_filter[f]})').index
                pd_y.loc[index, 'pattern_label'] = 1
            except Exception as e:
                pass
#%%
# 1.0 Map buy to all sell during 120-D
FPATH = "ticker_v1a/"
duration = 120
ignore = 2021
sample = 3000
filter = {
    "_VolMax5Y": "((Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & (Close>1.02*Volume_Max5Y_High) & (ROE5Y>0.09)&(PE<13)& (NP_P0 > 1.21*NP_P1) & (PE >0)&(ID_Current-Volume_Max5Y_ID<=120)&(PB<2)",
    # "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
    # "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
    # "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
    # "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
    # "~SellMaxVol": f"(Close < Volume_Max5Y_Low) &(ID_Current-Volume_Max5Y_ID<=120)",
    # "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)"
}
df_buy = get_buy_signal(filter)
df_buy = df_buy.query(f"time > '{ignore + 1}' | time < '{ignore}'")
step = int(df_buy.shape[0] / sample)
df_buy = df_buy.iloc[::step, :].reset_index(drop=True)
df_buy['buy_index'] = df_buy.index

df_curr_ticker = None
df_sell = []
df_temp = []
for idx_buy, data in df_buy.iterrows():
    ticker = data['ticker']
    if df_curr_ticker is None or ticker != df_curr_ticker['ticker'].values[0]:
        if df_temp:
            df_temp = pd.concat(df_temp, axis=0)
            df_sell.append(df_temp)
        df_curr_ticker = pd.read_csv(f'{FPATH}/{ticker}.csv')
        ###### HAI
        df_curr_ticker['Close_T1M'] = df_curr_ticker['Close'].shift(20)
        df_curr_ticker['Close_1M'] = df_curr_ticker['Close'].shift(-20)
        ###### HAI
        df_temp = []

    df_curr_ticker['buy_index'] = idx_buy
    start = df_curr_ticker[df_curr_ticker['time'] == data['time']].index[0] + 3
    end = start + duration if start + duration < df_curr_ticker.shape[0] else df_curr_ticker.shape[0]
    df_temp.append(df_curr_ticker.iloc[start:end])

df_sell = pd.concat(df_sell, axis=0).reset_index(drop=True)
df_map = df_sell.groupby(['time', 'ticker'], as_index=False).agg({'buy_index': lambda x: ','.join(x.astype(str))})
df_sell = df_sell.drop_duplicates(subset=['time', 'ticker'], keep='first').reset_index(drop=True)
df_sell.drop(columns=['buy_index'], inplace=True)
df_sell = df_sell.merge(df_map, on=['time', 'ticker'], how='left')
pattern_label(df_sell)

df_map = df_sell[['time', 'ticker', 'buy_index', 'pattern_label']].copy()
df_map['buy_index'] = df_map['buy_index'].apply(lambda x: x.split(',')).copy()
df_map = df_map.explode('buy_index')
df_map['buy_index'] = df_map['buy_index'].astype(int)
df_map['sell_index'] = df_map.index
df_map.sort_values(['buy_index', 'sell_index'], inplace=True)
df_map = df_map[['buy_index', 'sell_index', 'pattern_label']]
df_map.reset_index(drop=True, inplace=True)
df_map = df_map.merge(df_buy[['buy_index', 'time', 'ticker']], on='buy_index', how='left')
split_tvt(df_map, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01')
df_sell.drop(columns=['buy_index'], inplace=True)

df_buy.to_csv('tuning/sell/df_buy.csv', index=False)
df_sell.to_csv('tuning/sell/df_sell.csv', index=False)
df_map.to_csv('tuning/sell/df_map.csv', index=False)

#%%
# 1.1 Divide train/val/test
df_buy = pd.read_csv("tuning/sell/df_buy.csv")
df_sell = pd.read_csv("tuning/sell/df_sell.csv")
df_map = pd.read_csv("tuning/sell/df_map.csv")

list_eval = get_list_eval(df_map, cname_tvt='tvt')

dataset = {
    'df_buy': df_buy,
    'df_sell': df_sell,
    'df_map': df_map,
    'list_eval': list_eval,
}
#%%
#1.1 draft
display(df_sell.head())
display(df_map.head())
#%%
# 1.1 Prepare features for dataset (df_sell)

pattern_cfeature = ['PE', 'MA20', 'MA50', 'MA200', 'MA20_T1', 'MA50_T1', 'Close', 'Close_T1', 'NP_P0', 'NP_P1',
                    'C_L1W', 'D_CMB', 'D_CMB_Peak_T1', 'D_CMB_XFast', 'BVPS', 'VAP1M', 'Volume_Max5Y_Low', 'Open_1D', 'PB']

df_sell['sell_index'] = df_sell.index
df_buy['buy_index'] = df_buy.index
df_map = df_map.merge(df_buy[['buy_index'] + pattern_cfeature], on='buy_index', how='left', suffixes=('_x', '_y'))
df_map = df_map.merge(df_sell[['sell_index', 'time'] + pattern_cfeature], on='sell_index', how='left',
                      suffixes=('_x', '_y'))

df_map['holding_session'] = 0
for unit in df_map['buy_index'].unique():
    df_map.loc[df_map['buy_index'] == unit, 'holding_session'] = pd.DataFrame(
        range(3, 3 + len(df_map.loc[df_map['buy_index'] == unit]))).values

df_map['sell_profit'] = df_map['Open_1D_y'] / df_map['Open_1D_x'] - 1





#%%
############ HAI
pattern_cfeature = ['PE', 'MA20', 'MA50', 'MA200', 'MA20_T1', 'MA50_T1', 'Close', 'Close_T1', 'NP_P0', 'NP_P1',
                    'C_L1W', 'D_CMB', 'D_CMB_Peak_T1', 'D_CMB_XFast', 'BVPS', 'VAP1M', 'Volume_Max5Y_Low', 'Open_1D', 'PB']

df_sell['sell_index'] = df_sell.index
df_buy['buy_index'] = df_buy.index
df_map = df_map.merge(df_buy[['buy_index'] + pattern_cfeature], on='buy_index', how='left', suffixes=('_x', '_y'))
df_map = df_map.merge(df_sell[['sell_index', 'time', 'Close_1M', 'Close_T1M'] + pattern_cfeature], on='sell_index', how='left',
                      suffixes=('_x', '_y'))

df_map['holding_session'] = 0
for unit in df_map['buy_index'].unique():
    df_map.loc[df_map['buy_index'] == unit, 'holding_session'] = pd.DataFrame(
        range(3, 3 + len(df_map.loc[df_map['buy_index'] == unit]))).values

df_map['sell_profit'] = df_map['Open_1D_y'] / df_map['Open_1D_x'] - 1
df_map['label_p1m'] = df_map['Close_1M'] / df_map['Close_y']
df_map['C_C_T1m'] = df_map['Close_y'] / df_map['Close_T1M']

df_map.to_csv('tuning/sell/df_hpo_train.csv', index=False)


############# HAI
#%%
display(df_map)
display(df_sell)
#%%
# 3. Prepare for eval Label
# for each hit, get first exit, then assume 7% annual profit after sell it
def eval_label(strategy):
    eval_label = []
    for idx_buy, d_buy in df_buy.iterrows():
        df_label = df_map.loc[(df_map['buy_index'] == idx_buy)].copy()

        # for idx_sell, d_sell in df_label.iterrows():
        d = {}
        for i in range(df_label.shape[0]):
            d_sell = df_label.iloc[i]
            if d_sell[strategy] == 1:
                d = {
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'ticker': d_sell['ticker'],
                    'Close_x': d_sell['Close_x'],
                    'Close_y': d_sell['Close_y'],
                    'Open_1D_x': d_sell['Open_1D_x'],
                    'Open_1D_y': d_sell['Open_1D_y'],
                    'hold_session': d_sell['holding_session'],
                    'sell_profit': d_sell['sell_profit'],
                    'sell_reason': strategy
                }
                break

            if i == df_label.shape[0] - 1:
                d = {
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'ticker': d_sell['ticker'],
                    'Close_x': d_sell['Close_x'],
                    'Close_y': d_sell['Close_y'],
                    'Open_1D_x': d_sell['Open_1D_x'],
                    'Open_1D_y': d_sell['Open_1D_y'],
                    'hold_session': d_sell['holding_session'],
                    'sell_profit': d_sell['sell_profit'],
                    'sell_reason': 'hold'
                }

        eval_label.append(pd.DataFrame(data=d, index=[d_buy['buy_index']]))

    df_eval_label = pd.concat(eval_label, axis=0).reset_index(drop=True)

    interest_rate = 0.07
    df_eval_label['profit'] = 100 * ((1 + df_eval_label['sell_profit']) * (
            interest_rate * ((122 - df_eval_label['hold_session']) / 240)) + df_eval_label['sell_profit'])

    return df_eval_label
#%%
# 3.1 Eval Label

# Cutloss
strategy_label = ["cutloss_30", "cutloss_20", "cutloss_10"]

# Label by stragegy
df_map[strategy_label] = 0
for idx in range(df_map.shape[0]):
    sell_profit = df_map.loc[idx, 'sell_profit']
    if sell_profit <= -0.3:
        df_map.loc[idx, strategy_label[0]] = 1
    if sell_profit <= -0.2:
        df_map.loc[idx, strategy_label[1]] = 1
    if sell_profit <= -0.1:
        df_map.loc[idx, strategy_label[2]] = 1

# Evaluating
strategy_dict = {}
for strategy in strategy_label:
    df_eval_label = eval_label(strategy)

    strategy_dict[strategy] = df_eval_label

#%%
# Drop 10-30% in the next month
strategy_label = ["drop_30", "drop_25", "drop_20", "drop_15", "drop_10", "drop_5"]
strategy_value = {
    "drop_30": "(sell_profit <= -0.3) & (holding_session == 20)",
    "drop_25": "(sell_profit <= -0.25) & (holding_session == 20)",
    "drop_20": "(sell_profit <= -0.2) & (holding_session == 20)",
    "drop_15": "(sell_profit <= -0.15) & (holding_session == 20)",
    "drop_10": "(sell_profit <= -0.1) & (holding_session == 20)",
    "drop_5": "(sell_profit <= -0.05) & (holding_session == 20)"
}
# Label by stragegy
df_map[strategy_label] = 0
for strategy in strategy_label:
    index = df_map.query(strategy_value[strategy]).index
    df_map.loc[index, strategy] = 1

# Evaluating
strategy_dict = {}
for strategy in strategy_label:
    df_eval_label = eval_label(strategy)

    strategy_dict[strategy] = df_eval_label
#%%
# 3.2 Observator eval label
df_eval = df_buy[['buy_index']]
for strategy, df in strategy_dict.items():
    df = df.rename(columns={'profit': strategy, 'sell_reason': f'{strategy}_reason'})
    df = df.rename(columns={'profit': strategy})
    df_eval = df_eval.merge(df[['buy_index', strategy, f'{strategy}_reason']], on='buy_index', how='left')

df_eval = df_eval.dropna()
# visualize label
label_1 = []
label_0 = []
for strategy in strategy_label:
    counts = df_eval[f'{strategy}_reason'].value_counts()
    label_0.append(counts['hold'])
    label_1.append(counts[strategy])
df = pd.DataFrame({'sell': label_1,
                   'hold': label_0}, index=strategy_label)
ax = df.plot.bar(rot=0, stacked=True)

# visualize return vs risk
average_profit = list(df_eval[strategy_label].mean().values)
std_profit = list(df_eval[strategy_label].std().values)

plt.figure(figsize=(8, 6))
plt.scatter(std_profit, average_profit)
for i, strategy in enumerate(strategy_label):
    plt.text(std_profit[i], average_profit[i], strategy, fontsize=12)

plt.title('Return vs Risk (Sharpe Ratio)')
plt.xlabel('Risk (Standard Deviation)')
plt.ylabel('Return (Average Profit)')
plt.grid(True)

# visualize for each eval_label
df_plot = pd.melt(df_eval, value_vars=strategy_label)
fig, ax = plt.subplots(figsize=(12, 6))
sns.boxplot(data=df_plot, x='variable', y='value', ax=ax)

for i, box in enumerate(ax.get_xticks()):
    category = ax.get_xticklabels()[i].get_text()
    box_data = df_plot[df_plot['variable'] == category]['value']
    q1 = np.nanpercentile(box_data, 25)
    q3 = np.nanpercentile(box_data, 75)
    median = np.nanpercentile(box_data, 50)
    whisker_low = box_data.min()
    whisker_high = box_data.max()

    x_coord = i

    ax.text(x_coord, median, f'{median:.1f}', ha='center', va='bottom', color='red', fontsize=10,
            fontweight='bold')  # Giá trị median
    ax.text(x_coord, q1, f'{q1:.1f}', ha='center', va='top', color='black', fontsize=10)  # Q1
    ax.text(x_coord, q3, f'{q3:.1f}', ha='center', va='bottom', color='black', fontsize=10)  # Q3
    ax.text(x_coord, whisker_low, f'{whisker_low:.1f}', ha='center', va='top', color='black',
            fontsize=10)  # Whisker thấp nhất
    ax.text(x_coord, whisker_high, f'{whisker_high:.1f}', ha='center', va='bottom', color='black',
            fontsize=10)  # Whisker cao nhất

ax.set_title(f'Profit distribution by label')
plt.show()
#%%
# 2. Strategy for lable (df_map)
ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
cname_label = ['pattern_label']

cname_features = [column for column in df_map.columns if column not in (ignore_cols + cname_label)]
#%%
# 4. Train XGBOOST
import xgboost as xgb


def train_xgboost(pdx, cname_features, cname_label, cname_tvt,
                  params=None):
    ''' pdx : dataframe
        cname_features : list of column names
        cname_label : column name for label (0/1)
        cname_tvt : column name for train, validation, test ('train'/'val'/'test')
    Output:
        model
    '''
    if params is None:
        params = {'tree_method': 'hist',
                  'max_bin': 256,
                  'max_depth': 7,
                  'min_child_weight': 10,
                  'subsample': .8,
                  'colsample_bytree': .8,
                  'learning_rate': .01,
                  'n_estimators': 1000,
                  'predictor': 'cpu_predictor',
                  'verbosity': 0,
                  'eval_metric': ['error', 'auc'],
                  'early_stopping_rounds': 100,
                  'gamma': .1,
                  'lambda': 1.0,
                  'alpha': 0.5,
                  }
    # split data
    pdx_train = pdx.query(f'{cname_tvt}=="train"')
    pdx_val = pdx.query(f'{cname_tvt}=="val"')

    # train model
    model = xgb.XGBClassifier(**params)
    eval_set = [(pdx_train[cname_features], pdx_train[cname_label]), (pdx_val[cname_features], pdx_val[cname_label])]

    model.fit(pdx_train[cname_features], pdx_train[cname_label], eval_set=eval_set, verbose=params['verbosity'])

    return model


model = train_xgboost(df_map, cname_features=cname_features, cname_label='pattern_label', cname_tvt='tvt')
model.save_model("tuning/sell/model/xgb_model.json")
#%%
# 4.1 Predict result

# best_ntree = model.get_booster().best_ntree_limit
# if (xgb.__version__ >= '1.4'):
#     y_pred = model.predict_proba(pdx_test[cname_features], iteration_range=(0, best_ntree))[:, 1]
# else:
#     y_pred = model.predict_proba(pdx_test[cname_features], ntree_limit=best_ntree)[:, 1]

df_map['pred'] = model.predict_proba(df_map[cname_features])[:, 1]

# Choose threshold
threshold = 0.5
df_map['pred_.5'] = np.where(df_map['pred'] > 0.5, 1, 0)

# Show predict bin
display(get_bin_stats(df_map[(df_map['tvt'] == 'test')], 'pred', 'pattern_label', bins=10))

# Profit mean
df_map['profit'] = df_map['Close_y'] / df_map['Close_x'] - 1

print(f"profit_mean: {df_map[(df_map['tvt'] == 'test') & (df_map['pred_.5'] == 1)]['profit'].mean()}")
# # plot error and auc curve over iteration
evals_result = model.evals_result()

fig, ax = plt.subplots(figsize=(10, 6))
ax.plot(model.evals_result()['validation_0']['error'], label='train')
ax.plot(model.evals_result()['validation_1']['error'], label='val')
ax.set_xlabel('Epoch')
ax.set_ylabel('Error')
ax.set_title('Loss Curve')
ax.legend()
plt.show()

fig, ax = plt.subplots(figsize=(10, 6))
ax.plot(model.evals_result()['validation_0']['auc'], label='train')
ax.plot(model.evals_result()['validation_1']['auc'], label='val')
ax.set_xlabel('Epoch')
ax.set_ylabel('AUC')
ax.set_title('AUC Curve')
ax.legend()
plt.show()
#%%
# 5. Prepare for Simulation

def ai_hit(df_pred, threshold):
    # Find first hit
    buy_index = list(df_pred['buy_index'].unique())

    df_result = []
    for idx_buy in buy_index:
        df_label = df_pred.loc[(df_pred['buy_index'] == idx_buy)].copy()
        d = {}
        for i in range(df_label.shape[0]):
            d_sell = df_label.iloc[i]
            if d_sell['pred'] > threshold:
                d = {
                    'ticker': d_sell['ticker'],
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'time': d_sell['time_x'],
                    'sell_time': d_sell['time_y'],
                    'holding_session': d_sell['holding_session'],
                    'profit': d_sell['sell_profit'],
                    'sell_reason': 'ml'
                }
                break

            if i == df_label.shape[0] - 1:
                d = {
                    'ticker': d_sell['ticker'],
                    'buy_index': d_sell['buy_index'],
                    'sell_index': d_sell['sell_index'],
                    'time': d_sell['time_x'],
                    'sell_time': d_sell['time_y'],
                    'holding_session': d_sell['holding_session'],
                    'profit': d_sell['sell_profit'],
                    'sell_reason': np.nan
                }

        # df_result.append(pd.DataFrame(data=d, index=[d_buy['buy_index']]))
        df_result.append(pd.DataFrame(data=d, index=[idx_buy]))
    df_result = pd.concat(df_result, axis=0).reset_index(drop=True)

    return df_result

# df_deal = ai_hit(df_map[(df_map['tvt'] == 'test')], 0.5)
#%%
# 5. Simulation for deal
# loaded_model = xgb.XGBClassifier()
# loaded_model.load_model("xgb_model.json")

# 5.1 Choose threshold
threshold = 0.5
df_deal = ai_hit(df_map[(df_map['tvt'] == 'test')], threshold)
# 4.2 Run simulation

simulation = Simulation(df_deal, start_date='2014-01-01', initial_assets=1e8, max_deals=10)
si_result = simulation.all_simulation(10)
print(si_result)
si_result = simulation.all_run(10)
# 4.3 Run simulation with other sell

#%%
si_result
#%%
df_deal
#%%
# 5. Optimize label
# 5.1 Optimize si_retun (buy)