"""
This use to explore riskrating throught VNIDEX

"""
import numpy as np
import pandas as pd
from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
import logging
import os
import re
import sys
import warnings
from datetime import datetime
from datetime import timedelta
from pathos.multiprocessing import ProcessingPool as Pool
import pandas as pd
from joblib import Memory

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)


def eval(pdxx, p_col='P3M', value=20):
    ticker = 'VNINDEX'
    try:
        pd_all = pdxx.query(f"{p_col} >= {value}")

        pd_all['s_time'] = pd.to_datetime(pd_all['time'])
        pd_all['week'] = pd_all['s_time'].dt.strftime('%Y-%W')
        pd_all['biweek'] = pd_all['s_time'].dt.to_period('2W').astype(str)
        pd_all['month'] = pd_all['s_time'].dt.strftime('%Y-%m')

        """
        A: data frame have P6M <= -20
        A deduplicate
        group all hit in same period find group_start_time and group_end_time

        Result:
        If there is no buy hit in same period so that need to consider

        """

        # pd_all = pd_all.drop_duplicates(subset=['month'], keep='first')

        pd_all['month'] = pd.to_datetime(pd_all['month'])
        pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)

        # Group consecutive months (month_diff <= 1)
        pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('first')
        pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('last') + pd.Timedelta(days=3 * 30)
        pd_all['mean_profit'] = pd_all.groupby('group')[p_col].transform('mean')
        pd_all['max_profit'] = pd_all.groupby('group')[p_col].transform('max')
        pd_all['min_profit'] = pd_all.groupby('group')[p_col].transform('min')
        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

        pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
        # Group consecutive group (group_diff <= 1)
        pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('first')
        pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('last')
        pd_all['mean_profit'] = pd_all.groupby('group')['mean_profit'].transform('mean')
        pd_all['max_profit'] = pd_all.groupby('group')['max_profit'].transform('max')
        pd_all['min_profit'] = pd_all.groupby('group')['min_profit'].transform('min')

        pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
        pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

        # path = 'tuning/results/'
        # res_month = pd.DataFrame(res_month)
        # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)

        return pd_all[
            ['start_group', 'end_group', 'start_group_q', 'end_group_q', 'mean_profit', 'max_profit', 'min_profit']]
    except Exception as error:
        print(f"Error: {ticker}: {error}")
        pass


def multi_process(list_processed_ticker, df_riskrate):
    def func(ticker):
        df_ticker = pd.read_csv(f'{FPATH}/{ticker}.csv',
                                usecols=['time', 'ticker', 'Close', 'O1M', 'O3M', 'O6M', 'O1Y'])
        df_ticker['P1Y'] = 100 * (df_ticker['O1Y'] - 1)
        df_ticker['P6M'] = 100 * (df_ticker['O6M'] - 1)
        df_ticker['P3M'] = 100 * (df_ticker['O3M'] - 1)
        df_ticker['P1M'] = 100 * (df_ticker['O1M'] - 1)
        df_ticker['quarter'] = pd.to_datetime(df_ticker['time']).dt.to_period('Q').astype(str)
        df_ticker = df_ticker.merge(df_riskrate[['quarter', f'{ticker}_risk_rating']], on='quarter', how='left')
        df_ticker.rename(columns={f'{ticker}_risk_rating': 'risk_rating'}, inplace=True)

        return df_ticker[['time', 'ticker', 'risk_rating', 'P1M']]

    num_procs = 1
    with Pool(num_procs) as p:
        lres = p.map(func, list_processed_ticker)

    lres = [res for res in lres if res is not None and not res.empty]
    pd_all = pd.concat(lres, axis=0).reset_index(drop=True)

    return pd_all


if __name__ == "__main__":
    FPATH = 'ticker_v1a'
    df_vnindex = pd.read_csv(f'{FPATH}/VNINDEX.csv')
    #
    # percentile P6M, P3M of vnindex
    df_vnindex['P1Y'] = 100 * (df_vnindex['O1Y'] - 1)
    df_vnindex['P6M'] = 100 * (df_vnindex['O6M'] - 1)
    df_vnindex['P3M'] = 100 * (df_vnindex['O3M'] - 1)
    df_vnindex['P1M'] = 100 * (df_vnindex['O1M'] - 1)

    percentiles = [0.5, 0.7, 0.8, 0.9, 0.95]

    df_percentiles = pd.DataFrame({
        'P1Y': df_vnindex['P1Y'].quantile(percentiles).values,
        'P6M': df_vnindex['P6M'].quantile(percentiles).values,
        'P3M': df_vnindex['P3M'].quantile(percentiles).values,
        'P1M': df_vnindex['P1M'].quantile(percentiles).values
    }, index=[f'{int(p * 100)}%' for p in percentiles])
    print(df_percentiles)
    #
    pd_period = eval(df_vnindex, 'P3M', 22)

    # handle
    result = []
    for i in range(pd_period.shape[0]):
        start_date = pd_period.iloc[i]['start_group']
        end_date = pd_period.iloc[i]['end_group']
        print(pd_period.iloc[i]['start_group'], pd_period.iloc[i]['end_group'])

        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        dates = pd.date_range(start=start_date, end=end_date, freq='30D')
        dates = dates.insert(0, start_date)

        last_date = end_date - pd.Timedelta(days=30)
        process_date = []
        for d in dates:
            if d - last_date >= pd.Timedelta(days=30):
                process_date.append(d)


        dates = dates.insert(len(dates), end_date - pd.Timedelta(days=30))

        dates = dates[dates <= end_date - pd.Timedelta(days=10)]
        dates = dates.drop_duplicates()
        dates = dates.to_numpy(dtype='datetime64[ns]')

        df_times = pd.to_datetime(df_vnindex['time'].copy(), errors='coerce').values

        dates_list = []
        for d in dates:
            idx = np.searchsorted(df_times, d, side='left')
            if idx < len(df_times):
                matched_day = df_times[idx]
                dates_list.append(matched_day)

        # df_all_slice = pd_all[
        #     (pd_all['time'] >= start_date.strftime('%Y-%m-%d')) & (pd_all['time'] <= end_date.strftime('%Y-%m-%d'))]

        for d in dates_list:
            d_str = pd.Timestamp(d).strftime('%Y-%m-%d')
            result.append({
                'time_series': i,
                'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                'date': (d + pd.Timedelta(days=30)).strftime('%Y-%m-%d'),
                'P1M_1': df_all_slice.query(f"time == '{d_str}' and risk_rating == 1")['P1M'].mean(),
                'P1M_2': df_all_slice.query(f"time == '{d_str}' and risk_rating == 2")['P1M'].mean(),
                'P1M_3': df_all_slice.query(f"time == '{d_str}' and risk_rating == 3")['P1M'].mean(),
                'P1M_4': df_all_slice.query(f"time == '{d_str}' and risk_rating == 4")['P1M'].mean(),
                'P1M_5': df_all_slice.query(f"time == '{d_str}' and risk_rating == 5")['P1M'].mean(),
            })

    df_result = pd.DataFrame(result)
