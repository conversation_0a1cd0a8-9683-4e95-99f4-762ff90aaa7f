import argparse
import logging
import os
import sys
from datetime import datetime

from hyo_tuning_manager import SellPatternTuner

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(
    filename=f'tuning/monitor_hyo_tuning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def parse_args():
    parser = argparse.ArgumentParser(description='Run hyperopt tuning for sell patterns')
    parser.add_argument('--patterns', nargs='+', help='List of patterns to tune')
    parser.add_argument('--max_evals', type=int, default=1000, help='Maximum number of evaluations')
    parser.add_argument('--single', action='store_true', help='Run single pattern tuning')
    return parser.parse_args()

def main():
    args = parse_args()
    tuner = SellPatternTuner()

    if args.single:
        if not args.patterns or len(args.patterns) != 1:
            raise ValueError("Single pattern mode requires exactly one pattern")
        pattern_name = args.patterns[0]
        print(f"\nTuning single pattern: {pattern_name}")
        result = tuner.tune_pattern(pattern_name, args.max_evals)
        print(f"\nBest parameters for {pattern_name}:", result)
    else:
        if not args.patterns:
            # Nếu không chỉ định pattern, chạy tất cả các pattern
            patterns_to_tune = list(tuner.patterns.keys())
        else:
            patterns_to_tune = args.patterns

        print(f"\nTuning patterns: {patterns_to_tune}")
        results = tuner.tune_multiple_patterns(patterns_to_tune, args.max_evals)
        print("\nFinal results:", results)

if __name__ == "__main__":
    main() 