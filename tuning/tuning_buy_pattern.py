import os
import re
import warnings

import numpy as np
from pathos.multiprocessing import ProcessingPool as Pool
from tabulate import tabulate
import time
from tuning.eval_utils import Simulation, TickerEval, PreProcess

warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime

FPATH = 'ticker_v1a/'



def parse_start_time(dict_filter):
    dates = ['2020-01-01']
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    return min_date_str



def eval_filter_all_v2(dictFilter, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS)
            res = eval_ticker.eval_by_deal()
            return res
        except Exception as error:
            print(f"Error: {ticker}: {e}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    #     lres.append(res)

    num_procs = 15
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    # Process dataframe
    lres = [res for res in lres if res is not None and not res.empty]
    if lres == []:
        return None

    pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)

    df_process = PreProcess()
    pd_deal = df_process.deals(pd_deal)

    _pdd = df_process.group_by(pd_deal, ['filter'])

    # Simulation deal
    start_date = parse_start_time(dictFilter)
    simulation = Simulation(pd_deal, start_date=start_date, initial_assets=1e8, max_deals=10)
    result = simulation.run_fast(iterate=100)

    si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_utilization']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'utilization']

    for pattern, value in result.items():
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            _pdd.loc[_pdd['filter'] == pattern, si_col] = value[si_result_col]

    pdd = _pdd[['filter', 'deal']].copy()
    pdd['%win_deal'] = (_pdd['count_win'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%loss_deal'] = (_pdd['count_loss'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%hold_deal'] = (_pdd['count_hold'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd["%cutloss_deal"] = (_pdd['count_cutloss'].astype(int) / _pdd['deal'].astype(int)) * 100

    # pdd['profit_win'] = _pdd['p_win'].values
    # pdd['profit_loss'] = _pdd['p_loss'].values
    # pdd['profit_hold'] = _pdd['p_hold'].values
    pdd['profit_cutloss'] = _pdd['p_cutloss'].values
    pdd['holding_period'] = _pdd['holding_period'].values
    pdd['profit_expected'] = _pdd['profit'].values

    # pdd['profit_vni'] = _pdd['profit_vni'].values
    # pdd['corr_deal_vni'] = _pdd['corr'].values
    # pdd['n_half'] = _pdd['n_half']
    # pdd['entropy'] = _pdd['entropy']
    # pdd['si_total_time'] = _pdd['si_total_time']
    # pdd['si_time_in_market'] = _pdd['si_time_in_market']
    pdd['si_deals'] = _pdd['si_deals']
    pdd['si_profit'] = _pdd['si_profit']
    pdd['si_cash_profit'] = _pdd['si_cash_profit']
    pdd['si_return'] = _pdd['si_return']

    return pdd


def append_empty_rows(dataframe, n):
    for _ in range(n):
        dataframe.loc[len(dataframe)] = pd.Series(dtype='float64')


def random_range(start, end, step):
    return round(np.random.choice(list(np.arange(start, end + step, step))), 4)


def random_int(start, end):
    return np.random.randint(start, end + 1)


def random_log(start, end):
    return round(np.random.choice(list(np.logspace(start, end, num=20))), 4)


def get_score(value, origin):
    score = 0

    if value > 10 + origin:
        score += 10
    elif value > 5 + origin:
        score += 5
    elif value > origin:
        score += 2
    elif value == origin:
        score += 1
    elif value < -10 + origin:
        score -= 10
    elif value < -5 + origin:
        score -= 5

    return score


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    filter = {
        # "_T2P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7)  & (C_H1M<0.9)  &  (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=3)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >=1)  &  (M_CMB_LAG <=4)",
        # "_T2P5X": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (C_H3M<0.8)  & (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=4)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >0)  &  (M_CMB_LAG <=4)",
        # "_TL3M": "(HI_3M_T1/LO_3M_T1<1.3) & (Volume > Volume_3M_P90)& (ROE5Y>0.1) & (PE<20) & (PB < 1.5) & (FSCORE > 4) & (NP_P0 > 1.1*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
        # "_PKDM8": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (M_CMB_Step>0)  &  (M_CMB_LEN>=8)  &  (M_CMB_LAG==1)",
        # "_T3P4": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R>0)&(PE<10)&(C_H2Y<0.7) & (C_H2Y>0.5)",
        # "_T3P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R<0)&(PE<5)&(C_H2Y<0.6)&(C_H2Y>0.3)",
        # "_T3P6": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
        # "_T3P6V1": "((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
        # "_BKMA200": "((Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>480) & (MA50/MA200>1.1) & (MA10/MA200<1.2) & (ROE5Y >0.1) & (PE <20) & (NP_P0 > NP_P1)",
        "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
        "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
        "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
        "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
        "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)",
        # "~SellMaxVol": " (Close < Volume_Max5Y_Low)"
    }

    result = []
    np.random.seed((os.getpid() * int(time.time())) % 123456789)

    for time in range(50):
        # "~S232": "((C_L3M>=1.2) & (Close < 2*MA200) & (Close_1W > MA200))&(Close<= MA200)&(Close_1W > MA200)",
        # S232 = f"((C_L3M>={w1}) & (Close < {w2}*MA200) & (Close_1W > {w3}*MA200))",

        if time == 0:
            filter[
                "_T3P6"] = "((Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)"
        else:
            turn_filter = {}
            for i in range(30):
                w1 = random_range(0, 0.4, 0.005)
                w7 = random_range(0.1, 0.3, 0.005)
                w6 = random_range(0.5, 0.7, 0.005)
                w5 = random_range(0.6, 0.9, 0.005)
                # w1 = random_range(1.05, 1.5, 0.01)
                # w1 = random_range(1, 1.3, 0.01)
                w3 = random_range(0.09, 0.13, 0.005)
                w4 = random_range(0.16, 0.21, 0.005)
                # w1 = random_range(0.05, 0.1, 0.005)
                # w3 = random_int(15, 21)
                w2 = random_int(2, 10)
                # w5 = random_range(1, 2, 0.01)
                # w5 = random_range(1, 1.5, 0.01)
                filter[
                    f"_test{i}"] = f"((Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>{w1}) & (W_CMB_LEN>={w2}) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>{w1}) & (M_CMB_LEN>={w2}) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>={w3})&(ROE5Y<={w4})&(NP_P0<0)&(PB<{w5})&(C_H2Y<{w6})&(C_H2Y>{w7})"

        evaluate = eval_filter_all_v2(filter)
        if evaluate is None:
            continue
        # evaluate['score'] = 0
        # # Ranking
        # sum_score = 0
        # for i in range(evaluate.shape[0]):
        #     score = 0
        #     score += get_score(evaluate['%win_deal'].iloc[i], evaluate['origin_win_deal'].iloc[i])
        #     score += get_score(evaluate['profit_expected'].iloc[i], evaluate['origin_profit_expected'].iloc[i])
        #
        #     evaluate['score'].iloc[i] = score
        #     sum_score += score
        #
        # evaluate['score'].iloc[-1] = sum_score

        result.append(evaluate)
        print(time)
        # print(tabulate(evaluate, headers='keys', tablefmt='psql'))

    result = pd.concat(result)
    output_path = 'test/temp/find_buy_pattern_t3p6.csv'
    result.to_csv(output_path, mode='a', header=not os.path.exists(output_path), index=False)
