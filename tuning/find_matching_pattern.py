import argparse
import json
import os
import random
import re
import time
from datetime import datetime, timedelta
from itertools import combinations

import numpy as np
import pandas as pd
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.base_eval import Simulation, PreProcess, TickerEval
from core_utils.constant import JOBLIB_CACHE_DIR
from core_utils.redis_cache import EvalRedis

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(expiry=18000)


class TickerEval_V2(TickerEval):
    def __init__(self, stock, df_all, dict_filter, cutloss=0.15, cache_service=None):
        super().__init__(stock, df_all, dict_filter, cutloss, cache_service)

    @staticmethod
    def random_filter(dictFilter):
        s_filters = []
        for f in dictFilter:
            if f.startswith('~'):
                s_filters.append(f[1:])
        C_s_filters = []
        for r in range(1, len(s_filters) + 1):
            C_s_filters.extend([list(comb) for comb in combinations(s_filters, r)])
        random.seed(time.time())
        return random.choices(C_s_filters, k=1)

    def find_matching(self, C_s_filters):
        pd_all = self.df_all.copy()
        # Evaluate by deal
        # Combination sell pattern
        # s_filters = []
        # for f in self.dictFilter:
        #     if f.startswith('~'):
        #         s_filters.append(f[1:])
        # C_s_filters = []
        # for r in range(1, len(s_filters) + 1):
        #     C_s_filters.extend([list(comb) for comb in combinations(s_filters, r)])

        buy_reasons = list(self.df_buy['filter'].unique())

        l_pd_deal = []
        now = pd_all.iloc[-1:][['time', 'Close', 'Volume']].copy()
        now['Sell_filter'] = 'Hold'
        for buy_reason in buy_reasons:
            pd_predefine_sell = self.df_buy[self.df_buy['filter'] == buy_reason][
                ['pre_sell', 'pre_sell_profit', 'pre_sell_id']].copy()
            buy_reason_indexes = pd_predefine_sell.index
            # buy_reason_indexes = list(self.df_buy[self.df_buy['filter'] == buy_reason].index).copy()

            for C_s_filter in C_s_filters:
                pd_sell_temp = self.df_sell.query(f"Sell_filter == {C_s_filter}").copy()
                pd_sell_temp = pd_sell_temp._append(now)
                sell_indexes = list(pd_sell_temp.index)
                sell_reasons = list(pd_sell_temp['Sell_filter'])

                result = self.find_sell(self.df_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']], pd_predefine_sell,
                                        buy_reason_indexes, sell_indexes, sell_reasons, self.cutloss)

                deal_time = pd_all.loc[result[0]]['time'].values
                ticker = pd_all.loc[result[0]]['ticker'].values
                deal_sell_time = pd_all.loc[result[2]]['time'].values
                pd_deal_pa = pd.DataFrame({'ticker': ticker,
                                           'time': deal_time,
                                           'buy_price': result[1],
                                           'sell_price': result[3],
                                           'profit': result[4],
                                           'sell_filter': result[5],
                                           'sell_time': deal_sell_time,
                                           'profit_vni': result[6],
                                           })
                C_s_filter.sort()
                pd_deal_pa['filter'] = f"{buy_reason}_{C_s_filter}"

                l_pd_deal.append(pd_deal_pa)

        deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                     'profit_vni']
        pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

        pd_deal = pd_deal.merge(pd_all[['time', 'Open_1D', 'Close']], on=['time'], how='left')
        return pd_deal


def random_filter(dictFilter, k=10):
    s_filters = []
    for f in dictFilter:
        if f.startswith('~'):
            s_filters.append(f[1:])
    C_s_filters = []
    for r in range(1, len(s_filters) + 1):
        C_s_filters.extend([list(comb) for comb in combinations(s_filters, r)])
    random.seed(time.time())
    return random.choices(C_s_filters, k=k)


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def parse_input():
    """
    Parses command-line inputs provided by the user.
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Template for Python script with user input.")

    parser.add_argument('--profiles', type=str, default='email', help='Input data file or raw input string')
    parser.add_argument('--cutloss', type=float, default=0.15, help='cutloss')
    parser.add_argument('--num_procs', type=float, default=8, help='cutloss')

    return parser.parse_args()


def main():
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """
    args = parse_input()
    configFilters = json.loads(open("config.json", "r").read())
    dictFilter = json.loads(configFilters[args.profiles][0])
    if 'Init' in dictFilter.keys():
        for key, value in dictFilter.items():
            dictFilter[key] = value.replace("{Init}", dictFilter['Init'])
    cutloss = args.cutloss
    print("profile:", args.profiles)
    print(dictFilter)

    C_s_filters = random_filter(dictFilter, k=20)

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval_V2(ticker, pdxx, dictFilter, cutloss, cache_service=redis_cache)
            res = eval_ticker.find_matching(C_s_filters)
            return res
        except Exception as e:
            print(f"Error: {ticker}: {e}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = args.num_procs
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    # Process dataframe
    pd_deal = pd.concat([res for res in lres if res is not None and not res.empty], axis=0).reset_index(drop=True)
    print("Done evaluation")
    # pd_deal
    df_process = PreProcess()
    # pd_deal = df_process.deals(pd_deal)
    #######
    pd_deal[['p_hold', 'p_cutloss']] = np.nan
    for idx, result in enumerate(pd_deal['sell_filter'].values):
        if f'p_{result}' not in pd_deal.columns:
            pd_deal[f'p_{result}'] = np.nan
        pd_deal.loc[idx, f'p_{result}'] = pd_deal.loc[idx, 'profit']

    p_sell_columns = [col for col in pd_deal.columns if
                      (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
    pd_deal["p_sell_pattern"] = pd.concat([pd_deal[col].dropna() for col in p_sell_columns])

    pd_deal['count_win'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] > 0, np.nan)
    pd_deal['count_hold'] = pd_deal['p_hold'].copy()
    pd_deal['deal'] = 1
    _pdd = df_process.group_by(pd_deal, ['filter'])
    #######

    # Simulation deal
    start_date, end_date = parse_time(dictFilter)
    print("Simulation from", start_date, "to", end_date)
    simulation = Simulation(pd_deal, start_date=start_date, end_date=end_date, initial_assets=1e8, max_deals=10,
                            cache_service=memory, num_proc=8)
    result = simulation.run_fast(iterate=20)

    si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_utilization']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'utilization']

    for pattern, value in result.items():
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            _pdd.loc[_pdd['filter'] == pattern, si_col] = value[si_result_col]

    pdd = _pdd[['filter', 'deal']].copy()

    pdd['%win_deal'] = (_pdd['count_win'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%hold_deal'] = (_pdd['count_hold'].astype(int) / _pdd['deal'].astype(int)) * 100
    # pdd['%loss_deal'] = (_pdd['count_loss'].astype(int) / _pdd['deal'].astype(int)) * 100
    # pdd["%cutloss_deal"] = (_pdd['count_cutloss'].astype(int) / _pdd['deal'].astype(int)) * 100
    #
    # pdd['profit_win'] = _pdd['p_win'].values
    # pdd['profit_loss'] = _pdd['p_loss'].values
    # pdd['profit_hold'] = _pdd['p_hold'].values
    # pdd['profit_cutloss'] = _pdd['p_cutloss'].values
    # pdd['holding_period'] = _pdd['holding_period'].values
    # pdd['profit_expected'] = _pdd['profit'].values
    # pdd['profit_vni'] = _pdd['profit_vni'].values
    # pdd['corr_deal_vni'] = _pdd['corr'].values

    pdd['si_total_time'] = _pdd['si_total_time']
    pdd['si_time_in_market'] = _pdd['si_time_in_market']
    pdd['si_deals'] = _pdd['si_deals']
    pdd['si_profit'] = _pdd['si_profit']
    pdd['si_cash_profit'] = _pdd['si_cash_profit']
    pdd['si_return'] = _pdd['si_return']

    output_file = f'tuning/temp/evaluate_buy_to_sell.csv'
    if os.path.exists(output_file):
        existing_data = pd.read_csv(output_file)
        updated_data = pd.concat([existing_data, pdd], axis=0).reset_index(drop=True)
        # updated_data = updated_data.sort_values(['filter', "si_return"], ascending=[False, False])
        updated_data.to_csv(output_file, index=False)
    else:
        pdd.to_csv(output_file, index=False)


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)
    now = time.time()
    while True:
        evaluate = main()
