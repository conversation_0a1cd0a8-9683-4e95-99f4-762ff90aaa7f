import os
import pickle
import re
import warnings
from hyperopt.early_stop import no_progress_loss
import numpy as np
from hyperopt import hp, fmin, tpe, Trials, STATUS_OK
from hyperopt.pyll.base import scope
from pathos.multiprocessing import ProcessingPool as Pool
from hyperopt.fmin import generate_trials_to_calculate
import logging

logging.basicConfig(filename='monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime
from tuning.eval_utils import Simulation, TickerEval

FPATH = 'ticker_v1a/'
path = 'tuning/sell/'


# pdxx = pd.read_csv(f'{path}/df_hpo_train_1.csv')


def predict(df_data: pd.DataFrame, weight):
    w1, w2, w3 = weight
    # df_data['Close_T1M'] = df_data['Close'].shift(30)
    # df_data['Close_1M'] = df_data['Close'].shift(-30)
    # df_data['sell_profit'] = df_data['Open_1D_y'] / df_data['Open_1D_x'] - 1
    # df_data['C_C_T1m'] = df_data['Close_y'] / df_data['Close_T1M']
    # df_data['label_p1m'] = df_data['Close_1M'] / df_data['Close_y']

    df_data['pred'] = w1 * (df_data['C_C_T1m']) + w2 * (df_data['holding_session'] / 30) + w3

    df = df_data[['label_p1m', 'pred']].dropna().copy()
    data = (abs(df['label_p1m'] - df['pred']))

    return data.sum(), data.count()


def eval_filter_all_v2(weight, l_data):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    # def eval(args):
    #     pdxx = args
    #
    #     try:
    #         res = predict(pdxx, weight)
    #         return res
    #     except Exception as error:
    #         pass

    # num_procs = 1
    # with Pool(num_procs) as p:
    #     # lres = p.amap(eval, list_processed_ticker)
    #     lres = p.map(eval, l_data)

    # lres = predict(pdxx, weight)
    #
    # r1 = [res[0] for res in lres if np.isnan(res[0]) == False]
    # r2 = [res[1] for res in lres if np.isnan(res[1]) == False]

    # sum = np.nansum(r1)
    # count = np.nansum(r2)

    lres = predict(l_data[0], weight)
    sum = lres[0]
    count = lres[1]
    result = sum / count

    return result


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    pdxx = pd.read_csv(f'{path}/df_hpo_train.csv')
    list_data = [pdxx]

    def objective(params):
        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        weight = (w1, w2, w3)
        result = eval_filter_all_v2(weight, list_data)

        logging.info(f"\n")
        logging.info(f'loss: {result}')
        print(f'loss: {result}')

        logging.info(
            f"w1={w1}, w2={w2}, w3={w3}")

        return {'loss': result, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', -10, 10, 0.001),
        'w2': hp.quniform('w2', -10, 10, 0.001),
        'w3': hp.quniform('w3', -10, 10, 0.001),

        # 'w2': scope.int(hp.quniform('w2', 2, 30, 2)),
        # 'w8': hp.uniform('w8', 0, 8),
        # 'w2': hp.quniform('w2', 1, 50, 0.5),
    }

    init_vals = [{'w1': 0.3, 'w2': 20, 'w3': 0,
                  'w4': 2, 'w5': 0.1, 'w6': 1, 'w7': 4, 'w8': 0}]
    # objective(init_vals[0])

    # trials = generate_trials_to_calculate(init_vals)
    # trials = pickle.load(open("tuning/trials_T3P4.pkl", "rb"))
    trials = Trials()

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file="trials_profit_expected.pkl"
    )
    print("Best parameters:", best)
