#%%
import pandas as pd
from datetime import datetime
from tqdm import tqdm
import time
import os
import numpy as np
from ast import literal_eval
from vnstock3 import Vnstock
import warnings

warnings.simplefilter(action='ignore')

exec(open('utils.py').read())
exec(open('prepare.py').read())
#%%

#%%
def prepare_pdxy(list_processed_ticker, in_folder='../ticker_v1a/'):
    ## combine all ticker data into one file
    ll = []
    for ticker in list_processed_ticker:
        pd_xy = load_data(f'{in_folder}/{ticker}.csv', start=2014)
        pd_xy['Close_T20'] = handle_input_data(pd_xy, 20)
        pd_xy['min_T20'] = pd_xy['Close'].rolling(20).min()
        pd_xy['EMA'] = pd_xy['Close'].ewm(span=20, adjust=False).mean()
        # Bollinger Bands
        std = pd_xy['Close'].rolling(20).std()
        pd_xy['BB_lower'] = pd_xy['MA20'] - (std * 2)

        for time in range(1, 6):
            col = f"minL{time}"
            pd_xy[col] = pd_xy['Close'].rolling(time).min()
            # pd_xy[col] = pd_xy['Close'].rolling(20).apply(lambda x: 1 if (x.min() == x[20 - 1 - time: 20]).any() else 0)
        for name, w in zip(['minW5D', 'minW10D', 'minW20D'], [11, 21, 41]):
            pd_xy[name] = pd_xy['Close'].rolling(w, center=True).min()
            # pd_xy[name] = pd_xy['Close'].rolling(w, center=True).apply(lambda x: 1 if x[w // 2] == x.min() else 0,
            #                                                            raw=True)

        pd_xy['Y1'] = 0.0
        for i in range(1, pd_xy.shape[0]):
            if pd_xy.at[i, 'minW10D'] == 1:
                pd_xy.at[i, 'Y1'] = 0.8
                pd_xy.at[i + 1, 'Y1'] = 0.9
                pd_xy.at[i + 2, 'Y1'] = 1

        pd_xy['label1'] = np.where(pd_xy['minL1'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label2'] = np.where(pd_xy['minL2'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label3'] = np.where(pd_xy['minL3'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label4'] = np.where(pd_xy['minL4'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label5'] = np.where(pd_xy['minL5'] == pd_xy['minW5D'], 1, 0)

        pd_xy.dropna(subset="Close_T20", inplace=True)
        pd_xy.ffill(inplace=True)
        ll.append(pd_xy)

    pdxy = pd.concat(ll, axis=0, ignore_index=True).reset_index(drop=True)  #
    pdxy = pdxy.dropna(axis=1, how='all')  # drop columns with all NaN

    # pdxy.replace(np.inf, np.nan, inplace=True)
    # pdxy.replace(-np.inf, np.nan, inplace=True)
    # pdxy.dropna(inplace=True)
    # minima_idxs = trendln.get_extrema((pd00[cname_key], None), extmethod=trendln.METHOD_NUMDIFF, accuracy=10)

    cname_feature = [f for f in pdxy.columns if f not in ['time', 'ticker', 'ymd', 'quarter',
                                                          'Open', 'Close', 'High', 'Low', 'Close_TT20',
                                                          'L1W', 'L2W', 'L3w', 'L1M', 'L2M', 'L3M', 'L1Y', 'L2Y', 'H1W',
                                                          'H2W', 'H3W', 'H1M', 'H2M', 'H3M', 'H1Y', 'H2Y', 'C1W', 'C2W',
                                                          'C3W', 'C1M', 'C2M', 'C3M', 'C1Y', 'C2Y']]

    cname_tvt = 'tvt'
    split_tvt_v1(pdxy, cname_tvt)
    list_eval = get_list_eval(pdxy, cname_tvt=cname_tvt)
    list_label = ['minW5D', 'minW10D', 'minW20D', 'minL1', 'minL2', 'minL3', 'minL4', 'minL5', 'label1',
                  'label2', 'label3', 'label4', 'label5', 'Y1']
    return {'pdxy': pdxy,
            'cname_feature': cname_feature,
            'list_label': list_label,
            'list_eval': list_eval,
            }


stock_vci = Vnstock(show_log=False).stock(source='VCI')
list_ticker = [f for f in stock_vci.listing.symbols_by_group('VN30')]

dataset = prepare_pdxy(list_ticker)
pd_raw = dataset['pdxy']
feature_cols = dataset['cname_feature']
label_cols = dataset['list_label']
list_eval = dataset['list_eval']
#
cols = feature_cols + label_cols

train_dataset = pd_raw[list_eval[0][1]]
val_dataset = pd_raw[list_eval[3][1]]

# pd_raw.to_csv('pd_xy.csv', index=False)
# train_dataset.to_csv('train.csv', index=False)
# val_dataset.to_csv('val.csv', index=False)
#%%
def SMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['MA10']].copy()
    plot_2_relationship(df, title=f'minL1 < MA10: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA20']].copy()
    plot_2_relationship(df, title=f'minL1 < MA20: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA50']].copy()
    plot_2_relationship(df, title=f'minL1 < MA50: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA100']].copy()
    plot_2_relationship(df, title=f'minL1 < MA100: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA200']].copy()
    plot_2_relationship(df, title=f'minL1 < MA200: {df.shape[0]}')


def EMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['EMA']].copy()
    plot_2_relationship(df, title=f'minL1 < EMA: {df.shape[0]}')


def RSI(df_raw):
    df = df_raw[df_raw['D_RSI'] < 0.1].copy()
    plot_2_relationship(df, title=f'D_RSI < 0.1: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.2].copy()
    plot_2_relationship(df, title=f'D_RSI < 0.2: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.3].copy()
    plot_2_relationship(df, title=f'D_RSI < 0.3: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.4].copy()
    plot_2_relationship(df, title=f'D_RSI < 0.4: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.5].copy()
    plot_2_relationship(df, title=f'D_RSI < 0.5: {df.shape[0]}')


def MACD(df):
    df = df[np.where((df['D_MACD'].shift(1) < df['D_MACDsign'].shift(1)) &
                     (df['D_MACD'] > df['D_MACDsign']), True, False)].copy()
    plot_2_relationship(df, title=f'MACD cắt lên D_MACDsign : {df.shape[0]}')


def Bollinger_Bands(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['BB_lower']]
    plot_2_relationship(df, title=f'minL1 < BB_lower : {df.shape[0]}')
#%%
pd_xy = pd.read_csv('pd_xy.csv', converters={'Close_T20': literal_eval})
train_dataset = pd.read_csv('train.csv', converters={'Close_T20': literal_eval})
val_dataset = pd.read_csv('val.csv', converters={'Close_T20': literal_eval})
corr = pd.read_csv("corr.csv", index_col=0)
#%%
plot_2_relationship(train_dataset, title=f'original: {train_dataset.shape[0]}')

condition1 = train_dataset[train_dataset['minL1'] > train_dataset['minL5']].copy()
plot_2_relationship(condition1, title=f'minL1> minL5: {condition1.shape[0]}')

condition2 = train_dataset[train_dataset['minL2'] > train_dataset['minL5']].copy()
plot_2_relationship(condition2, title=f'minL2> minL5: {condition2.shape[0]}')

condition3 = train_dataset[train_dataset['minL3'] > train_dataset['minL5']].copy()
plot_2_relationship(condition3, title=f'minL3> minL5: {condition3.shape[0]}')

SMA(train_dataset)
EMA(train_dataset)
RSI(train_dataset)
MACD(train_dataset)
Bollinger_Bands(train_dataset)

#%%
co = corr['Pearson'].sort_values(ascending=False)
# co = corr['Spearman'].sort_values(ascending=False)
# co = corr['Kendall'].sort_values(ascending=False)
# co = corr['Mutual Information'].sort_values(ascending=False)
# co = corr['Maximal Information Coefficient'].sort_values(ascending=False)
# co = corr['Average Score'].sort_values(ascending=False)
# co = corr['Average Score_1'].sort_values(ascending=False)
#%% md

#%%
list20pos = co.head(20).index.to_list()
list20neg = co.tail(20).index.to_list()
#%%
list20neg
#%%
list20neg.remove('Y1')
#%%
# train : feature 32K x 10 features / label 32K x 1
# test : feature 8K x 10 features / label 8K x 1
import xgboost as xgb


def train_xgboost(pdx, cname_features, cname_label, cname_tvt,
                  params={'tree_method': 'hist',
                          'max_bin': 256,
                          'max_depth': 7,
                          'min_child_weight': 10,
                          'subsample': .8,
                          'colsample_bytree': .8,
                          'learning_rate': .01,
                          'n_estimators': 1000,
                          'predictor': 'cpu_predictor',
                          'verbosity': 0,
                          'eval_metric': 'logloss',
                          'early_stopping_rounds': 100,
                          'gamma': .1,
                          'lambda': 1.0,
                          'alpha': 0.5,

                          }):
    ''' pdx : dataframe
        cname_features : list of column names
        cname_label : column name for label (0/1)
        cname_tvt : column name for train, validation, test ('train'/'val'/'test')
    Output:
        model
        pred
    '''
    # split data
    pdx_train = pdx.query(f'{cname_tvt}=="train"')
    pdx_val = pdx.query(f'{cname_tvt}=="val"')
    pdx_test = pdx.query(f'{cname_tvt}=="test1" | {cname_tvt}=="test2"')
    # train model
    model = xgb.XGBClassifier(**params)
    # using early stopping on val
    model.fit(pdx_train[cname_features], pdx_train[cname_label],
              eval_set=[(pdx_val[cname_features], pdx_val[cname_label])], verbose=params['verbosity'])
    # predict
    # y_pred = model.predict_proba(pdx_test[cname_features])[:, 1]
    y_pred = model.predict_proba(pdx[cname_features])[:, 1]

    # best_ntree = model.get_booster().best_ntree_limit
    # if (xgb.__version__ >= '1.4'):
    #     y_pred = model.predict_proba(pdx_test[cname_features], iteration_range=(0, best_ntree))[:, 1]
    # else:
    #     y_pred = model.predict_proba(pdx_test[cname_features], ntree_limit=best_ntree)[:, 1]
    return model, y_pred


model, pd_xy['pred'] = train_xgboost(pd_xy, cname_features=list20neg + list20pos, cname_label='label3', cname_tvt='tvt')

get_bin_stats(pd_xy[(pd_xy['tvt'] == 'test1') | (pd_xy['tvt'] == 'test2')], 'pred', 'label3', bins=10)

#%%
pd_xy['pred_2'] = np.where(pd_xy['pred'] > 0.4, 1, 0)

pd_test = pd_xy[(pd_xy['tvt'] == 'test1') | (pd_xy['tvt'] == 'test2')]
metric = Metrics(pd_test['label3'], pd_test['pred_2'])

print("precision", metric.precision_score())
print("recall", metric.recall_score())
print("f1_score", metric.f1_score())
print("accuracy_score", metric.accuracy_score())
print("auc", metric.auc_score())
print(metric.plot_confusion_matrix())
#%%
print("precision", metric.precision_score())
print("recall", metric.recall_score())
print("f1_score", metric.f1_score())
print("accuracy_score", metric.accuracy_score())
print("auc", metric.auc_score())
print(metric.plot_confusion_matrix())
#%%
get_bin_stats(pd_xy[(pd_xy['tvt'] == 'test1') | (pd_xy['tvt'] == 'test2')], 'pred', 'label3', bins=10)
