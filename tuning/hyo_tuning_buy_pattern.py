import logging
import os
import pickle
import re
import sys
import warnings
from datetime import datetime
from datetime import timedelta

import numpy as np
import pandas as pd
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.base_eval import TickerEval, PreProcess, Simulation
from core_utils.constant import JOBLIB_CACHE_DIR
from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host="*************")
RANKING = 'ranking_point'


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def eval_filter_all_v2(dictFilter, CUTLOSS=0.15, skip=None):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    if skip is None:
        skip = []

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, dictFilter, cutloss=CUTLOSS, cache_service=redis_cache)
            res = eval_ticker.get_deal()
            return res
        except Exception as error:
            # print(f"Error: {ticker}: {error}")
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 1
    with Pool(num_procs) as p:
        # lres = p.amap(eval, list_processed_ticker)
        lres = p.map(eval, list_processed_ticker)

        # lres.extend(lres.get())
    try:
        lres = [res for res in lres if res is not None and not res.empty]

        pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)
        if pd_deal.shape[0] < 150:
            print(pd_deal.shape[0])
            raise

        df_process = PreProcess()
        pd_deal = df_process.deals(pd_deal)

        pd_deal['month'] = pd_deal['time'].str[:7]
        _pdd_q = df_process.group_by(pd_deal, ['filter', "quarter"])
        _pdd_m = df_process.group_by(pd_deal, ['filter', "month"])

        win = _pdd_q[(_pdd_q['count_win'] + _pdd_q['count_hold_win']) >= (
                _pdd_q['count_loss'] + _pdd_q['count_cutloss'] + _pdd_q['count_hold_loss'])].shape[0]
        loss = _pdd_q[(_pdd_q['count_win'] + _pdd_q['count_hold_win']) <= (
                _pdd_q['count_loss'] + _pdd_q['count_cutloss'] + _pdd_q['count_hold_loss'])].shape[0]
        win_quarter = win / sum([win, loss])

        df_tail = _pdd_q.tail(21).iloc[:-1]
        win_tail = df_tail[(df_tail['count_win'] + df_tail['count_hold_win']) >= (
                df_tail['count_loss'] + df_tail['count_cutloss'] + df_tail['count_hold_loss'])].shape[0]
        loss_tail = df_tail[(df_tail['count_win'] + df_tail['count_hold_win']) <= (
                df_tail['count_loss'] + df_tail['count_cutloss'] + df_tail['count_hold_loss'])].shape[0]
        winblock_20quarters = win_tail / sum([win_tail, loss_tail])

        df_tail = _pdd_m.tail(24)
        win_tail = df_tail[(df_tail['count_win'] + df_tail['count_hold_win']) >= (
                df_tail['count_loss'] + df_tail['count_cutloss'] + df_tail['count_hold_loss'])].shape[0]
        loss_tail = df_tail[(df_tail['count_win'] + df_tail['count_hold_win']) <= (
                df_tail['count_loss'] + df_tail['count_cutloss'] + df_tail['count_hold_loss'])].shape[0]
        winblock_24months = win_tail / sum([win_tail, loss_tail])

        # SIMULATION
        start_date, _ = parse_time(dictFilter)

        simulation = Simulation(start_date=start_date, initial_assets=50e9, max_deals=25,
                                cache_service=memory, num_proc=18)
        result = simulation.run_fast(pd_deal, iterate=50, s_type='adjust_cash')
        si_return = 0
        std = 0
        peak_number_deals = 0
        penalty = 1

        # windeal, win_quarter
        if (win_quarter < 0.5) or (winblock_20quarters < 0.5) or (winblock_24months < 0.5):
            penalty *= np.min([win_quarter, winblock_20quarters, winblock_24months]) / 0.5
            print(
                f"win_quarter: {win_quarter}, winblock_20quarters: {winblock_20quarters}, winblock_24months: {winblock_24months}")

        logging.info(
            f"win_quarter: {win_quarter}, winblock_20quarters: {winblock_20quarters}, winblock_24months: {winblock_24months}")

        for pattern, value in result.items():
            si_return += value['return']
            std += value['return_std']
            peak_number_deals += value['peak_number_deals']

        _pdd_d = df_process.group_by(pd_deal, ['filter'])
        _pdd_d['%win_deal'] = (_pdd_d['count_win'].astype(int) / _pdd_d['deal'].astype(int)) * 100

        # return si_return, std, _pdd_d['%win_deal'].values[0]
        return {
            'si_return': si_return,
            'si_std': std,
            '%win_deal': _pdd_d['%win_deal'].values[0],
            'deal': _pdd_d['deal'].values[0],
            'peak': peak_number_deals,
            'penalty': penalty,
            'status': STATUS_OK
        }
    except:
        return {
            'si_return': np.inf,
            'si_std': np.inf,
            '%win_deal': np.inf,
            'peak': 0,
            'deal': 1,
            'penalty': -1,
            'status': STATUS_OK
        }


if __name__ == "__main__":

    import random

    random.seed(0)


    def objective(params):
        skip = []

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        w8 = params['w8']
        w9 = params['w9']
        w10 = params['w10']
        w11 = params['w11']
        w12 = params['w12']
        w13 = params['w13']
        w14 = params['w14']
        w15 = params['w15']
        w16 = params['w16']
        w17 = params['w17']
        w18 = params['w18']

        key_map = {
            'w_ma21': 'MA21',
            'w_ma31': 'MA31',
            'w_ma41': 'MA41',
            'w_s13': 'S13',
            'w_selllowgrowth': 'SellLowGrowth',
            'w_sellresistance1y': 'SellResistance1Y',
            'w_sellresistance1m': 'SellResistance1M',
            'w_sellresistance': 'SellResistance',
            'w_sellbv': 'SellBV',
            'w_sellbv2': 'SellBV2',
            'w_sellpe': 'SellPE',
            'w_sellvolmax': 'SellVolMax',
            'w_beardvg2': 'BearDvg2'
        }

        sell_filter = {
            "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
            "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
            "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
            "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
            "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
            "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
            "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
            "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
            "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
            "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
            "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
            "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
            "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
        }

        filter = {
            "_BuyPattern": f"(Volume_1M_P50*Price/Inflation_7>5e+8) & (time>='2014-01-01') & (time<='2026-01-01')  & (D_RSI / D_RSI_T1 > {w1}) & (D_RSI > {w2}) & (D_RSI < {w3}) & (D_RSI_Min3M < {w4}) & (D_RSI_Min1W > {w5}) & (D_RSI_Min1W/D_RSI_Min3M > {w6}) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < {w7})  & (FSCORE > {w8}) & (PE< {w9}) &  (PE>{w10}) & (PB < {w11})  & (ROE_Min5Y > {w12}) & (PCF <{w13}) & (PCF>{w14}) &  ((Cash_P0/ (LtDebt_P0+1) > {w15})|(abs(IntCov_P0) > {w16})) & ((CF_OA_5Y/OShares)> {w17}) & (NP_P0/NP_P4 >={w18})"
        }
        filter.update(sell_filter)

        b2s = ""
        for key, value in key_map.items():
            if params[key] > 0:
                b2s += f"{value}, "

        filter['$BuyPattern'] = b2s[:-2]
        logging.info(f"\n")
        print("----------------------------------------------------------------------------------------------------")

        result = eval_filter_all_v2(filter, skip=skip)

        si_return = result['si_return']
        si_std = result['si_std']
        windeal = result['%win_deal']
        deal = result['deal']
        peak = result['peak']
        penalty = result['penalty']

        loss = (si_return + windeal) * penalty

        logging.info(
            f'return: {si_return}, std: {si_std}, windeal: {windeal}, deal :{deal}, loss: {-loss}, peak: {peak}, penalty: {penalty}')
        print(filter)
        print(
            f'return: {si_return}, std: {si_std}, windeal: {windeal}, deal :{deal}, loss: {-loss}, peak: {peak}, penalty: {penalty}')
        print("\n")
        return {'loss': -loss, 'status': result['status']}


    search_space = {
        # (D_RSI / D_RSI_T1 > {w1})
        'w1': hp.quniform('w1', 0.6, 0.9, 0.02),

        # (D_RSI > {w2})
        'w2': hp.quniform('w2', 0.2, 0.5, 0.02),

        # (D_RSI < {w3})
        'w3': hp.quniform('w3', 0.6, 0.9, 0.02),

        # (D_RSI_Min3M < {w4})
        'w4': hp.quniform('w4', 0.3, 0.6, 0.02),

        # (D_RSI_Min1W > {w5})
        'w5': hp.quniform('w5', 0.01, 0.15, 0.01),

        # (D_RSI_Min1W/D_RSI_Min3M > {w6})
        'w6': hp.quniform('w6', 1.05, 1.34, 0.02),

        # (D_RSI_Min1W_Close/D_RSI_Min3M_Close < {w7})
        'w7': hp.quniform('w7', 1.2, 1.8, 0.05),

        # (FSCORE > {w8})
        'w8': hp.quniform('w8', 1, 7, 1),

        # (PE < {w9})
        'w9': hp.quniform('w9', 8, 15, 0.2),

        # (PE > {w10})
        'w10': hp.quniform('w10', 2, 6, 0.2),

        # (PB < {w11})
        'w11': hp.quniform('w11', 3, 7, 0.1),

        # (ROE_Min5Y > {w12})
        'w12': hp.quniform('w12', 0.02, 0.1, 0.005),

        # (PCF < {w13})
        'w13': hp.quniform('w13', 20, 40, 0.5),

        # (PCF > {w14})
        'w14': hp.quniform('w14', 0.5, 5, 0.5),

        # ((Cash_P0 / (LtDebt_P0+1) > {w15}) | (abs(IntCov_P0) > {w16}))
        'w15': hp.quniform('w15', 0.01, 0.08, 0.002),
        'w16': hp.quniform('w16', 3, 8, 0.5),

        # ((CF_OA_5Y/OShares) > {w17})
        'w17': hp.quniform('w17', 4000, 9000, 200),

        # (NP_P0/NP_P4 >= {w18})
        'w18': hp.quniform('w18', 1.1, 1.6, 0.05),

        'w_ma21': hp.quniform('w_ma21', 0, 1, 1),
        'w_ma31': hp.quniform('w_ma31', 0, 1, 1),
        'w_ma41': hp.quniform('w_ma41', 0, 1, 1),
        'w_s13': hp.quniform('w_s13', 0, 1, 1),
        'w_selllowgrowth': hp.quniform('w_selllowgrowth', 0, 1, 1),
        'w_sellresistance1y': hp.quniform('w_sellresistance1y', 0, 1, 1),
        'w_sellresistance1m': hp.quniform('w_sellresistance1m', 0, 1, 1),
        'w_sellresistance': hp.quniform('w_sellresistance', 0, 1, 1),
        'w_sellbv': hp.quniform('w_sellbv', 0, 1, 1),
        'w_sellbv2': hp.quniform('w_sellbv2', 0, 1, 1),
        'w_sellpe': hp.quniform('w_sellpe', 0, 1, 1),
        'w_sellvolmax': hp.quniform('w_sellvolmax', 0, 1, 1),
        'w_beardvg2': hp.quniform('w_beardvg2', 0, 1, 1),
    }

    init_vals = [{
        'w1': 0.74,  # D_RSI / D_RSI_T1 > 0.74
        'w2': 0.35,  # D_RSI > 0.35
        'w3': 0.82,  # D_RSI < 0.82
        'w4': 0.43,  # D_RSI_Min3M < 0.43
        'w5': 0.02,  # D_RSI_Min1W > 0.02
        'w6': 1.13,  # D_RSI_Min1W/D_RSI_Min3M > 1.13
        'w7': 1.5,  # D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.5
        'w8': 4,  # FSCORE > 4.0
        'w9': 11.5,  # PE < 11.5
        'w10': 3,  # PE > 3.0
        'w11': 5.5,  # PB < 5.5
        'w12': 0.045,  # ROE_Min5Y > 0.045
        'w13': 31.5,  # PCF < 31.5
        'w14': 1,  # PCF > 1.0
        'w15': 0.034,  # Cash_P0 / (LtDebt_P0 + 1) > 0.034
        'w16': 5,  # abs(IntCov_P0) > 5.0
        'w17': 6400,  # CF_OA_5Y/OShares > 6400.0
        'w18': 1.4,  # NP_P0/NP_P4 >= 1.4

        'w_ma21': 1,
        'w_ma31': 1,
        'w_ma41': 1,
        'w_s13': 1,
        'w_selllowgrowth': 1,
        'w_sellresistance1y': 1,
        'w_sellresistance1m': 1,
        'w_sellresistance': 1,
        'w_sellbv': 1,
        'w_sellbv2': 1,
        'w_sellpe': 1,
        'w_sellvolmax': 1,
        'w_beardvg2': 1,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_BullDVG_50_allo.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)
    # trials = pickle.load(open("tuning/trials_BullDvg.pkl", "rb"))
    # trials = Trials()

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
