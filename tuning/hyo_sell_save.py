### MA21 xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (MA20/MA50<{w1}) & (MA20_T1/MA50_T1>{w2})  &  (D_RSI/D_RSI_T1W < {w3}) & (Close < {w4}*VAP1M)  & (D_MACDdiff< {w5}) & (Close/Close_T1W < {w6})",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100
        logging.info(f"\n")
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.8, 1.3, 0.01),
        'w3': hp.quniform('w3', 0.8, 1.3, 0.01),
        'w4': hp.quniform('w4', 0.8, 1.3, 0.01),
        'w5': hp.quniform('w5', -15, 10, 1),
        'w6': hp.quniform('w6', 0.8, 1.3, 0.01),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 0.95,
        'w4': 1,
        'w5': 0,
        'w6': 0.95
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_MA21.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

###MA31 xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG', 'TT6', 'TSA', 'AVG', 'BGE']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (MA10/MA200<{w1}) & (MA10_T1/MA200_T1>{w2}) & (Close < {w3}*VAP3M)  & (Close/Close_T1W < {w4})& (D_RSI/D_RSI_T1W < {w5}) & (D_RSI < {w6}) & (D_MACDdiff< {w7})",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100
        logging.info(f"\n")
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.85, 1.2, 0.01),
        'w2': hp.quniform('w2', 0.85, 1.2, 0.01),
        'w3': hp.quniform('w3', 0.85, 1.2, 0.01),
        'w4': hp.quniform('w4', 0.8, 1.05, 0.01),
        'w5': hp.quniform('w5', 0.8, 1.05, 0.01),
        'w6': hp.quniform('w6', 0.2, 0.7, 0.01),
        'w7': hp.quniform('w7', -15, 10, 1),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 0.98,
        'w4': 0.95,
        'w5': 0.95,
        'w6': 0.5,
        'w7': 0
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_MA31.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### MA41 xX
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG', 'TT6', 'TSA', 'AVG', 'BGE']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (Close > {w1}*MA200) & (NP_P0/NP_P1 < {w2}) & (Volume>{w3}*Volume_3M_P50)  & (Close/Close_T1W < {w4}) & (Close < {w5}*VAP1M)",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100
        logging.info(f"\n")
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 1.2, 1.8, 0.01),
        'w2': hp.quniform('w2', 0.85, 1.2, 0.01),
        'w3': hp.quniform('w3', 0.85, 1.2, 0.01),
        'w4': hp.quniform('w4', 0.8, 1.05, 0.01),
        'w5': hp.quniform('w5', 0.8, 1.05, 0.01),
    }

    init_vals = [{
        'w1': 1.5,
        'w2': 1,
        'w3': 1,
        'w4': 0.95,
        'w5': 0.98,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_MA31.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### S13 xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['ECO', 'RYG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (C_L1W>={w1}) & (D_CMB_Peak_T1>{w2}*D_CMB) & (Close>{w3}*MA10) & (D_CMB_XFast<{w4})",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100
        logging.info(f"\n")
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 1, 1.6, 0.01),
        'w2': hp.quniform('w2', 0.85, 1.2, 0.01),
        'w3': hp.quniform('w3', 1, 1.8, 0.01),
        'w4': hp.quniform('w4', 0, 7, 1),
    }

    init_vals = [{
        'w1': 1.2,
        'w2': 1,
        'w3': 1.3,
        'w4': 2,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_S13.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### SellBV xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = []

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (Close > {w1}*BVPS) & (NP_P0 /NP_P1 < {w2}) &(Close < {w3}*VAP1M) & (Close_T1W > {w4}*VAP1M) & (Volume > {w5}* Volume_3M_P50) & (ICB_Code != 8633)",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100
        logging.info(f"\n")
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 1.7, 2.8, 0.05),
        'w2': hp.quniform('w2', 0.6, 1.2, 0.01),
        'w3': hp.quniform('w3', 0.75, 1.2, 0.01),
        'w4': hp.quniform('w4', 0.85, 1.25, 0.01),
        'w5': hp.quniform('w5', 0.95, 1.4, 0.01),
    }

    init_vals = [{
        'w1': 2,
        'w2': 0.85,
        'w3': 1,
        'w4': 1,
        'w5': 1.15,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_SellBV.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(700),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### SellBV2 xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['ECO', 'AIG', 'MZG', 'RYG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (PB > {w1}*PB_MA5Y + {w2}*PB_SD5Y) & (NP_P0 /NP_P1 < {w3})  & (Close < {w4}*VAP1M) & (Close_T1W > {w5}*VAP1M)  & (D_RSI > {w6}) & (Volume > {w7}*Volume_3M_P50)",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100

        logging.info(filter)
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.8, 1.3, 0.01),
        'w3': hp.quniform('w3', 0.5, 1.1, 0.01),
        'w4': hp.quniform('w4', 0.8, 1.3, 0.01),
        'w5': hp.quniform('w5', 0.8, 1.3, 0.01),
        'w6': hp.quniform('w6', 0.1, 0.65, 0.01),
        'w7': hp.quniform('w7', 1, 1.4, 0.01),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 0.85,
        'w4': 0.98,
        'w5': 1,
        'w6': 0.32,
        'w7': 1.15
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_SellBV2.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(700),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### SellPE xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['ECO', 'AIG', 'MZG', 'RYG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (PE >= {w1}*PE_MA5Y  + {w2}*PE_SD5Y) & (NP_P0 /NP_P1 < {w3})  & (Close < {w4}*VAP3M) & (Close_T1W > {w5}*VAP3M)  & (Close/Close_T1W < {w6})  & (Volume > {w7}*Volume_3M_P50)",
        }

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100

        logging.info(filter)
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.8, 1.3, 0.01),
        'w3': hp.quniform('w3', 0.5, 1.1, 0.01),
        'w4': hp.quniform('w4', 0.8, 1.3, 0.01),
        'w5': hp.quniform('w5', 0.8, 1.3, 0.01),
        'w6': hp.quniform('w6', 0.7, 1.3, 0.01),
        'w7': hp.quniform('w7', 0.8, 1.3, 0.01),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 0.85,
        'w4': 1,
        'w5': 1,
        'w6': 0.95,
        'w7': 1
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_SellPE.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(700),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### BearDVG2 xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['ECO', 'AIG', 'MZG', 'RYG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (D_RSI_Max1W/D_RSI > {w1})  & (D_RSI_Max3M > {w2})& (D_RSI_Max1W < {w3}) & (D_RSI_Max1W >{w4}) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > {w5}) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > {w6})  & (D_RSI_T1/D_RSI > {w7}) & (Volume > {w8}*Volume_1M)",
        }
        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100

        logging.info(filter)
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.5, 1, 0.01),
        'w3': hp.quniform('w3', 0.65, 0.8, 0.01),
        'w4': hp.quniform('w4', 0.5, 0.65, 0.01),
        'w5': hp.quniform('w5', 0.9, 1.4, 0.01),
        'w6': hp.quniform('w6', 1, 1.5, 0.01),
        'w7': hp.quniform('w7', 0.8, 1.3, 0.01),
        'w8': hp.quniform('w8', 0.9, 1.4, 0.01),
    }

    init_vals = [{
        'w1': 1.03,
        'w2': 0.75,
        'w3': 0.69,
        'w4': 0.6,
        'w5': 1.14,
        'w6': 1.25,
        'w7': 1,
        'w8': 1.18
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_BearDvg2.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(700),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### SellVolMax xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['ECO', 'AIG', 'MZG', 'RYG', 'SBG', 'SBB', 'GDA', 'BCR', 'NCG', 'THM', 'AVG', 'BGE', 'BHI', 'BHI', 'TT6',
                'ABW', 'TSA', 'HIO', 'AAH', 'QNP', 'TAL']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (Close/Volume_MaxTop5_2Y_Close < {w1}) & (ID_Current - Volume_MaxTop5_2Y_ID <={w2}) & (Close < {w3}*VAP1W) & (D_RSI > {w4})  & (Close/Close_T1 < {w5}) & (D_RSI/D_RSI_T1W < {w6}) & (Close_T1/LO_3M_T1 > {w7})",
        }
        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * penalty / 100

        logging.info(filter)
        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.75, 1.3, 0.01),
        'w2': hp.quniform('w2', 100, 150, 1),
        'w3': hp.quniform('w3', 0.8, 1.3, 0.01),
        'w4': hp.quniform('w4', 0.15, 0.5, 0.01),
        'w5': hp.quniform('w5', 0.8, 1.3, 0.01),
        'w6': hp.quniform('w6', 0.8, 1.3, 0.01),
        'w7': hp.quniform('w7', 1.1, 1.7, 0.01),
    }

    init_vals = [{
        'w1': 0.9,
        'w2': 120,
        'w3': 1,
        'w4': 0.32,
        'w5': 0.98,
        'w6': 0.95,
        'w7': 1.3,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_SellVolMax.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(700),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### SellResistance xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        # w5 = params['w5']
        # w6 = params['w6']
        # w7 = params['w7']
        # w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (Open/Close< {w1})  & (Close  <  {w2}*Res_1Y)  & (Close/LO_3M_T1 > {w3}) & (Volume > {w4}*Volume_3M_P50)",
        }
        logging.info(filter)

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * win_deal * penalty / 1000

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.8, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.8, 1.3, 0.01),
        'w3': hp.quniform('w3', 1, 1.5, 0.01),
        'w4': hp.quniform('w4', 1.7, 2.5, 0.01),
    }

    init_vals = [{
        'w1': 0.97,
        'w2': 0.96,
        'w3': 1.3,
        'w4': 2,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_SellResistance.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### Sell Resistance1M xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        # w6 = params['w6']
        # w7 = params['w7']
        # w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= {w1}) & (Close < {w2}*VAP1M) & (Close_T1 >  {w3}*VAP1M) & (Volume > {w4}* Volume_3M_P50)& (D_RSI > {w5})",
        }
        logging.info(filter)

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * win_deal * penalty / 1000

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 15, 40, 1),
        'w2': hp.quniform('w2', 0.85, 1.2, 0.01),
        'w3': hp.quniform('w3', 0.85, 1.2, 0.01),
        'w4': hp.quniform('w4', 0.85, 1.3, 0.01),
        'w5': hp.quniform('w5', 0.2, 0.4, 0.01),
    }

    init_vals = [{
        'w1': 30,
        'w2': 0.98,
        'w3': 1,
        'w4': 1.1,
        'w5': 0.32,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_SellResistance1M.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### Sell Resistance3M xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        # w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000) & (ID_Current - ID_XVAP3M_Down_P2 <= {w1}) & (ID_XVAP3M_Down_P0 - ID_XVAP3M_Down_P1 >= {w2})  & (Close < {w3}*VAP3M) & (Close_T1W > {w4}*VAP3M)  & (Close/Close_T1 < {w5}) & (D_RSI > {w6}) & (Volume >  {w7} *Volume_3M_P50)",
        }
        logging.info(filter)

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * win_deal * penalty / 1000

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 55, 85, 1),
        'w2': hp.quniform('w2', 1, 30, 1),
        'w3': hp.quniform('w3', 0.85, 1.2, 0.01),
        'w4': hp.quniform('w4', 0.85, 1.2, 0.01),
        'w5': hp.quniform('w5', 0.85, 1.2, 0.01),
        'w6': hp.quniform('w6', 0.2, 0.4, 0.01),
        'w7': hp.quniform('w7', 0.85, 1.2, 0.01),
    }

    init_vals = [{
        'w1': 70,
        'w2': 4,
        'w3': 0.97,
        'w4': 1.03,
        'w5': 0.98,
        'w6': 0.32,
        'w7': 1,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_SellResistance3M.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)

### Sell Resistance1Y xx
if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    import random

    random.seed(0)


    def objective(params):
        skip = ['MZG', 'RYG', 'ECO', 'AIG']

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        # w8 = params['w8']

        Init = "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        filter = {
            # "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
            "_PS1": "Price < 0",
            "~SellPattern": f"",
        }
        logging.info(filter)

        result = eval_filter_all_v2(filter, CUTLOSS=1, skip=skip)
        print(filter)
        profit_expected = result['profit_expected']
        win_deal = result['win_deal']
        deal = result['deal']

        penalty = result['penalty']
        loss = deal * profit_expected * win_deal * penalty / 1000

        logging.info(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        logging.info(f"\n")

        print(f'return: {profit_expected}, win_deal: {win_deal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0.85, 1.3, 0.01),
        'w2': hp.quniform('w2', 0.85, 1.3, 0.01),
        'w3': hp.quniform('w3', 0.65, 1, 0.01),
        'w4': hp.quniform('w4', 0.85, 1.3, 0.01),
        'w5': hp.quniform('w5', 1.2, 2.2, 0.01),
        'w6': hp.quniform('w6', 0.85, 1.3, 0.01),
        'w7': hp.quniform('w7', 0.1, 0.5, 0.01),
    }

    init_vals = [{
        'w1': 1,  # Hệ số nhân của PB_MA5Y
        'w2': 1,  # Hệ số nhân của PB_SD5Y
        'w3': 0.85,  # Tỷ lệ NP_P0 / NP_P1
        'w4': 0.96,  # Hệ số nhân của Res_1Y
        'w5': 1.4,  # Hệ số nhân của Volume_3M_P50
        'w6': 1,  # Hệ số nhân của VAP1M
        'w7': 0.32,  # Giá trị ngưỡng của D_RSI
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_SellResistance1Y.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
