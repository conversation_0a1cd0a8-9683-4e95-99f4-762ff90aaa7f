import numpy as np
import pandas as pd
from ast import literal_eval
from prepare import prepare_pdxy
# from sklearn.metrics import roc_auc_score
import json
from utils import *
from IPython.core.display_functions import display
from tabulate import tabulate


def SMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['MA10']].copy()
    relationship_matrix(df, title=f'minL1 < MA10: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA20']].copy()
    relationship_matrix(df, title=f'minL1 < MA20: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA50']].copy()
    relationship_matrix(df, title=f'minL1 < MA50: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA100']].copy()
    relationship_matrix(df, title=f'minL1 < MA100: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA200']].copy()
    relationship_matrix(df, title=f'minL1 < MA200: {df.shape[0]}')


def EMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['EMA']].copy()
    relationship_matrix(df, title=f'minL1 < EMA: {df.shape[0]}')


def RSI(df_raw):
    df = df_raw[df_raw['D_RSI'] < 0.1].copy()
    relationship_matrix(df, title=f'D_RSI < 0.1: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.2].copy()
    relationship_matrix(df, title=f'D_RSI < 0.2: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.3].copy()
    relationship_matrix(df, title=f'D_RSI < 0.3: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.4].copy()
    relationship_matrix(df, title=f'D_RSI < 0.4: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.5].copy()
    relationship_matrix(df, title=f'D_RSI < 0.5: {df.shape[0]}')


def MACD(df):
    df = df[np.where((df['D_MACD'].shift(1) < df['D_MACDsign'].shift(1)) &
                     (df['D_MACD'] > df['D_MACDsign']), True, False)].copy()
    relationship_matrix(df, title=f'MACD cắt lên D_MACDsign : {df.shape[0]}')


def Bollinger_Bands(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['BB_lower']]
    relationship_matrix(df, title=f'minL1 < BB_lower : {df.shape[0]}')


def random_range(start, end, step):
    return round(np.random.choice(list(np.arange(start, end + step, step))), 4)


def hammer(df_raw):
    df_raw = df_raw.copy()
    body = abs(df_raw['Close'] - df_raw['Open'])
    lo_shadow = abs(df_raw[['Open', 'Close']].min(axis=1) - df_raw['Low'])
    up_shadow = abs(df_raw['High'] - df_raw[['Open', 'Close']].max(axis=1))
    length = df_raw['High'] - df_raw['Low']

    lower_shadow_ratio = lo_shadow / body
    # Tỷ lệ > 3: Hammer mạnh, tín hiệu đảo chiều cao.
    # Tỷ lệ từ 2 đến 3: Hammer trung bình, tín hiệu đảo chiều trung bình.
    # Tỷ lệ < 2: Hammer yếu, tín hiệu đảo chiều thấp.
    t_1 = random_range(3, 5, 0.5)
    score_1 = lower_shadow_ratio.transform(lambda x: min(x / t_1, 1))

    upper_shadow_ratio = up_shadow / body
    # Tỷ lệ < 0.1: Hammer mạnh, tín hiệu tốt.
    # Tỷ lệ từ 0.1 đến 0.25: Hammer trung bình, tín hiệu đảo chiều trung bình.
    # Tỷ lệ > 0.25: Hammer yếu, tín hiệu không đáng tin cậy.
    t_2 = random_range(0.25, 0.5, 0.05)
    score_2 = 1 - upper_shadow_ratio.transform(lambda x: min(x / t_2, 1))

    real_body_ratio = body / length
    # Tỷ lệ < 0.3: Hammer mạnh, vì thân nến nhỏ so với bóng nến, cho thấy sự từ chối giá mạnh.
    # Tỷ lệ từ 0.3 đến 0.5: Hammer trung bình.
    # Tỷ lệ > 0.5: Hammer yếu, thân nến quá dài so với tổng chiều dài nến.
    t_3 = random_range(0.5, 0.7, 0.05)
    score_3 = 1 - real_body_ratio.transform(lambda x: min(x / t_3, 1))

    recovery_factor = (df_raw['Close'] - df_raw['Low']) / (df_raw['Open'] - df_raw['Low'])
    # Recovery Factor > 0.8: Hammer mạnh, thể hiện sự hồi phục giá mạnh mẽ.
    # Recovery Factor từ 0.6 đến 0.8: Hammer trung bình.
    # Recovery Factor < 0.6: Hammer yếu, sự hồi phục không đủ mạnh
    t_4 = random_range(0.6, 0.8, 0.05)
    score_4 = recovery_factor.transform(lambda x: min(x / t_4, 1))

    volume_increase_ratio = df_raw['Volume'] / df_raw['Volume'].shift(1)
    t_5 = random_range(1.5, 2, 0.5)
    score_5 = volume_increase_ratio.transform(lambda x: min(x / t_5, 1))

    cols = ['score_1', 'score_2', 'score_3', 'score_4', 'score_5']
    df_sample = pd.concat(
        (lower_shadow_ratio, upper_shadow_ratio, real_body_ratio, recovery_factor, volume_increase_ratio), axis=1)
    df = pd.concat((score_1, score_2, score_3, score_4, score_5), axis=1, keys=cols)
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.fillna(0, inplace=True)

    df['score'] = 0.0
    for i in range(df.shape[0]):
        if not (df.iloc[i][cols] == 0).any():
            df.loc[i, 'score'] = (df.loc[i][cols]).mean()
    # df_raw['HM_C'] = df['score']
    # df = df_raw[(lo_shadow >= 2) & (up_shadow <= body) & (df_raw['Close'] > df_raw['Open'])]
    print(f'{t_1}-{t_2}-{t_3}-{t_4}-{t_5}')
    return df['score']

def eval():
    model = ""
    # dataset = prepare_pdxy_v1()
    # pd_raw = dataset['pdxy']
    # feature_cols = dataset['cname_feature']
    # label_cols = dataset['list_label']
    # list_eval = dataset['list_eval']
    # #
    # cols = feature_cols + label_cols
    # train_dataset = pd_raw[list_eval[0][1]]
    # val_dataset = pd_raw[list_eval[3][1]]
    # train_dataset.to_csv('train.csv', index=False)
    # val_dataset.to_csv('val.csv', index=False)

    pd_xy = pd.read_csv('train.csv', converters={'Close_TT20': literal_eval})
    list_label = ['minW5D', 'minW10D', 'minW20D', 'minL1', 'minL2', 'minL3', 'minL4', 'minL5', 'label1',
                  'label2', 'label3', 'label4', 'label5']
    cname_feature = [f for f in pd_xy.columns if f not in ['time', 'ticker', 'ymd', 'L1W', 'H1M', 'H3M', 'H1Y', 'H2Y',
                                                           'L1W', 'L1M', 'L3M', 'L1Y', 'L2Y', 'quarter',
                                                           'Open', 'Close', 'High', 'Low', 'Volume', 'Close_TT20',
                                                           'L1W', 'L1M', 'L3M', 'L1Y', 'L2Y', 'H1W', 'H1M', 'H3M',
                                                           'H1Y', 'H2Y'] + list_label]
    # cols = []
    # for i in range(10):
    #     print(i)
    #     pd_xy[f'HM_C_{i}'] = hammer(pd_xy)
    #     cols.append(f'HM_C_{i}')
    # relationship_variable_bin(df, 'Close_TT20', 'minL1', 'minW1')
    # display(get_bin_stats(pd_xy, 'D_RSI', 'label3'))
    display(get_bin_stats_2d(pd_xy, 'D_RSI', 'MA200', 'label3'))
    # pd_xy['min35'] = pd_xy['minL3'] / pd_xy['minW5D']
    # selector = FeatureSelector(pd_xy, cols + ['D_RSI', 'MI_10'], 'label3')
    # selector = FeatureSelector(pd_xy, cols + cname_feature, 'label3')
    # aggregated_scores, corr_score = selector.aggregate_scores()
    # # aggregated_scores.to_csv('corr.csv', index=True)
    # print(tabulate(aggregated_scores, headers='keys', tablefmt='psql'))


if __name__ == '__main__':
    eval()
