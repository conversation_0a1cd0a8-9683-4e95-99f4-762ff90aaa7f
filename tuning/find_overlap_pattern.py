import argparse
import json
import os
import time
import warnings
from datetime import timed<PERSON><PERSON>
from itertools import combinations

import pandas as pd
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.base_eval import TickerEval
from core_utils.redis_cache import EvalRedis

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()

os.environ["SERVICE"] = "WEBUI"
from core_utils.log import logger

warnings.simplefilter(action='ignore')


class TickerEval_overlap(TickerEval):
    def __init__(self, stock, df_all, dict_filter, cutloss=0.15, cache_service=None):
        super().__init__(stock, df_all, dict_filter, cutloss, cache_service)

    def find_buy_overlap(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'

        df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        result = {'total': 0}
        for f in self.b_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        count = []
        for _, data in df.iterrows():
            if data['status'] == 'sell':
                count = [c for c in count if not pd.isna(c)]
                if len(count) == 1:
                    result[count[0]] += 1
                    result[f'total_{count[0]}'] += 1
                    result['total'] += 1

                elif len(count) > 1:
                    for f in count:
                        # result[f'total_{f}'] += len(count)
                        result[f'total_{f}'] += 1
                    result['total'] += 1

                count = []
            elif data['status'] == 'buy':
                if data['filter'] not in count:
                    count.append(data['filter'])

        return result

    def find_sell_overlap(self):
        """
        Finds the overlap of sell signals in the dataset.

        This method processes sell signals, counts the occurrences of each filter, if the sell signal is appear in
        same week and calculates the total overlap of sell signals

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """

        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell[['time', 'Sell_filter']].copy()
        pd_sell['status'] = 'sell'

        # pd_sell['week'] = pd.to_datetime(pd_sell['time']).dt.to_period('W').dt.strftime('%Y-%U')
        # pd_sell['month'] = pd_sell['time'].str[:7]

        result = {'total': 0}
        for f in self.s_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        # group and count each sell signal in same week
        for date, group in pd_sell.groupby('time'):
            filters = [f for f in group['Sell_filter'].unique() if f != 'Hold']

            if len(filters) == 1:
                result[filters[0]] += 1
                result[f'total_{filters[0]}'] += 1
                result['total'] += 1
            elif len(filters) > 1:
                for f in filters:
                    result[f'total_{f}'] += 1
                    result['total'] += 1

        return result

    def find_buy_overlap_pair(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'
        # df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        # a = [list(comb) for comb in combinations(list(self.b_f.keys()), 2)]

        filters = [f[1:] for f in self.b_f.keys()]
        results = {}
        for i in range(len(filters)):
            for j in range(len(filters)):
                result = {'total': 0}
                fi = filters[i]
                fj = filters[j]

                result[fi] = 0
                result[fj] = 0
                result[f'total_{fi}'] = 0
                result[f'total_{fj}'] = 0

                count = []
                pd_buy_slice = pd_buy[pd_buy['filter'].isin([filters[i], filters[j]])]
                df = pd.concat([pd_buy_slice, pd_sell], axis=0).sort_values('time', ascending=True)

                for _, data in df.iterrows():
                    if data['status'] == 'sell':
                        count = [c for c in count if not pd.isna(c)]
                        if len(count) == 1:
                            result[count[0]] += 1
                            result[f'total_{count[0]}'] += 1
                            result['total'] += 1

                        elif len(count) > 1:
                            for f in count:
                                # result[f'total_{f}'] += len(count)
                                result[f'total_{f}'] += 1
                            result['total'] += 1

                        count = []
                    elif data['status'] == 'buy':
                        if data['filter'] not in count:
                            count.append(data['filter'])

                results[f'{fi}_to_{fj}'] = result[fi]
                results[f'total_{fi}_to_{fj}'] = result[f'total_{fi}']
                results[f'{fj}_to_{fi}'] = result[fj]
                results[f'total_{fj}_to_{fi}'] = result[f'total_{fj}']
        return results

    def overlap(self):
        res_s = self.find_sell_overlap()
        res_b = self.find_buy_overlap()
        res = dict()
        res.update(res_b)
        res.update(res_s)
        return res


def parse_input():
    """
    Parses command-line inputs provided by the user.
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Template for Python script with user input.")

    parser.add_argument('--profiles', type=str, default='email', help='Input data file or raw input string')
    parser.add_argument('--cutloss', type=float, default=0.15, help='cutloss')
    parser.add_argument('--num_procs', type=float, default=8, help='cutloss')

    return parser.parse_args()


def main():
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """
    args = parse_input()
    configFilters = json.loads(open("config.json", "r").read())
    dictFilter = json.loads(configFilters[args.profiles][0])
    if 'Init' in dictFilter.keys():
        for key, value in dictFilter.items():
            dictFilter[key] = value.replace("{Init}", dictFilter['Init'])
    cutloss = args.cutloss
    print("profile:", args.profiles)
    print(dictFilter)

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval_overlap(ticker, pdxx, dictFilter, cutloss=cutloss, cache_service=redis_cache)
            res1 = eval_ticker.find_buy_overlap_pair()
            res2 = eval_ticker.overlap()

            return (res1, res2)
        except Exception as error:
            logger.error(f"Error: {ticker}: {error}")

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    # Process dataframe
    df_pair = pd.json_normalize([lr[0] for lr in lres])
    df_overall = pd.json_normalize([lr[1] for lr in lres])
    # For pair of buy

    matrix_cols = [col[1:] for col in dictFilter.keys() if col.startswith('_')]
    matrix_cols.sort()
    df_matrix = pd.DataFrame(index=matrix_cols, columns=matrix_cols)

    df_pair = df_pair.sum(axis=0)
    for col1 in matrix_cols:
        for col2 in matrix_cols:
            df_matrix.loc[col1, col2] = (
                    (df_pair[f'{col1}_to_{col2}'] / df_pair[f'total_{col1}_to_{col2}']) * 100).round(2)

    # For overlap overall
    df_cols = [col for col in df_overall.columns if 'total' not in col]  # + ['total']
    df_result = pd.DataFrame(index=df_cols)

    # df_result.loc['total', 'exclusive_count'] = df['total'].sum()
    df_result['exclusive_count'] = 0
    df_result['total_count'] = 0
    df_result['exclusive_index'] = 0

    df_overall = df_overall.sum(axis=0)
    for col in df_cols:
        df_result.loc[col, 'exclusive_count'] = df_overall[col]
        df_result.loc[col, 'total_count'] = df_overall[f'total_{col}']
        df_result.loc[col, 'exclusive_index'] = (df_overall[col] / df_overall[f'total_{col}']) * 100

    # Save
    df_matrix.to_csv(f'tuning/temp/{args.profiles}_overlap_pair.csv', index=True)
    df_result.to_csv(f'tuning/temp/{args.profiles}_overlap_overall.csv', index=True)


if __name__ == "__main__":
    import sys
    import random

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/tuning", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)
    random.seed(123)

    main()
    pass
