import datetime
import json
import pickle
import sys
import os
import io
import time

import numpy as np
from matplotlib import pyplot as plt

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning/exp_indicator", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)
# # external_script.py
import os
from core_utils.gcstorage import GoogleCloudStorageService
from core_utils.stockquery import StockQuery
import pandas as pd
from explo import tdlib as td
import plotly.graph_objects as go
import plotly.subplots as sp

bucket_name = "tav2-gs"
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "core_utils/env/gcskey.json"
gcs_service = GoogleCloudStorageService(bucket_name)
stock_query = StockQuery(end_date='2026-01-01')

# Loop through all tickers

verbose = True


# Load data from GCS
def compute_data(ticker):
    # Download preprocess data from GCS
    # try:
    #     pdraw = gcs_service.download_file_to_memory(f"preprocess/adjusted_price/{ticker}.csv")
    #     pd_unadj = gcs_service.download_file_to_memory(f"preprocess/price/{ticker}.csv")
    # except:
    #     return
    #
    # pd_unadj = pd.read_csv(io.BytesIO(pd_unadj))
    # pdraw = pd.read_csv(io.BytesIO(pdraw))
    #
    # pdraw = pdraw.merge(pd_unadj, on='time', how='left')
    # pdraw['Price'] = pdraw[['Close', 'Price']].max(axis=1)

    pdraw = pd.read_csv(f'ticker_v1a/{ticker}.csv')

    # Calculate for condition in the future
    pd_condition = pdraw.copy()
    pd_condition = pd_condition.sort_values('time', ascending=False).reset_index(drop=True)
    pd_condition['percentile_15_6M_f'] = pd_condition['Close'].rolling(20 * 6).apply(lambda x: x.quantile(0.15))
    pd_condition['min_3M_f'] = pd_condition['Close'].rolling(20 * 3).min()
    pd_condition['max_3M_f'] = pd_condition['Close'].rolling(20 * 3).max()
    pd_condition['min_6M_f'] = pd_condition['Close'].rolling(20 * 6).min()
    pd_condition['max_6M_f'] = pd_condition['Close'].rolling(20 * 6).max()

    # Calculate support/resistance was defined
    pdraw['res_v1'], pdraw['sup_v1'] = calculate_support_resistance_v1(pdraw)
    pdraw['res_v2'], pdraw['sup_v2'] = calculate_support_resistance_v2(pdraw)

    # Merge data
    # pdxy = pdx1.merge(pdx2, on='time', how='left')
    cols = [col for col in pd_condition.columns if col not in pdraw.columns] + ['time']
    pd00 = pdraw.merge(pd_condition[cols], on='time', how='left')

    # Backtest support/resistance
    backtest_service = backtest_support_resistance(pd00, '2014-01-01', '2024-01-01')
    result = {}
    for res, sup in [['res_v1', 'sup_v1'], ['res_v2', 'sup_v2']]:
        res_score = backtest_service.resistance(res)
        sup_score = backtest_service.support(sup)
        result[res + '_' + sup] = res_score + sup_score

    return result


# Define support/resistance function
def calculate_support_resistance_v1(pdraw, x_period=1, x_up_unchange=240 * 3, x_down_unchange=240 * 3):
    def custom_func(values, close, window):
        n = len(values)
        res = np.full(n, np.nan)
        sup = np.full(n, np.nan)
        prev_value = 0
        counts = {}

        up_unchange = 0
        down_unchange = 0
        bound_up = [-np.inf, 0]
        bound_down = [np.inf, 0]

        for i in range(window - 1, n):
            window_data_raw = values[i - window + 1:i + 1]
            window_data = window_data_raw[~np.isnan(window_data_raw)]
            if i == window - 1:
                for d in window_data:
                    counts[d] = counts.get(d, 0) + 1
            else:
                try:
                    counts[prev_value] -= 1
                    if counts[prev_value] == 0:
                        del counts[prev_value]
                except:
                    pass
                counts[window_data[-1]] = counts.get(window_data[-1], 0) + 1

            close_price = close[i - 5:i].mean()
            # find
            # smoothing
            ###
            smoothing = {}
            values_unit = list(counts.keys())
            values_unit.sort(reverse=True)
            values_unit.append(-np.inf)
            for uni in range(len(values_unit) - 1):
                if values_unit[uni] - values_unit[uni + 1] < close_price * 0.04:
                    key = (values_unit[uni] * counts[values_unit[uni]] + values_unit[uni + 1] * counts[
                        values_unit[uni + 1]]) / (counts[values_unit[uni]] + counts[values_unit[uni + 1]])

                    smoothing[int(key)] = counts[values_unit[uni]] + counts[values_unit[uni + 1]]
                else:
                    smoothing[int(values_unit[uni])] = counts[values_unit[uni]]
            # smoothing = dict(sorted(smoothing.items(), key=lambda item: item[1], reverse=True))
            ###

            if up_unchange > x_up_unchange:
                bound_up = [-np.inf, 0]
                up_unchange = 0

            if down_unchange > x_down_unchange:
                # bound_down = [bound_down[0], bound_up[1]*0.9]
                bound_down = [bound_down[0] * 1.3, 0]
                down_unchange = 0

            for k, v in smoothing.items():
                if k > bound_up[0] and v >= bound_up[1]:
                    bound_up = [k, v]
                    up_unchange = 0
                if k < bound_down[0] and v >= bound_down[1]:
                    bound_down = [k, v]
                    down_unchange = 0
                up_unchange += 1
                down_unchange += 1

            if close_price < bound_up[0]:
                res[i] = bound_up[0]
            else:
                bound_down = bound_up

            if close_price > bound_down[0]:
                sup[i] = bound_down[0]
            else:
                bound_up = bound_down
                res[i] = bound_up[0]

            prev_value = window_data_raw[0]
        return res, sup

    pd00 = pdraw.copy()
    pd00['res'], pd00['sup'] = custom_func(pd00['VAP6M'].values, pd00['Close'].values, 240 * x_period)
    if verbose:
        fig = go.Figure(data=[go.Scatter(x=pd00['time'], y=pd00['Close'], name='Close'),
                              go.Scatter(x=pd00['time'], y=pd00['res'], name='res'),
                              go.Scatter(x=pd00['time'], y=pd00['sup'], name='sup'),
                              go.Scatter(x=pd00['time'], y=pd00['VAP1M'], name='VAP1M'),
                              go.Scatter(x=pd00['time'], y=pd00['VAP3M'], name='VAP3M'),
                              go.Scatter(x=pd00['time'], y=pd00['VAP6M'], name='VAP6M')])

        fig.update_layout(title='Support/Resistance V1',
                          xaxis_title='Time',
                          yaxis_title='Price')

        fig.show()

    return pd00['res'], pd00['sup']


def calculate_support_resistance_v2(pdraw, x_period=3, x_res=90, x_sp=20):
    pd00 = pdraw.copy()
    pd00['up'] = pd00['VAP6M'].rolling(240 * x_period).apply(lambda x: np.percentile(x, x_res), raw=True)
    pd00['down'] = pd00['VAP6M'].rolling(240 * x_period).apply(lambda x: np.percentile(x, x_sp), raw=True)
    pd00['MA5'] = td.MA(pd00, n=5, price='Close')
    pd00[['res', 'sup']] = np.nan

    for i in range(pd00.shape[0]):
        if pd00.at[i, 'MA5'] < pd00.at[i, 'up']:
            pd00.at[i, 'res'] = pd00.at[i, 'up']
        else:
            pd00.at[i, 'down'] = pd00.at[i, 'up']

        if pd00.at[i, 'MA5'] > pd00.at[i, 'down']:
            pd00.at[i, 'sup'] = pd00.at[i, 'down']
        else:
            pd00.at[i, 'up'] = pd00.at[i, 'down']
            pd00.at[i, 'res'] = pd00.at[i, 'up']

    if verbose:
        fig = go.Figure(data=[go.Scatter(x=pd00['time'], y=pd00['Close'], name='Close'),
                              go.Scatter(x=pd00['time'], y=pd00['res'], name='res'),
                              go.Scatter(x=pd00['time'], y=pd00['sup'], name='sup')])

        fig.update_layout(title='Support/Resistance V2',
                          xaxis_title='Time',
                          yaxis_title='Price')

        fig.show()

    return pd00['res'], pd00['sup']


def calculate_support_resistance_v3(pdraw, ticker):
    def RES_SUP(pdxx, cname_date='time', period=240):
        pdxx = pdxx[['time', 'Low', 'High', 'Close', 'Volume', ]].copy()
        pdxx['d_time'] = pd.to_datetime(pdxx['time'])
        T = pdxx.shape[0]
        data = {'Price': [], 'Volume': [], cname_date: []}
        pdxx["end"] = 0
        for t in range(T):
            low_value = pdxx.iloc[t]['Low']
            high_value = pdxx.iloc[t]['High']
            if np.isnan(low_value) or np.isnan(high_value):
                lp = []
            else:
                # find_step
                if high_value <= 10e3:
                    price_step = 10
                elif high_value <= 50e3:
                    price_step = 50
                else:
                    price_step = 100

                # lp = list(range(int(low_value), int(high_value) + price_step, price_step))
                lp = list(range(int(pdxx.iloc[t]['Low']), int(pdxx.iloc[t]['High']) + price_step, price_step))
            if len(lp) == 0:
                data['Price'].extend([np.nan])
                data['Volume'].extend([np.nan])
                date = pdxx.iloc[t][cname_date]
                data[cname_date].extend([date])
                pdxx.at[t, 'end'] = len(data[cname_date])
                continue
            data['Price'].extend(lp)
            data['Volume'].extend([pdxx.iloc[t]['Volume'].astype(float) / len(lp)] * len(lp))
            date = pdxx.iloc[t][cname_date]
            data[cname_date].extend([date] * len(lp))
            pdxx.at[t, 'end'] = len(data[cname_date])
        pdy = pd.DataFrame(data)

        pdxx[['res', 'sup', 'vol_res', 'vol_sup']] = np.nan
        before_end_idx = 0
        before_start_idx = 0
        df_range = None

        # df = pdy.copy()
        # df = df.groupby('Price', as_index=False).agg({'Volume': 'sum'}).sort_values('Volume',ascending=False)
        # print('shape: ', df.shape[0])
        #
        # df = df.head(int(0.2 * df.shape[0]))
        # df = df.sort_values('Price', ascending=True).reset_index(drop=True)
        # # smoothing
        # smoothing = []
        # diff = df['Price'].max() * 0.01
        # df_smoothing = []
        # price_standard = df.iloc[0]['Price']
        # for id_dfgroup in range(df.shape[0]):
        #     if df.iloc[id_dfgroup]['Price'] - price_standard < diff:
        #         smoothing.append(df.iloc[id_dfgroup])
        #     else:
        #         smoothing_df = pd.concat(smoothing, axis=1).T
        #         smoothing_df['cap'] = smoothing_df['Price'] * smoothing_df['Volume']
        #
        #         median_cap = smoothing_df['cap'].median()
        #         closest_idx = (smoothing_df['cap'] - median_cap).abs().idxmin()
        #         price_at_median_cap = smoothing_df.loc[closest_idx, 'Price']
        #
        #         df_group = pd.DataFrame(smoothing_df.agg({'Price': 'max', 'Volume': 'sum'})).T
        #         # df_group['Price'] = price_at_median_cap
        #         df_smoothing.append(df_group)
        #
        #         price_standard = df.iloc[id_dfgroup]['Price']
        #         smoothing = [df.iloc[id_dfgroup]]
        #
        # df_smoothing = pd.concat(df_smoothing, ignore_index=True)
        # pass

        for id in range(T):
            if (id - period + 1) < 0:
                continue

            end_idx = pdxx.iloc[id]['end']
            start_idx = pdxx.iloc[id - period + 1]['end']

            if df_range is None:
                before_end_idx = end_idx
                before_start_idx = start_idx
                df_range = pdy.iloc[start_idx:end_idx].copy()
                df_range = df_range.groupby('Price', as_index=False).agg({'Volume': 'sum'}).sort_values('Volume',
                                                                                                        ascending=False)
            else:
                out_of_range = pdy.iloc[before_start_idx:start_idx]
                new_of_range = pdy.iloc[before_end_idx:end_idx]

                out_volume = out_of_range.iloc[0]['Volume']
                new_volume = new_of_range.iloc[0]['Volume']

                df_range.loc[df_range['Price'].isin(out_of_range['Price']), 'Volume'] -= out_volume
                df_range.loc[df_range['Price'].isin(new_of_range['Price']), 'Volume'] += new_volume

                new_prices = new_of_range[~new_of_range['Price'].isin(df_range['Price'])]
                if not new_prices.empty:
                    df_range = pd.concat([df_range, new_prices[['Price', 'Volume']]], ignore_index=True)

                df_range = df_range[df_range['Volume'] > 500]
                df_range = df_range.sort_values('Volume', ascending=False)

                print('shape: ', df_range.shape[0])
                before_end_idx = end_idx
                before_start_idx = start_idx
            if df_range.empty:
                continue
            df = df_range.copy()

            # end_idx = pdxx.iloc[id]['end']
            # start_idx = pdxx.iloc[id - period + 1]['end'] if (id - period + 1) > 0 else 0
            # # if id == 477:
            # #     continue
            # df = pdy.iloc[start_idx:end_idx].copy()
            # df = df.groupby('Price', as_index=False).agg({'Volume': 'sum'}).sort_values('Volume', ascending=False)
            # print('shape: ', df.shape[0])
            # if df.empty:
            #     continue

            df = df.head(int(0.2 * df.shape[0]))
            df = df.sort_values('Price', ascending=True).reset_index(drop=True)
            # smoothing
            smoothing = []
            diff = df['Price'].max() * 0.01
            df_smoothing = []
            price_standard = df.iloc[0]['Price']
            for id_dfgroup in range(df.shape[0]):
                if df.iloc[id_dfgroup]['Price'] - price_standard < diff:
                    smoothing.append(df.iloc[id_dfgroup])
                else:
                    smoothing_df = pd.concat(smoothing, axis=1).T
                    smoothing_df['cap'] = smoothing_df['Price'] * smoothing_df['Volume']

                    median_cap = smoothing_df['cap'].median()
                    closest_idx = (smoothing_df['cap'] - median_cap).abs().idxmin()
                    price_at_median_cap = smoothing_df.loc[closest_idx, 'Price']

                    df_group = pd.DataFrame(smoothing_df.agg({'Price': 'max', 'Volume': 'sum'})).T
                    df_group['Price'] = price_at_median_cap
                    df_smoothing.append(df_group)

                    price_standard = df.iloc[id_dfgroup]['Price']
                    smoothing = [df.iloc[id_dfgroup]]

            df_smoothing = pd.concat(df_smoothing, ignore_index=True).sort_values('Volume', ascending=False)
            pdxx.at[id, 'res'] = max(df_smoothing['Price'])
            pdxx.at[id, 'vol_res'] = df_smoothing[df_smoothing['Price'] == max(df_smoothing['Price'])]['Volume'].iloc[0]

            pdxx.at[id, 'sup'] = min(df_smoothing['Price'])
            pdxx.at[id, 'vol_sup'] = df_smoothing[df_smoothing['Price'] == max(df_smoothing['Price'])]['Volume'].iloc[0]




            # top_smo = df_smoothing.head(5)
            # l1 = max(df_smoothing['Price'])
            # v1 = df_smoothing[df_smoothing['Price'] == l1]['Volume'].iloc[0]
            # l2 = max(top_smo['Price'])
            # v2 = top_smo[top_smo['Price'] == l2]['Volume'].iloc[0]
            # l3 = min(top_smo['Price'])
            # v3 = top_smo[top_smo['Price'] == l3]['Volume'].iloc[0]
            # l4 = min(df_smoothing['Price'])
            # v4 = df_smoothing[df_smoothing['Price'] == l4]['Volume'].iloc[0]
            # if pdxx.iloc[id]['Close'] > 1.04 * l1:
            #     pdxx.at[id, 'res'] = pdxx.iloc[id]['Close'] * 2
            #     pdxx.at[id, 'sup'] = l1
            #     pdxx.at[id, 'vol_res'] = v1
            #     pdxx.at[id, 'vol_sup'] = v1
            # elif pdxx.iloc[id]['Close'] > 1.04 * l2:
            #     pdxx.at[id, 'res'] = l1
            #     pdxx.at[id, 'sup'] = l2
            #     pdxx.at[id, 'vol_res'] = v1
            #     pdxx.at[id, 'vol_sup'] = v2
            # elif pdxx.iloc[id]['Close'] > 0.96 * l3:
            #     pdxx.at[id, 'res'] = l2
            #     pdxx.at[id, 'sup'] = l3
            #     pdxx.at[id, 'vol_res'] = v2
            #     pdxx.at[id, 'vol_sup'] = v3
            # elif pdxx.iloc[id]['Close'] > 0.96 * l4:
            #     pdxx.at[id, 'res'] = l3
            #     pdxx.at[id, 'sup'] = l4
            #     pdxx.at[id, 'vol_res'] = v3
            #     pdxx.at[id, 'vol_sup'] = v4
            # else:
            #     pdxx.at[id, 'res'] = l4
            #     pdxx.at[id, 'sup'] = pdxx.iloc[id]['Close'] // 2
            #     pdxx.at[id, 'vol_res'] = v4
            #     pdxx.at[id, 'vol_sup'] = v4

            # pdxx[['res', 'sup', 'strength_res', 'strength_sup', 'strength_res10', 'strength_sup10']] = pdxx[
            #     ['res', 'sup', 'strength_res', 'strength_sup', 'strength_res10', 'strength_sup10']
            # ].ffill()

        pdxx['r_v'] = pdxx['res'] * pdxx['vol_res']
        pdxx['s_v'] = pdxx['sup'] * pdxx['vol_sup']

        pdxx['sum_rv_5'] = pdxx['r_v'].rolling(5).sum()
        pdxx['sum_sv_5'] = pdxx['s_v'].rolling(5).sum()
        pdxx['sum_vol_rv_5'] = pdxx['vol_res'].rolling(5).sum()
        pdxx['sum_vol_sv_5'] = pdxx['vol_sup'].rolling(5).sum()

        pdxx['res_mean_weight_5'] = pdxx['sum_rv_5'] / pdxx['sum_vol_rv_5'].replace(0, np.nan)
        pdxx['sup_mean_weight_5'] = pdxx['sum_sv_5'] / pdxx['sum_vol_sv_5'].replace(0, np.nan)

        return pdxx['res_mean_weight_5'], pdxx['sup_mean_weight_5'], pdxx['res'], pdxx['sup']
        # return pdxx['res'], pdxx['sup'], pdxx['vol_res'], pdxx['vol_sup']

    pd00 = pdraw.copy()
    pd00['res_mean_weight_5'], pd00['sup_mean_weight_5'], pd00['res'], pd00['sup'] = RES_SUP(pdraw, cname_date='time', period=240)
    # pd00['res_5'] = pd00['res'].rolling(5).mean()
    # pd00['sup_5'] = pd00['sup'].rolling(5).mean()
    # pd00['res_10'] = pd00['res'].rolling(10).mean()
    # pd00['sup_10'] = pd00['sup'].rolling(10).mean()
    #
    # pd00['r_v'] = pd00['res'] * pd00['vol_res']
    # pd00['s_v'] = pd00['sup'] * pd00['vol_sup']
    #
    # pd00['sum_vol_rv_5'] = pd00['vol_res'].rolling(5).sum()
    # pd00['sum_vol_sv_5'] = pd00['vol_sup'].rolling(5).sum()
    # pd00['sum_vol_rv_10'] = pd00['vol_res'].rolling(10).sum()
    # pd00['sum_vol_sv_10'] = pd00['vol_sup'].rolling(10).sum()
    #
    # pd00['sum_rv_5'] = pd00['r_v'].rolling(5).sum()
    # pd00['sum_sv_5'] = pd00['s_v'].rolling(5).sum()
    # pd00['sum_rv_10'] = pd00['r_v'].rolling(10).sum()
    # pd00['sum_sv_10'] = pd00['s_v'].rolling(10).sum()
    #
    # pd00['res_mean_weight_5'] = pd00['sum_rv_5'] / pd00['sum_vol_rv_5'].replace(0, np.nan)
    # pd00['sup_mean_weight_5'] = pd00['sum_sv_5'] / pd00['sum_vol_sv_5'].replace(0, np.nan)
    # pd00['res_mean_weight_10'] = pd00['sum_rv_10'] / pd00['sum_vol_rv_10'].replace(0, np.nan)
    # pd00['sup_mean_weight_10'] = pd00['sum_sv_10'] / pd00['sum_vol_sv_10'].replace(0, np.nan)



    # pd00['sup_10'] = pd00['sup'].rolling(10).mean()
    if verbose:
        fig = go.Figure(data=[go.Scatter(x=pd00['time'], y=pd00['Close'], name='Close'),
                              # go.Scatter(x=pd00['time'], y=pd00['Low'], name='Low'),
                              # go.Scatter(x=pd00['time'], y=pd00['High'], name='High'),
                              go.Scatter(x=pd00['time'], y=pd00['res'], name='res'),
                              # go.Scatter(x=pd00['time'], y=pd00['res_5'], name='res_5_mean'),
                              # go.Scatter(x=pd00['time'], y=pd00['res_10'], name='res_10_mean'),
                              go.Scatter(x=pd00['time'], y=pd00['res_mean_weight_5'], name='res_mean_weight_5'),
                              # go.Scatter(x=pd00['time'], y=pd00['res_mean_weight_10'], name='res_mean_weight_10'),
                              go.Scatter(x=pd00['time'], y=pd00['sup'], name='sup'),
                              # go.Scatter(x=pd00['time'], y=pd00['sup_5'], name='sup_5_mean'),
                              # go.Scatter(x=pd00['time'], y=pd00['sup_10'], name='sup_10_mean'),
                              go.Scatter(x=pd00['time'], y=pd00['sup_mean_weight_5'], name='sup_mean_weight_5'),
                              # go.Scatter(x=pd00['time'], y=pd00['sup_mean_weight_10'], name='sup_mean_weight_10'),

                              ])

        fig.update_layout(title=f'Support/Resistance {ticker}',
                          xaxis_title='Time',
                          yaxis_title='Price')

        fig.write_html(f'tuning/exp_indicator/html/{ticker}.html')

        pass


# Backtest support/resistance with condition
class backtest_support_resistance:
    def __init__(self, pdx, start_date, end_date):
        self.pdx = pdx
        self.start_date = start_date
        self.end_date = end_date

    def resistance(self, obj_cname):
        score = 0
        score += self.resistance_Close_at_TOP_N_x_indicator_p(obj_cname, 'Volume', period=240 * 2, top_n=5, percent=5)
        score += self.rule_resistance_p(obj_cname, percentile=95, threshold=4, lookahead=3, period=240 * 2)
        return score

    def support(self, obj_cname):
        score = 0
        score += self.support_Close_at_TOP_N_x_indicator_p(obj_cname, 'Volume', period=240 * 2, top_n=5, percent=5)
        score += self.rule_support_p(obj_cname, percentile=5, threshold=4, lookahead=3, period=240 * 2)
        return score

    def resistance_Close_at_TOP_N_x_indicator_p(self, obj_cname, cname_x, period, top_n=5, percent=5):
        """For resistance line in the past"""

        def fast_find_last_higher_index(values, top_5, window):
            n = len(values)
            result = np.full(n, np.nan)

            for i in range(window - 1, n):
                window_data = values[i - window + 1:i + 1]
                top_value = top_5[i]

                idxs = np.where(window_data > top_value)[0]
                for id in idxs:
                    i_px = i - window + 1 + id
                    if (1 + percent / 100) * self.pdx.loc[i_px, obj_cname] > self.pdx.loc[i_px, "Close"] > \
                            self.pdx.loc[i_px, obj_cname]:
                        result[i] = True

            return result

        # init
        pdx = self.pdx.copy()

        pdx['rank'] = pdx[cname_x].rolling(window=period).agg(lambda x: np.sort(x)[-top_n])
        result = fast_find_last_higher_index(pdx[cname_x].values, pdx['rank'].values, period)
        # Score
        # Score
        true_count = np.sum(result == True) / len(result)
        if true_count > 0.8:
            return 1
        if true_count > 0.6:
            return 0.8
        if true_count > 0.4:
            return 0.6
        if true_count > 0.2:
            return 0.4
        else:
            return 0

    def support_Close_at_TOP_N_x_indicator_p(self, obj_cname, cname_x, period, top_n=5, percent=5):
        """For support line in the past"""

        def fast_find_last_higher_index(values, top_5, window):
            n = len(values)
            result = np.full(n, np.nan)

            for i in range(window - 1, n):
                window_data = values[i - window + 1:i + 1]
                top_value = top_5[i]

                idxs = np.where(window_data > top_value)[0]
                for id in idxs:
                    i_px = i - window + 1 + id
                    if (1 + percent / 100) * self.pdx.loc[i_px, obj_cname] < self.pdx.loc[i_px, "Close"] < \
                            self.pdx.loc[i_px, obj_cname]:
                        result[i] = True

            return result

        # init
        pdx = self.pdx.copy()

        pdx['rank'] = pdx[cname_x].rolling(window=period).agg(lambda x: np.sort(x)[-top_n])
        result = fast_find_last_higher_index(pdx[cname_x].values, pdx['rank'].values, period)
        # Score
        # Score
        true_count = np.sum(result == True) / len(result)
        if true_count > 0.8:
            return 1
        if true_count > 0.6:
            return 0.8
        if true_count > 0.4:
            return 0.6
        if true_count > 0.2:
            return 0.4
        else:
            return 0

    def rule_resistance_p(self, obj_cname, percentile=95, threshold=4, lookahead=3, period=240 * 2):
        """
        Tính đường kháng cự dựa trên yêu cầu:
        - Ngưỡng giá kháng cự được tính dựa trên điều kiện:
            + Giá vượt qua ngưỡng nhưng không tăng quá 4% trong 3 ngày sau.
            + Giá giảm xuống dưới ngưỡng trong vòng 3 ngày.
        """

        def resistance_validate(values, obj_price, window):
            n = len(values)
            result = np.full(n, np.nan)

            for i in range(window - 1, n):
                window_data = values[i - window + 1:i + 1]
                obj_value = obj_price[i]

                idxs = np.where(window_data > obj_value)[0]
                if len(idxs) == 0:
                    continue

                success_count = 0
                for id in idxs:
                    i_px = i - window + 1 + id

                    max_price_in_3_days = self.pdx.loc[i_px:i_px + lookahead + 1, "Close"][
                                          i_px:i_px + lookahead + 1].max()
                    min_price_in_3_days = self.pdx.loc[i_px:i_px + lookahead + 1, "Close"][
                                          i_px:i_px + lookahead + 1].min()

                    # Kiểm tra điều kiện:
                    # 1. Giá không vượt quá 4% so với ngưỡng
                    # 2. Giá giảm xuống dưới ngưỡng trong vòng 3 ngày
                    if (max_price_in_3_days <= obj_value * (1 + threshold / 100)) and (
                            min_price_in_3_days < obj_value):
                        success_count += 1

                success_ratio = (success_count / len(idxs)) * 100
                if success_ratio >= percentile:
                    result[i] = True

            return result

        pdx = self.pdx.copy()
        result = resistance_validate(pdx['Close'].values, pdx[obj_cname].values, period)

        # Score
        true_count = np.sum(result == True) / len(result)
        if true_count > 0.8:
            return 1
        if true_count > 0.6:
            return 0.8
        if true_count > 0.4:
            return 0.6
        if true_count > 0.2:
            return 0.4
        else:
            return 0

    def rule_support_p(self, obj_cname, percentile=95, threshold=4, lookahead=3, period=240 * 2):
        """
        Tính đường hỗ trợ dựa trên yêu cầu:
        - Ngưỡng giá hỗ trợ được tính dựa trên điều kiện:
            + Giá giảm qua ngưỡng nhưng không giảm quá 4% trong 3 ngày sau.
            + Giá quay lại ngưỡng trong vòng 3 ngày.
        """

        def support_validate(values, obj_price, window):
            n = len(values)
            result = np.full(n, np.nan)

            for i in range(window - 1, n):
                window_data = values[i - window + 1:i + 1]
                obj_value = obj_price[i]

                idxs = np.where(window_data < obj_value)[0]
                success_count = 0
                if len(idxs) == 0:
                    continue

                for id in idxs:
                    i_px = i - window + 1 + id

                    max_price_in_3_days = self.pdx.loc[i_px:i_px + lookahead + 1, "Close"][
                                          i_px:i_px + lookahead + 1].max()
                    min_price_in_3_days = self.pdx.loc[i_px:i_px + lookahead + 1, "Close"][
                                          i_px:i_px + lookahead + 1].min()

                    # Kiểm tra điều kiện:
                    if (min_price_in_3_days >= obj_value * (1 - threshold / 100)) and (
                            max_price_in_3_days > obj_value):
                        success_count += 1

                success_ratio = (success_count / len(idxs)) * 100
                if success_ratio >= percentile:
                    result[i] = True
            return result

        pdx = self.pdx.copy()
        result = support_validate(pdx['Close'].values, pdx[obj_cname].values, period)
        # Score
        true_count = np.sum(result == True) / len(result)
        if true_count > 0.8:
            return 1
        if true_count > 0.6:
            return 0.8
        if true_count > 0.4:
            return 0.6
        if true_count > 0.2:
            return 0.4
        else:
            return 0


# If support/resistance is pass all condition, then plot to check by hand

# Save support/resistance

def main():
    ticker_list = []


def calculate_resistance(df, percentile=95, threshold=4, lookahead=3):
    """
    Tính đường kháng cự dựa trên yêu cầu:
    - Dữ liệu nằm trong khoảng thời gian từ start_date đến end_date.
    - Ngưỡng giá kháng cự được tính dựa trên điều kiện:
        + Giá vượt qua ngưỡng nhưng không tăng quá 4% trong 3 ngày sau.
        + Giá giảm xuống dưới ngưỡng trong vòng 3 ngày.
    """
    # Lưu trữ các mức giá đóng cửa
    closing_prices = df['Close'].values

    # Danh sách các ngưỡng kháng cự thử nghiệm
    vap3m = df['VAP6M'].unique()

    best_resistance = None
    best_ratio = 0  # Lưu tỷ lệ tốt nhất đạt được

    # Duyệt qua từng ngưỡng kháng cự thử nghiệm
    for resistance in vap3m:
        success_count = 0
        total_count = 0

        # Duyệt qua từng ngày
        for i in range(len(closing_prices) - lookahead):
            current_price = closing_prices[i]

            # Điều kiện giá vượt ngưỡng kháng cự
            if current_price > resistance:
                total_count += 1
                max_price_in_3_days = closing_prices[i:i + lookahead + 1].max()
                min_price_in_3_days = closing_prices[i:i + lookahead + 1].min()

                # Kiểm tra điều kiện:
                # 1. Giá không vượt quá 4% so với ngưỡng
                # 2. Giá giảm xuống dưới ngưỡng trong vòng 3 ngày
                if (max_price_in_3_days <= resistance * (1 + threshold / 100)) and (min_price_in_3_days < resistance):
                    success_count += 1

        if total_count > 0:
            success_ratio = (success_count / total_count) * 100
            if success_ratio >= percentile and success_ratio > best_ratio:
                best_resistance = resistance
                best_ratio = success_ratio

    return best_resistance


def calculate_support(df, percentile=95, threshold=4, lookahead=3):
    """
    Tính đường kháng cự dựa trên yêu cầu:
    - Dữ liệu nằm trong khoảng thời gian từ start_date đến end_date.
    - Ngưỡng giá kháng cự được tính dựa trên điều kiện:
        + Giá vượt qua ngưỡng nhưng không tăng quá 4% trong 3 ngày sau.
        + Giá giảm xuống dưới ngưỡng trong vòng 3 ngày.
    """
    # Lưu trữ các mức giá đóng cửa
    closing_prices = df['Close'].values
    vap3m = df['VAP6M'].unique()

    best_resistance = None
    best_ratio = 0  # Lưu tỷ lệ tốt nhất đạt được

    # Duyệt qua từng ngưỡng kháng cự thử nghiệm
    for resistance in vap3m:
        success_count = 0
        total_count = 0

        # Duyệt qua từng ngày
        for i in range(len(closing_prices) - lookahead):
            current_price = closing_prices[i]

            # Điều kiện giá vượt ngưỡng kháng cự
            if current_price > resistance:
                total_count += 1
                max_price_in_3_days = closing_prices[i:i + lookahead + 1].max()
                min_price_in_3_days = closing_prices[i:i + lookahead + 1].min()

                # Kiểm tra điều kiện:
                # 1. Giá không vượt quá 4% so với ngưỡng
                # 2. Giá giảm xuống dưới ngưỡng trong vòng 3 ngày
                if (max_price_in_3_days >= resistance * (1 - threshold / 100)) and (min_price_in_3_days > resistance):
                    success_count += 1

        # Tính tỷ lệ thành công
        if total_count > 0:
            success_ratio = (success_count / total_count) * 100
            # Kiểm tra nếu tỷ lệ này đạt mức yêu cầu
            if success_ratio >= percentile and success_ratio > best_ratio:
                best_resistance = resistance
                best_ratio = success_ratio

    return best_resistance


if __name__ == '__main__':
    ticker = 'TTA'
    pdraw = pd.read_csv(f'ticker_v1a/{ticker}.csv')
    # pdraw = pdraw[:].reset_index(drop=True)
    # pdraw = pdraw.tail(450).reset_index(drop=True)
    now = time.time()
    # pdraw['support_resistance'] = calculate_support_resistance_v1(pdraw)
    # pdraw['support_resistance'] = calculate_support_resistance_v2(pdraw)
    # pdraw = pdraw.tail(300).reset_index(drop=True)

    pdraw = pdraw.tail(1000).reset_index(drop=True)
    calculate_support_resistance_v3(pdraw, ticker)
    print(time.time() - now)

    # for i in ['FPT', 'FOC', 'APH', 'ANV', 'BVB', 'ANT', 'DGC', 'DHG', 'DPM', 'CDC', 'ACB', 'BRC', 'ACV', 'FMC',
    #           'DPR']:
    #
    # # for i in ['DHM', 'FPT']:
    #     pd00 = pd.read_csv(f'ticker_v1a/{i}.csv')
    #     # pd00 = pd00.tail(240 * 2).reset_index(drop=True)
    #     print(i)
    #     if verbose:
    #         fig = go.Figure(data=[go.Scatter(x=pd00['time'], y=pd00['Close'], name='Close'),
    #                               go.Scatter(x=pd00['time'], y=pd00['Res_1Y'], name='res'),
    #                               go.Scatter(x=pd00['time'], y=pd00['Sup_1Y'], name='sup')])
    #
    #         fig.update_layout(title=f'Support/Resistance of {i}',
    #                           xaxis_title='Time',
    #                           yaxis_title='Price')
    #         fig.write_html(f'tuning/exp_indicator/html/{i}.html')

    # result = compute_data(ticker)
    # print(time.time() - now)

    # data_dict_support = {
    #     "DXS": ['2022-10-01', '2024-10-01', 4, 6],
    #     "DXP": ['2017-01-01', '2019-01-01', 8, 10],
    #     "DVP": ['2020-10-01', '2022-10-01', 30, 35],
    #     "CSC": ['2023-02-01', '2024-12-01', 21, 26],
    #     # "DTA": ['2022-05-01', ],
    #     # "DTA_1": ['2021-01-01'],
    #     "CMX": ['2023-01-01', '2025-01-01', 7.5, 9.5],
    #     "API": ['2023-04-01', '2025-04-01', 5, 6],
    #     "APH": ['2022-10-01', '2024-10-01', 7, 8],
    #     "NRC": ['2022-10-01', '2024-10-01', 4, 5],
    #
    # }
    # data_dict_resistance = {
    #     "CTF": ['2022-10-01', '2024-10-01', 27, 29],
    #     # "DTA": ["2022-04-01"],
    #     "PSH": ['2023-11-01', '2025-11-01', 7, 9],
    #     "VIC": ['2023-01-01', '2025-01-01', 50, 55],
    #
    # }
    # result_sp = {}
    # result_res = {}
    #
    # for ticker, value in data_dict_support.items():
    #     start = value[0]
    #     end = value[1]
    #     df = pd.read_csv(f'ticker_v1a/{ticker}.csv', dtype={'time': str, 'ticker': str}).query(
    #         f'time>="{start}" and time<="{end}"').reset_index(drop=True)
    #
    #     result_sp[ticker] = (calculate_support(df))
    #
    # for ticker, value in data_dict_resistance.items():
    #     start = value[0]
    #     end = value[1]
    #     df = pd.read_csv(f'ticker_v1a/{ticker}.csv', dtype={'time': str, 'ticker': str}).query(
    #         f'time>="{start}" and time<="{end}"').reset_index(drop=True)
    #
    #     result_res[ticker] = (calculate_resistance(df))
    # pass
