import time
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import trendln
from IPython.core.display_functions import display


def find_longest_support(pd00, cname_key, MIN_LENGTH=0, MAX_LENGTH=240, TOLERANCE=0.01,
                         ratio_threshold=(0.955, 0.1, 0.08)):
    """
    Find the longest support line of array data at each local minimum.
    Use <PERSON><PERSON>'s theorem to determine that all data in A[i:j] is smaller than the straight line connecting 2 points
    if a line is drawn parallel to one side of a triangle intersecting the other two sides in distinct points, then
    the other two sides are divided in the same ratio
    #
    ratio_threshold=(0.955, 0.1, 0.08), MIN_LENGTH=20, MAX_LENGTH=240,TOLERANCE=0.01 -> long term
    ratio_threshold=(0.97, 0.1, 0.03), MIN_LENGTH=5, MAX_LENGTH=120,TOLERANCE=1e-4 -> short term
    """
    # 1. Find all local minima from 1..T-2.
    T = pd00.shape[0]
    minima_idxs = trendln.get_extrema((pd00[cname_key], None), extmethod=trendln.METHOD_NUMDIFF, accuracy=2)

    for i in range(len(minima_idxs)):
        if minima_idxs[i] < T - 1:
            if pd00.iloc[minima_idxs[i] + 1][cname_key] < pd00.iloc[minima_idxs[i]][cname_key] * (1 + TOLERANCE):
                minima_idxs[i] = minima_idxs[i] + 1

    pd00['C_H1M'] = pd00['Close'] / (pd00['High'].rolling(20).max())
    pd00['C_H1W'] = pd00['Close'] / (pd00['High'].rolling(5).max())

    # Condition 1: Decreasing price x.y%
    minima_idxs = [idx for idx in minima_idxs if
                   (pd00.iloc[idx]['C_H1W'] < ratio_threshold[0])]

    plt.plot(minima_idxs, pd00.iloc[minima_idxs]['Low'], marker='o', color='black', linestyle='None',
             label='Minima original')
    # darkseagreen
    #  Condition 2: Distance from 2 bottom is more than 1 month
    for i in range(1, 3):
        i = 1
        minima = minima_idxs.copy()
        for idx in range(len(minima_idxs)):
            # Up hill
            if (0 < minima_idxs[idx] - minima_idxs[idx - i] < MIN_LENGTH):
                ratio = pd00.iloc[minima_idxs[idx]][cname_key] / pd00.iloc[minima_idxs[idx - i]][cname_key]
                try:
                    # if ratio < 1 and abs(ratio - 1) < ratio_threshold[1]:
                    #     minima.remove(minima_idxs[idx - i]) if minima_idxs[idx - i] in minima else None
                    if ratio > 1 and abs(ratio - 1) < ratio_threshold[2]:
                        minima.remove(minima_idxs[idx]) if minima_idxs[idx] in minima else None
                except:
                    continue

            # Down hill
            if (0 < minima_idxs[idx] - minima_idxs[idx - i] < 2 * MIN_LENGTH):
                try:
                    ratio = pd00.iloc[minima_idxs[idx]][cname_key] / pd00.iloc[minima_idxs[idx - i]][cname_key]
                    if ratio < 1 and abs(ratio - 1) < ratio_threshold[1]:
                        minima.remove(minima_idxs[idx - i]) if minima_idxs[idx - i] in minima else None
                    # elif ratio > 1 and abs(ratio - 1) < ratio_threshold[2]:
                    #     minima.remove(minima_idxs[idx]) if minima_idxs[idx] in minima else None
                except:
                    continue

        minima_idxs = minima.copy()

    if len(minima_idxs) < 2:
        return [0] * pd00.shape[0], []

    plt.plot(minima_idxs, pd00.iloc[minima_idxs]['Low'], marker='.', color='red', linestyle='None',
             label='Minima filtered')

    minima_values = list(pd00.iloc[minima_idxs][cname_key].values)

    # Find support line
    all_support_lines = []
    min_minima_idx = np.argmin(minima_values)
    min_minima_loc = minima_idxs[min_minima_idx]

    # Concatenate local minimum indexes and value for loop
    minima = list(zip(minima_idxs, minima_values))
    for i, (cur_loc, cur_value) in enumerate(reversed(minima)):
        sl_temp = []
        minima_idx = len(minima) - i - 1

        if cur_loc < min_minima_loc:
            min_minima_idx = np.argmin(minima_values[:minima_idx + 1])
            min_minima_loc = minima_idxs[min_minima_idx]

        # Heuristics: Use the smallest value of the local minimum set as a starting point
        for check_minima_idx in range(min_minima_idx, minima_idx):
            check_loc = minima_idxs[check_minima_idx]
            check_value = minima_values[check_minima_idx]
            if (check_value >= cur_value) or (cur_loc - check_loc) < 0 or (cur_loc - check_loc) > MAX_LENGTH:
                continue

            # Use transform and (check_date, cur_value) point as the length coordinate reference
            loc_distance = cur_loc - check_loc
            value_distance = cur_value - check_value

            mid_locs = np.arange(check_loc, cur_loc)
            mid_values = pd00.iloc[check_loc:cur_loc][cname_key].values

            mask = mid_values < cur_value
            calib_mid_locs = abs((mid_locs[mask] - check_loc) - loc_distance)
            calib_mid_values = abs(mid_values[mask] - cur_value) * (1 - TOLERANCE)

            # Build ratios based on Thales's theorem
            x_ratio = calib_mid_locs / loc_distance
            y_ratio = calib_mid_values / value_distance

            ratio = y_ratio > x_ratio
            count = np.count_nonzero(ratio)

            if (count / loc_distance) < TOLERANCE * 2:
                sl_temp.append([check_loc, cur_loc])

        # Find the support line with the largest slope
        max_slope = 0
        for loc in sl_temp:
            x1, y1 = loc[0], pd00.iloc[loc[0]][cname_key]
            x2, y2 = loc[1], pd00.iloc[loc[1]][cname_key]
            slope = (y2 - y1) / (x2 - x1)
            if slope < 0:
                continue
            if slope > max_slope:
                max_slope = slope
                sp_loc = loc

        if max_slope != 0:
            intercept = pd00.iloc[sp_loc[0]][cname_key] - max_slope * sp_loc[0]
            all_support_lines.append((max_slope, intercept, sp_loc[0], sp_loc[1]))

    # Loop find support line
    all_support_lines.sort(key=lambda x: x[3], reverse=False)

    S = []
    sp_lines = []
    id_sp = 0

    for t in range(pd00.shape[0]):
        if len(sp_lines) == 0:
            S.append(0)
        else:
            # S.append(np.percentile([sp_lines[i][0] * t + sp_lines[i][1] for i in range(len(sp_lines))], 90, method="nearest"))
            S.append(max([sp_lines[i][0] * t + sp_lines[i][1] for i in range(len(sp_lines))]))

        if id_sp < len(all_support_lines) and (t - 2) >= all_support_lines[id_sp][3]:
            sp_lines.append(all_support_lines[id_sp])
            id_sp += 1
        sp_lines = [s for s in sp_lines if pd00[cname_key][t] >= (t * s[0] + s[1]) * (1.0 - TOLERANCE * 2)]
    # print(len(all_support_lines))
    return S, all_support_lines


from pathos.multiprocessing import ProcessingPool as Pool


def multi_process(func, list_input, num_proc=7):
    with Pool(num_proc) as p:
        output = p.map(func, list_input)
    return output


import os


def prepare_data(tickers):
    for ticker in tickers:
        file_ = f'../data/daily_latest/{ticker}.csv'
        pdx = pd.read_csv(file_)
        pdx = pd.read_csv(file_).rename(columns={'high': 'High',
                                                 'low': 'Low', 'open': 'Open', 'close': 'Close', 'volume': 'Volume'})
        pdx['C_L1M'] = pdx['Close'] / pdx['Low'].rolling(20).min()
        pdx['C_L3M'] = pdx['Close'] / pdx['Low'].rolling(60).min()
        pdx['C_L6M'] = pdx['Close'] / pdx['Low'].rolling(120).min()
        pdx['C_L1Y'] = pdx['Close'] / pdx['Low'].rolling(240).min()
        pdx['Open_A1'] = pdx['Open'].shift(-1)
        pdx['Open_A10'] = pdx['Open'].shift(-10)
        pdx['Open_A100'] = pdx['Open'].shift(-100)
        pdx['Open_A200'] = pdx['Open'].shift(-200)
        pdx['Open_A250'] = pdx['Open'].shift(-250)
        pdx['ratio'] = pdx['Open_A10'] / pdx['Open_A1']
        pdx['ratio_T100'] = pdx['Open_A100'] / pdx['Open_A1']
        pdx['ratio_T200'] = pdx['Open_A200'] / pdx['Open_A1']
        pdx['ratio_T250'] = pdx['Open_A250'] / pdx['Open_A1']
        buyPrice = pdx['Open'].shift(-1)
        dictP = {'1W': 5,
                 '1M': 20,
                 '3M': 60,
                 '1Y': 240,
                 '2Y': 480
                 }
        # L1W H1W C1W ...
        for k in dictP.keys():
            n = dictP[k]
            pdx[f'L{k}'] = pdx['Low'].rolling(n).min().shift(-n - 1) / buyPrice
            pdx[f'H{k}'] = pdx['High'].rolling(n).max().shift(-n - 1) / buyPrice
            pdx[f'C{k}'] = pdx['Close'].shift(-n - 1) / buyPrice

        pdx.to_csv(f'../data/data_test/{ticker}.csv')


def eval_all(tickers):
    for ticker in tickers:

        file_ = f'../data/data_test/{ticker}.csv'
        pdx = pd.read_csv(file_)
        # pdx = pdx.iloc[-480:].reset_index(drop=True)

        # plt.figure(figsize=(30, 10))
        fig, ax = plt.subplots(figsize=(30, 10))
        plt.gca().set_facecolor('whitesmoke')

        id = 1

        # arg2 = [3, 180, 0.02336, 0.935, 0.175, 0.085]
        # arg3 = [3, 180, 0.00546, 0.965, 0.225, 0.084]
        # arg4 = [0, 140, 0.0336, 0.95, 0.175, 0.065]
        # arg5 = [4, 160, 0.06952, 0.93, 0.135, 0.084]
        # arg6 = [1, 200, 0.04833, 0.95, 0.16, 0.018]
        # arg7 = [3, 160, 0.06952, 0.965, 0.22, 0.029]

        S, support_lines = find_longest_support(pdx, 'Low', MIN_LENGTH=arg[0], MAX_LENGTH=arg[1], TOLERANCE=arg[2],
                                                ratio_threshold=(arg[3], arg[4], arg[5]))

        for line in support_lines:
            x_values = np.arange(line[2], line[3])
            y_values = line[0] * x_values + line[1]
            plt.plot(x_values, y_values, color='black')

        plt.plot(pdx['time'], pdx['Low'], label=ticker)

        indices_to_display = range(0, len(pdx['time']), 60)
        ax.set_xticks(indices_to_display)

        ax.set_xticklabels([pdx['time'].iloc[i] for i in indices_to_display])

        plt.plot(S, color="lawngreen")
        plt.tick_params(axis="x", rotation=90)

        plt.legend()
        # plt.savefig(f'draw/{id}_{ticker}.png')
        plt.show()
        # plt.close()


def eval_by_mr_tri(tickers):
    # arg1 = [4, 180, 0.01129, 0.95, 0.115, 0.069]
    # arg2 = [3, 180, 0.02336, 0.935, 0.175, 0.085]
    # arg3 = [3, 180, 0.00546, 0.965, 0.225, 0.084]
    # arg4 = [0, 140, 0.0336, 0.95, 0.175, 0.065]
    # arg5 = [4, 160, 0.06952, 0.93, 0.135, 0.084]
    # arg6 = [1, 200, 0.04833, 0.95, 0.16, 0.018]
    # arg7 = [3, 160, 0.06952, 0.965, 0.22, 0.029]
    # args = [arg1, arg2, arg3, arg4, arg5, arg6, arg7]

    for i in range(100):
        np.random.seed((os.getpid() * int(time.time())) % 123456789)

        min_length = np.random.randint(0, 30)
        max_length = np.random.choice(list(range(120, 241, 20)))
        tolerance = round(np.random.choice(list(np.logspace(-4, -1, num=20))), 5)

        weight_1 = round(np.random.choice(list(np.arange(0.92, 0.98, .005))), 4)
        weight_2 = round(np.random.choice(list(np.arange(0.1, 0.3, .005))), 4)
        weight_3 = round(np.random.choice(list(np.arange(0.01, 0.2, .001))), 4)

        arg = [min_length, max_length, tolerance, weight_1, weight_2, weight_3]
        print(f"{i} - {arg} -------------------------------------------------------------------------")

        data = []

        for ticker in tickers:
            data.append([ticker, arg])

        result = multi_process(eval_1, data, num_proc=8)
        pd_ratio = []
        count = 0
        pdXY = []
        for j in result:
            pd_ratio.append(j[0])
            count += j[1]
            pdXY.append(j[2])

        # pd_ratio = pd.concat(pd_ratio)
        average = count / len(tickers)
        pdXY = pd.concat(pdXY)

        pd_report = []
        for threshold in np.arange(0.9, 1.02, 0.02):
            pdXY['hit'] = (pdXY['Close'] < (pdXY['Strend']) * threshold)
            pdXY['drop15%'] = pdXY['L1M'] < 0.85
            pdXY['drop20%'] = pdXY['L1M'] < 0.8
            print(f"support violation threshold {(1 - threshold) * 100:.0f}%")
            display(pdXY.groupby('hit', as_index=False).agg(
                {'time': 'count', 'C1M': 'mean', 'drop20%': 'mean', 'drop15%': 'mean'}))
            pd_report.append(
                pdXY.groupby('hit', as_index=False).agg(
                    {'time': 'count', 'C1M': 'mean', 'drop20%': 'mean', 'drop15%': 'mean'}))

        pd_report = pd.concat(pd_report)

        df_data = [min_length, max_length, tolerance, weight_1, weight_2, weight_3, average,
                   pd_report.iloc[0]['drop20%']
            , pd_report.iloc[1]['time'], pd_report.iloc[1]['drop15%'], pd_report.iloc[1]['drop20%']
            , pd_report.iloc[3]['time'], pd_report.iloc[3]['drop15%'], pd_report.iloc[3]['drop20%']
            , pd_report.iloc[5]['time'], pd_report.iloc[5]['drop15%'], pd_report.iloc[5]['drop20%']
            , pd_report.iloc[7]['time'], pd_report.iloc[7]['drop15%'], pd_report.iloc[7]['drop20%']
            , pd_report.iloc[9]['time'], pd_report.iloc[9]['drop15%'], pd_report.iloc[9]['drop20%']
            , pd_report.iloc[11]['time'], pd_report.iloc[11]['drop15%'], pd_report.iloc[11]['drop20%']

                   ]
        pd_report = pd.DataFrame(df_data).T

        pd_report.columns = ['min_length', 'max_length', 'tolerance', 'weight_1', 'weight_2', 'weight_3',
                             'sl_avarage', "drop20%_not",
                             'hit_10', 'drop15%_10', 'drop20%_10',
                             'hit_8', 'drop15%_8', 'drop20%_8',
                             'hit_6', 'drop15%_6', 'drop20%_6',
                             'hit_4', 'drop15%_4', 'drop20%_4',
                             'hit_2', 'drop15%_2', 'drop20%_2',
                             'hit_0', 'drop15%_0', 'drop20%_0',
                             ]

        output_path = f'temp/trend_line_report.csv'
        pd_report.to_csv(output_path, mode='a', header=not os.path.exists(output_path))


def draw_image(pd_ratio):
    percentile = np.percentile(pd_ratio['ratio'].dropna().values, [30, 50, 80])
    percentile_str = ""

    for i in range(len(percentile)):
        percentile_str += f"{round(percentile[i], 3)}, "
    fig, ax = plt.subplots()
    pd_ratio['ratio'].hist(bins=100)

    plt.text(x=0.7, y=.11,
             s=f"1: {round((pd_ratio[pd_ratio['ratio'] > 1].shape[0]) / pd_ratio.shape[0] * 100, 3)}%",
             fontsize=10, transform=ax.transAxes)
    plt.text(x=0.7, y=.22,
             s=f"1.05: {round((pd_ratio[pd_ratio['ratio'] > 1.05].shape[0]) / pd_ratio.shape[0] * 100, 3)}%",
             fontsize=10, transform=ax.transAxes)
    plt.text(x=0.7, y=.33,
             s=f"1.1: {round((pd_ratio[pd_ratio['ratio'] > 1.1].shape[0]) / pd_ratio.shape[0] * 100, 3)}%",
             fontsize=10, transform=ax.transAxes)
    plt.text(x=0.7, y=.44,
             s=f"1.15: {round((pd_ratio[pd_ratio['ratio'] > 1.15].shape[0]) / pd_ratio.shape[0] * 100, 3)}%",
             fontsize=10, transform=ax.transAxes)

    plt.text(.01, .99, f"average strendline: {round(average, 3)}", ha='left', va='top', transform=ax.transAxes)
    plt.text(.01, .88, f"percentile: {percentile_str}", ha='left', va='top', transform=ax.transAxes)
    plt.text(.01, .77, f"mean: {pd_ratio['ratio'].mean()}", ha='left', va='top', transform=ax.transAxes)
    plt.text(.01, .66, f"total hit: {pd_ratio.shape[0]}", ha='left', va='top', transform=ax.transAxes)
    plt.text(.01, .55, f"weight: {arg}", ha='left', va='top', transform=ax.transAxes)
    plt.xlabel('Ratio')
    plt.ylabel('Frequency')
    plt.title('Distribution of Open_T+10 / Open_T+1')
    ymd = datetime.today().strftime('%Y-%m-%d')
    plt.savefig(f'eval/{ymd}_{min_length}-{max_length}-{tolerance}-{weight_1}-{weight_2}-{weight_3}.png')
    # plt.show()
    plt.close()


def eval(data):
    ticker = data[0]
    arg = data[1]
    file_ = f'../data/data_test/{ticker}.csv'
    pdx = pd.read_csv(file_)
    try:
        S, support_lines = find_longest_support(pdx, 'Low', MIN_LENGTH=0, MAX_LENGTH=240, TOLERANCE=1e-2,
                                                ratio_threshold=(arg[0], arg[1], arg[2]))

        # S, support_lines = find_longest_support(pdx, 'Low', MIN_LENGTH=5, MAX_LENGTH=120, TOLERANCE=1e-4,
        #                                            ratio_threshold=(0.97, 0.1, 0.03))
    except Exception as e:
        print(f'Error: {ticker}: {e}')
        return pd.DataFrame(), 0

    pdx['Strend'] = S
    query = pdx.query("(Volume*Price>1e+9) & Close<Strend & C_L6M>1.15")

    return query, len(support_lines)


def eval_1(data):
    ticker = data[0]
    arg = data[1]
    file_ = f'../data/data_test/{ticker}.csv'
    pdx = pd.read_csv(file_)
    try:

        S, support_lines = find_longest_support(pdx, 'Low', MIN_LENGTH=arg[0], MAX_LENGTH=arg[1], TOLERANCE=arg[2],
                                                ratio_threshold=(arg[3], arg[4], arg[5]))
        # S, support_lines = find_longest_support(pdx, 'Low', MIN_LENGTH=5, MAX_LENGTH=120, TOLERANCE=1e-4,
        #                                            ratio_threshold=(0.97, 0.1, 0.03))
    except Exception as e:
        print(f'Error: {ticker}: {e}')
        return pd.DataFrame(), 0, pd.DataFrame()

    pdx['Strend'] = S
    query = pdx.query("(Volume*Price>1e+9) & Close<Strend & C_L3M>1.15")

    return query, len(support_lines), pdx


if __name__ == "__main__":

    RUN = 4
    tickers = [f.replace('.csv', '') for f in os.listdir('../data/data_test/') if
               f.endswith('.csv')]

    if RUN == 1:
        tickers = tickers[-750:-748]

        eval_all(["HPG"])

    if RUN == 2:
        # tickers = tickers[-750:-650]
        # tickers = tickers[-650:-450]

        data = []

        for i in range(100):
            np.random.seed((os.getpid() * int(time.time())) % 123456789)

            min_length = np.random.randint(0, 30)
            max_length = np.random.choice(list(range(120, 241, 20)))
            tolerance = round(np.random.choice(list(np.logspace(-4, -1, num=20))), 5)

            weight_1 = round(np.random.choice(list(np.arange(0.93, 0.98, .005))), 4)
            weight_2 = round(np.random.choice(list(np.arange(0.1, 0.23, .005))), 4)
            weight_3 = round(np.random.choice(list(np.arange(0.01, 0.1, .001))), 4)

            arg = [min_length, max_length, tolerance, weight_1, weight_2, weight_3]
            print(f"{i} - {arg}")
            data = []

            for ticker in tickers:
                data.append([ticker, arg])

            result = multi_process(eval_1, data, num_proc=8)
            pd_ratio = []
            pdXY = []
            count = 0

            for j in result:
                pd_ratio.append(j[0])
                count += j[1]
                # pdXY.append(j[2])

            pd_ratio = pd.concat(pd_ratio)
            average = count / len(tickers)
            # pdXY = pd.concat(pdXY)

            draw_image(pd_ratio)

            percentile = np.percentile(pd_ratio['ratio'].dropna().values, [30, 50, 80])
            ratio_T10 = [percentile[0], percentile[1], percentile[2], pd_ratio['ratio'].mean(),
                         round((pd_ratio[pd_ratio['ratio'] > 1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                         round((pd_ratio[pd_ratio['ratio'] > 1.05].shape[0]) / pd_ratio.shape[0] * 100, 3),
                         round((pd_ratio[pd_ratio['ratio'] > 1.1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                         round((pd_ratio[pd_ratio['ratio'] > 1.15].shape[0]) / pd_ratio.shape[0] * 100,
                               3)]

            percentile = np.percentile(pd_ratio['ratio_T100'].dropna().values, [30, 50, 80])
            ratio_T100 = [percentile[0], percentile[1], percentile[2], pd_ratio['ratio_T100'].mean(),
                          round((pd_ratio[pd_ratio['ratio_T100'] > 1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T100'] > 1.05].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T100'] > 1.1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T100'] > 1.15].shape[0]) / pd_ratio.shape[0] * 100,
                                3)]
            percentile = np.percentile(pd_ratio['ratio_T200'].dropna().values, [30, 50, 80])
            ratio_T200 = [percentile[0], percentile[1], percentile[2], pd_ratio['ratio_T200'].mean(),
                          round((pd_ratio[pd_ratio['ratio_T200'] > 1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T200'] > 1.05].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T200'] > 1.1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T200'] > 1.15].shape[0]) / pd_ratio.shape[0] * 100,
                                3)]

            percentile = np.percentile(pd_ratio['ratio_T250'].dropna().values, [30, 50, 80])
            ratio_T250 = [percentile[0], percentile[1], percentile[2], pd_ratio['ratio_T250'].mean(),
                          round((pd_ratio[pd_ratio['ratio_T250'] > 1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T250'] > 1.05].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T250'] > 1.1].shape[0]) / pd_ratio.shape[0] * 100, 3),
                          round((pd_ratio[pd_ratio['ratio_T250'] > 1.15].shape[0]) / pd_ratio.shape[0] * 100,
                                3)]

            df_data = [min_length, max_length, tolerance, weight_1, weight_2, weight_3, average, pd_ratio.shape[0]]
            df_data.extend(ratio_T10)
            df_data.extend(ratio_T100)
            df_data.extend(ratio_T200)
            df_data.extend(ratio_T250)

            # pdXY['hit'] = 0
            # pdXY['drop20%'] = 0
            # for threshold in np.arange(0.9, 1, 0.02):
            #     pdXY['hit'] = (pdXY['Close'] < (pdXY['support']) * threshold)
            #     pdXY['drop20%'] = pdXY['L1M'] < 0.8
            #     print(f"support violation threshold {(1 - threshold) * 100:.0f}%")
            #     display(pdXY.groupby('hit', as_index=False).agg({'time': 'count', 'C1M': 'mean', 'drop20%': 'mean'}))
            #     []

            pd_report = pd.DataFrame(df_data).T

            pd_report.columns = ['min_length', 'max_length', 'tolerance', 'weight_1', 'weight_2', 'weight_3',
                                 'sl_avarage', 'total_hit',
                                 'T10_p_30', 'T10_p_50', 'T10_p_80', 'T10_mean', 'T10_ratio_1', 'T10_ratio_105',
                                 'T10_ratio_11', 'T10_ratio_115',
                                 'T100_p_30', 'T100_p_50', 'T100_p_80', 'T100_mean', 'T100_ratio_1', 'T100_ratio_105',
                                 'T100_ratio_11', 'T100_ratio_115',
                                 'T200_p_30', 'T200_p_50', 'T200_p_80', 'T200_mean', 'T200_ratio_1', 'T200_ratio_105',
                                 'T200_ratio_11', 'T200_ratio_115',
                                 'T250_p_30', 'T250_p_50', 'T250_p_80', 'T250_mean', 'T250_ratio_1', 'T250_ratio_105',
                                 'T250_ratio_11', 'T250_ratio_115']

            output_path = f'temp/trend_line_report.csv'
            pd_report.to_csv(output_path, mode='a', header=not os.path.exists(output_path))

    if RUN == 3:
        tickers = [f.replace('.csv', '') for f in os.listdir('../data/daily_latest/') if
                   f.endswith('.csv')]
        prepare_data(tickers)

    if RUN == 4:
        # tickers = tickers[-750:-748]
        eval_by_mr_tri(tickers)
