"""
explore.py

This module provides functions for evaluating and filtering stock tickers based on various financial and technical criteria. It leverages pandas for data manipulation, multiprocessing for parallel evaluation, and custom evaluation logic from core_utils. The main functionalities include:

- Parsing date filters from filter dictionaries.
- Evaluating tickers for profit and buy signals using custom filter logic and cut-loss thresholds.
- Grouping and analyzing periods of significant profit, and identifying periods without buy signals.
- Saving results to CSV for further analysis.

Key components:
- Uses joblib for caching and pathos for parallel processing.
- Integrates with a Redis cache for evaluation results.
- Designed to be run as a script for batch processing of multiple tickers.

Typical usage involves setting up filter conditions and running the script to process all available ticker data files in the specified directory.
"""

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
import logging
import os
import re
import sys
import warnings
from datetime import datetime
from datetime import timedelta

import pandas as pd
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.base_eval import AllEval
from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host="localhost")
RANKING = 'ranking_point'


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str

def eval(filter):
    ticker = 'VNINDEX'
    try:
        pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
        eval_ticker = AllEval(ticker, pdxx, filter, cutloss=0.15, cache_service=redis_cache)
        period = '3M'
        pdxx[f"P{period}"] = 100. * (pdxx[f"O{period}"] - 1)
        pd_all = pdxx.query("P3M <= -20")
        df_sell = eval_ticker.get_sell_signals()

        pd_all['s_time'] = pd.to_datetime(pd_all['time'])
        pd_all['week'] = pd_all['s_time'].dt.strftime('%Y-%W')
        pd_all['biweek'] = pd_all['s_time'].dt.to_period('2W').astype(str)
        pd_all['month'] = pd_all['s_time'].dt.strftime('%Y-%m')

        df_sell['s_time'] = pd.to_datetime(df_sell['time'])

        """
        A: data frame have P6M <= -20
        A deduplicate
        group all hit in same period find group_start_time and group_end_time

        Result:
        If there is no buy hit in same period so that need to consider

        """

        # pd_all = pd_all.drop_duplicates(subset=['month'], keep='first')

        pd_all['month'] = pd.to_datetime(pd_all['month'])
        pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)

        # Group consecutive months (month_diff <= 1)
        pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('first') - pd.Timedelta(days=30)
        pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('last') + pd.Timedelta(days=30)
        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

        res_month = []

        for i in range(pd_all.shape[0]):
            row_a = pd_all.iloc[i]
            start_group = row_a['start_group']
            end_group = row_a['end_group']
            if not df_sell['s_time'].between(start_group,
                                            end_group).any():
                res_month.append(row_a)

        path = 'tuning/results/'
        res_month = pd.DataFrame(res_month)
        res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)

        return res_month
    except Exception as error:
        print(f"Error: {ticker}: {error}")
        pass





if __name__ == "__main__":
    import random

    random.seed(0)
    Init = "(time>='2000-01-01') & (time<='2026-01-01') "
    filter = {
        "~Diverge2_hyo_2": f" {Init} & (D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
        "~Diverge2_hyo_3": f"{Init} & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)",
        "_BKMA200": f"{Init} &(ID_LO_3Y-ID_HI_3Y)>250.0 & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (HI_3M_T1/LO_3M_T1<2.1)",

    }

    eval(filter)
