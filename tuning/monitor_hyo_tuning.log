2025-04-28 19:16:04,068 <core_utils.redis_cache.EvalRedis object at 0x70dab6f93460>
2025-04-28 19:16:04,115 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-28 19:16:11,855 <core_utils.redis_cache.EvalRedis object at 0x7f3ee0373430>
2025-04-28 19:16:11,869 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:23:51,519 <core_utils.redis_cache.EvalRedis object at 0x770d40d81090>
2025-04-29 17:23:51,558 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:41:13,669 <core_utils.redis_cache.EvalRedis object at 0x71dd82b87400>
2025-04-29 17:41:13,677 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:42:26,836 <core_utils.redis_cache.EvalRedis object at 0x7232a6b7b400>
2025-04-29 17:42:26,841 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:42:32,537 build_posterior_wrapper took 0.007840 seconds
2025-04-29 17:42:32,538 TPE using 1/1 trials with best loss inf
2025-04-29 17:42:32,594 build_posterior_wrapper took 0.008134 seconds
2025-04-29 17:42:32,595 TPE using 2/2 trials with best loss inf
2025-04-29 17:42:32,651 build_posterior_wrapper took 0.009568 seconds
2025-04-29 17:42:32,652 TPE using 3/3 trials with best loss inf
2025-04-29 17:42:32,707 build_posterior_wrapper took 0.007361 seconds
2025-04-29 17:42:32,708 TPE using 4/4 trials with best loss inf
2025-04-29 17:42:32,776 build_posterior_wrapper took 0.007313 seconds
2025-04-29 17:42:32,776 TPE using 5/5 trials with best loss inf
2025-04-29 17:42:32,836 build_posterior_wrapper took 0.008609 seconds
2025-04-29 17:42:32,836 TPE using 6/6 trials with best loss inf
2025-04-29 17:42:32,891 build_posterior_wrapper took 0.007401 seconds
2025-04-29 17:42:32,891 TPE using 7/7 trials with best loss inf
2025-04-29 17:42:32,951 build_posterior_wrapper took 0.010227 seconds
2025-04-29 17:42:32,952 TPE using 8/8 trials with best loss inf
2025-04-29 17:42:33,010 build_posterior_wrapper took 0.007871 seconds
2025-04-29 17:42:33,010 TPE using 9/9 trials with best loss inf
2025-04-29 17:42:33,069 build_posterior_wrapper took 0.009664 seconds
2025-04-29 17:42:33,070 TPE using 10/10 trials with best loss inf
2025-04-29 17:42:33,126 build_posterior_wrapper took 0.007070 seconds
2025-04-29 17:42:33,126 TPE using 11/11 trials with best loss inf
2025-04-29 17:42:33,183 build_posterior_wrapper took 0.009138 seconds
2025-04-29 17:42:33,183 TPE using 12/12 trials with best loss inf
2025-04-29 17:42:33,235 build_posterior_wrapper took 0.007617 seconds
2025-04-29 17:42:33,236 TPE using 13/13 trials with best loss inf
2025-04-29 17:42:33,291 build_posterior_wrapper took 0.007134 seconds
2025-04-29 17:42:33,291 TPE using 14/14 trials with best loss inf
2025-04-29 17:42:33,352 build_posterior_wrapper took 0.011138 seconds
2025-04-29 17:42:33,353 TPE using 15/15 trials with best loss inf
2025-04-29 17:42:33,413 build_posterior_wrapper took 0.011494 seconds
2025-04-29 17:42:33,413 TPE using 16/16 trials with best loss inf
2025-04-29 17:42:33,472 build_posterior_wrapper took 0.008032 seconds
2025-04-29 17:42:33,473 TPE using 17/17 trials with best loss inf
2025-04-29 17:42:33,534 build_posterior_wrapper took 0.008248 seconds
2025-04-29 17:42:33,534 TPE using 18/18 trials with best loss inf
2025-04-29 17:42:33,589 build_posterior_wrapper took 0.007612 seconds
2025-04-29 17:42:33,589 TPE using 19/19 trials with best loss inf
2025-04-29 17:42:33,643 build_posterior_wrapper took 0.006705 seconds
2025-04-29 17:42:33,643 TPE using 20/20 trials with best loss inf
2025-04-29 17:42:33,723 build_posterior_wrapper took 0.007315 seconds
2025-04-29 17:42:33,723 TPE using 21/21 trials with best loss inf
2025-04-29 17:42:33,809 build_posterior_wrapper took 0.007180 seconds
2025-04-29 17:42:33,809 TPE using 22/22 trials with best loss inf
2025-04-29 17:42:33,885 build_posterior_wrapper took 0.007180 seconds
2025-04-29 17:42:33,885 TPE using 23/23 trials with best loss inf
2025-04-29 17:42:33,967 build_posterior_wrapper took 0.008007 seconds
2025-04-29 17:42:33,967 TPE using 24/24 trials with best loss inf
2025-04-29 17:42:34,047 build_posterior_wrapper took 0.008491 seconds
2025-04-29 17:42:34,048 TPE using 25/25 trials with best loss inf
2025-04-29 17:42:34,127 build_posterior_wrapper took 0.007217 seconds
2025-04-29 17:42:34,127 TPE using 26/26 trials with best loss inf
2025-04-29 17:42:34,206 build_posterior_wrapper took 0.006805 seconds
2025-04-29 17:42:34,207 TPE using 27/27 trials with best loss inf
2025-04-29 17:42:34,283 build_posterior_wrapper took 0.009210 seconds
2025-04-29 17:42:34,284 TPE using 28/28 trials with best loss inf
2025-04-29 17:42:34,369 build_posterior_wrapper took 0.007960 seconds
2025-04-29 17:42:34,369 TPE using 29/29 trials with best loss inf
2025-04-29 17:42:34,454 build_posterior_wrapper took 0.012545 seconds
2025-04-29 17:42:34,454 TPE using 30/30 trials with best loss inf
2025-04-29 17:42:34,540 build_posterior_wrapper took 0.007081 seconds
2025-04-29 17:42:34,540 TPE using 31/31 trials with best loss inf
2025-04-29 17:42:34,632 build_posterior_wrapper took 0.008031 seconds
2025-04-29 17:42:34,632 TPE using 32/32 trials with best loss inf
2025-04-29 17:42:34,724 build_posterior_wrapper took 0.007714 seconds
2025-04-29 17:42:34,725 TPE using 33/33 trials with best loss inf
2025-04-29 17:42:34,815 build_posterior_wrapper took 0.009297 seconds
2025-04-29 17:42:34,815 TPE using 34/34 trials with best loss inf
2025-04-29 17:42:34,904 build_posterior_wrapper took 0.011125 seconds
2025-04-29 17:42:34,904 TPE using 35/35 trials with best loss inf
2025-04-29 17:42:34,993 build_posterior_wrapper took 0.006915 seconds
2025-04-29 17:42:34,993 TPE using 36/36 trials with best loss inf
2025-04-29 17:42:35,078 build_posterior_wrapper took 0.007501 seconds
2025-04-29 17:42:35,079 TPE using 37/37 trials with best loss inf
2025-04-29 17:42:35,169 build_posterior_wrapper took 0.007838 seconds
2025-04-29 17:42:35,169 TPE using 38/38 trials with best loss inf
2025-04-29 17:42:35,254 build_posterior_wrapper took 0.007909 seconds
2025-04-29 17:42:35,254 TPE using 39/39 trials with best loss inf
2025-04-29 17:42:35,342 build_posterior_wrapper took 0.007122 seconds
2025-04-29 17:42:35,342 TPE using 40/40 trials with best loss inf
2025-04-29 17:42:35,443 build_posterior_wrapper took 0.007765 seconds
2025-04-29 17:42:35,443 TPE using 41/41 trials with best loss inf
2025-04-29 17:42:35,535 build_posterior_wrapper took 0.008672 seconds
2025-04-29 17:42:35,535 TPE using 42/42 trials with best loss inf
2025-04-29 17:42:35,621 build_posterior_wrapper took 0.007888 seconds
2025-04-29 17:42:35,621 TPE using 43/43 trials with best loss inf
2025-04-29 17:42:35,714 build_posterior_wrapper took 0.008328 seconds
2025-04-29 17:42:35,714 TPE using 44/44 trials with best loss inf
2025-04-29 17:42:35,802 build_posterior_wrapper took 0.007048 seconds
2025-04-29 17:42:35,802 TPE using 45/45 trials with best loss inf
2025-04-29 17:42:35,890 build_posterior_wrapper took 0.007408 seconds
2025-04-29 17:42:35,890 TPE using 46/46 trials with best loss inf
2025-04-29 17:42:35,986 build_posterior_wrapper took 0.010581 seconds
2025-04-29 17:42:35,986 TPE using 47/47 trials with best loss inf
2025-04-29 17:42:36,089 build_posterior_wrapper took 0.007251 seconds
2025-04-29 17:42:36,089 TPE using 48/48 trials with best loss inf
2025-04-29 17:42:36,181 build_posterior_wrapper took 0.010054 seconds
2025-04-29 17:42:36,182 TPE using 49/49 trials with best loss inf
2025-04-29 17:42:36,271 build_posterior_wrapper took 0.007465 seconds
2025-04-29 17:42:36,271 TPE using 50/50 trials with best loss inf
2025-04-29 17:42:36,359 build_posterior_wrapper took 0.007206 seconds
2025-04-29 17:42:36,359 TPE using 51/51 trials with best loss inf
2025-04-29 17:42:36,451 build_posterior_wrapper took 0.010926 seconds
2025-04-29 17:42:36,451 TPE using 52/52 trials with best loss inf
2025-04-29 17:42:36,548 build_posterior_wrapper took 0.008541 seconds
2025-04-29 17:42:36,548 TPE using 53/53 trials with best loss inf
2025-04-29 17:42:36,645 build_posterior_wrapper took 0.008646 seconds
2025-04-29 17:42:36,646 TPE using 54/54 trials with best loss inf
2025-04-29 17:42:36,749 build_posterior_wrapper took 0.008455 seconds
2025-04-29 17:42:36,749 TPE using 55/55 trials with best loss inf
2025-04-29 17:42:36,840 build_posterior_wrapper took 0.007144 seconds
2025-04-29 17:42:36,840 TPE using 56/56 trials with best loss inf
2025-04-29 17:42:36,935 build_posterior_wrapper took 0.007137 seconds
2025-04-29 17:42:36,936 TPE using 57/57 trials with best loss inf
2025-04-29 17:42:37,028 build_posterior_wrapper took 0.008070 seconds
2025-04-29 17:42:37,028 TPE using 58/58 trials with best loss inf
2025-04-29 17:42:37,122 build_posterior_wrapper took 0.006882 seconds
2025-04-29 17:42:37,123 TPE using 59/59 trials with best loss inf
2025-04-29 17:42:37,216 build_posterior_wrapper took 0.007576 seconds
2025-04-29 17:42:37,216 TPE using 60/60 trials with best loss inf
2025-04-29 17:42:37,312 build_posterior_wrapper took 0.009154 seconds
2025-04-29 17:42:37,313 TPE using 61/61 trials with best loss inf
2025-04-29 17:42:37,459 build_posterior_wrapper took 0.012465 seconds
2025-04-29 17:42:37,459 TPE using 62/62 trials with best loss inf
2025-04-29 17:42:37,559 build_posterior_wrapper took 0.008048 seconds
2025-04-29 17:42:37,559 TPE using 63/63 trials with best loss inf
2025-04-29 17:42:37,659 build_posterior_wrapper took 0.007810 seconds
2025-04-29 17:42:37,659 TPE using 64/64 trials with best loss inf
2025-04-29 17:42:37,760 build_posterior_wrapper took 0.009324 seconds
2025-04-29 17:42:37,761 TPE using 65/65 trials with best loss inf
2025-04-29 17:42:37,860 build_posterior_wrapper took 0.008439 seconds
2025-04-29 17:42:37,861 TPE using 66/66 trials with best loss inf
2025-04-29 17:42:37,968 build_posterior_wrapper took 0.007970 seconds
2025-04-29 17:42:37,968 TPE using 67/67 trials with best loss inf
2025-04-29 17:42:38,069 build_posterior_wrapper took 0.008062 seconds
2025-04-29 17:42:38,069 TPE using 68/68 trials with best loss inf
2025-04-29 17:42:38,170 build_posterior_wrapper took 0.007281 seconds
2025-04-29 17:42:38,170 TPE using 69/69 trials with best loss inf
2025-04-29 17:42:38,275 build_posterior_wrapper took 0.007318 seconds
2025-04-29 17:42:38,276 TPE using 70/70 trials with best loss inf
2025-04-29 17:42:38,372 build_posterior_wrapper took 0.007241 seconds
2025-04-29 17:42:38,372 TPE using 71/71 trials with best loss inf
2025-04-29 17:42:38,474 build_posterior_wrapper took 0.006893 seconds
2025-04-29 17:42:38,474 TPE using 72/72 trials with best loss inf
2025-04-29 17:42:38,583 build_posterior_wrapper took 0.008798 seconds
2025-04-29 17:42:38,583 TPE using 73/73 trials with best loss inf
2025-04-29 17:42:38,684 build_posterior_wrapper took 0.010276 seconds
2025-04-29 17:42:38,684 TPE using 74/74 trials with best loss inf
2025-04-29 17:42:38,782 build_posterior_wrapper took 0.008181 seconds
2025-04-29 17:42:38,782 TPE using 75/75 trials with best loss inf
2025-04-29 17:42:38,883 build_posterior_wrapper took 0.007250 seconds
2025-04-29 17:42:38,884 TPE using 76/76 trials with best loss inf
2025-04-29 17:42:38,988 build_posterior_wrapper took 0.006893 seconds
2025-04-29 17:42:38,988 TPE using 77/77 trials with best loss inf
2025-04-29 17:42:39,091 build_posterior_wrapper took 0.007084 seconds
2025-04-29 17:42:39,092 TPE using 78/78 trials with best loss inf
2025-04-29 17:42:39,198 build_posterior_wrapper took 0.009275 seconds
2025-04-29 17:42:39,199 TPE using 79/79 trials with best loss inf
2025-04-29 17:42:39,294 build_posterior_wrapper took 0.007326 seconds
2025-04-29 17:42:39,295 TPE using 80/80 trials with best loss inf
2025-04-29 17:42:39,391 build_posterior_wrapper took 0.007216 seconds
2025-04-29 17:42:39,391 TPE using 81/81 trials with best loss inf
2025-04-29 17:42:39,492 build_posterior_wrapper took 0.006890 seconds
2025-04-29 17:42:39,492 TPE using 82/82 trials with best loss inf
2025-04-29 17:42:39,594 build_posterior_wrapper took 0.008703 seconds
2025-04-29 17:42:39,595 TPE using 83/83 trials with best loss inf
2025-04-29 17:42:39,701 build_posterior_wrapper took 0.007618 seconds
2025-04-29 17:42:39,702 TPE using 84/84 trials with best loss inf
2025-04-29 17:42:39,802 build_posterior_wrapper took 0.007086 seconds
2025-04-29 17:42:39,802 TPE using 85/85 trials with best loss inf
2025-04-29 17:42:39,921 build_posterior_wrapper took 0.007385 seconds
2025-04-29 17:42:39,922 TPE using 86/86 trials with best loss inf
2025-04-29 17:42:40,025 build_posterior_wrapper took 0.007012 seconds
2025-04-29 17:42:40,026 TPE using 87/87 trials with best loss inf
2025-04-29 17:42:40,128 build_posterior_wrapper took 0.007511 seconds
2025-04-29 17:42:40,128 TPE using 88/88 trials with best loss inf
2025-04-29 17:42:40,231 build_posterior_wrapper took 0.007397 seconds
2025-04-29 17:42:40,231 TPE using 89/89 trials with best loss inf
2025-04-29 17:42:40,332 build_posterior_wrapper took 0.008741 seconds
2025-04-29 17:42:40,333 TPE using 90/90 trials with best loss inf
2025-04-29 17:42:40,437 build_posterior_wrapper took 0.007137 seconds
2025-04-29 17:42:40,437 TPE using 91/91 trials with best loss inf
2025-04-29 17:42:40,550 build_posterior_wrapper took 0.007037 seconds
2025-04-29 17:42:40,550 TPE using 92/92 trials with best loss inf
2025-04-29 17:42:40,651 build_posterior_wrapper took 0.007006 seconds
2025-04-29 17:42:40,652 TPE using 93/93 trials with best loss inf
2025-04-29 17:42:40,755 build_posterior_wrapper took 0.007333 seconds
2025-04-29 17:42:40,756 TPE using 94/94 trials with best loss inf
2025-04-29 17:42:40,860 build_posterior_wrapper took 0.007523 seconds
2025-04-29 17:42:40,860 TPE using 95/95 trials with best loss inf
2025-04-29 17:42:40,967 build_posterior_wrapper took 0.007028 seconds
2025-04-29 17:42:40,967 TPE using 96/96 trials with best loss inf
2025-04-29 17:42:41,074 build_posterior_wrapper took 0.007179 seconds
2025-04-29 17:42:41,074 TPE using 97/97 trials with best loss inf
2025-04-29 17:42:41,183 build_posterior_wrapper took 0.011309 seconds
2025-04-29 17:42:41,183 TPE using 98/98 trials with best loss inf
2025-04-29 17:42:41,289 build_posterior_wrapper took 0.006845 seconds
2025-04-29 17:42:41,289 TPE using 99/99 trials with best loss inf
2025-04-29 17:42:41,417 build_posterior_wrapper took 0.008141 seconds
2025-04-29 17:42:41,417 TPE using 100/100 trials with best loss inf
2025-04-29 17:42:41,531 build_posterior_wrapper took 0.007900 seconds
2025-04-29 17:42:41,531 TPE using 101/101 trials with best loss inf
2025-04-29 17:42:41,639 build_posterior_wrapper took 0.007020 seconds
2025-04-29 17:42:41,639 TPE using 102/102 trials with best loss inf
2025-04-29 17:42:41,754 build_posterior_wrapper took 0.008733 seconds
2025-04-29 17:42:41,754 TPE using 103/103 trials with best loss inf
2025-04-29 17:42:41,869 build_posterior_wrapper took 0.010875 seconds
2025-04-29 17:42:41,869 TPE using 104/104 trials with best loss inf
2025-04-29 17:42:41,980 build_posterior_wrapper took 0.009022 seconds
2025-04-29 17:42:41,980 TPE using 105/105 trials with best loss inf
2025-04-29 17:42:42,107 build_posterior_wrapper took 0.007547 seconds
2025-04-29 17:42:42,108 TPE using 106/106 trials with best loss inf
2025-04-29 17:42:42,271 build_posterior_wrapper took 0.008045 seconds
2025-04-29 17:42:42,272 TPE using 107/107 trials with best loss inf
2025-04-29 17:42:42,383 build_posterior_wrapper took 0.007661 seconds
2025-04-29 17:42:42,384 TPE using 108/108 trials with best loss inf
2025-04-29 17:42:42,509 build_posterior_wrapper took 0.008580 seconds
2025-04-29 17:42:42,510 TPE using 109/109 trials with best loss inf
2025-04-29 17:42:42,641 build_posterior_wrapper took 0.010203 seconds
2025-04-29 17:42:42,642 TPE using 110/110 trials with best loss inf
2025-04-29 17:42:42,767 build_posterior_wrapper took 0.007812 seconds
2025-04-29 17:42:42,767 TPE using 111/111 trials with best loss inf
2025-04-29 17:42:42,879 build_posterior_wrapper took 0.007241 seconds
2025-04-29 17:42:42,880 TPE using 112/112 trials with best loss inf
2025-04-29 17:42:43,004 build_posterior_wrapper took 0.007741 seconds
2025-04-29 17:42:43,004 TPE using 113/113 trials with best loss inf
2025-04-29 17:42:43,129 build_posterior_wrapper took 0.010171 seconds
2025-04-29 17:42:43,129 TPE using 114/114 trials with best loss inf
2025-04-29 17:42:43,251 build_posterior_wrapper took 0.008445 seconds
2025-04-29 17:42:43,252 TPE using 115/115 trials with best loss inf
2025-04-29 17:42:43,379 build_posterior_wrapper took 0.008807 seconds
2025-04-29 17:42:43,379 TPE using 116/116 trials with best loss inf
2025-04-29 17:42:43,508 build_posterior_wrapper took 0.008237 seconds
2025-04-29 17:42:43,509 TPE using 117/117 trials with best loss inf
2025-04-29 17:42:43,633 build_posterior_wrapper took 0.011576 seconds
2025-04-29 17:42:43,634 TPE using 118/118 trials with best loss inf
2025-04-29 17:42:43,768 build_posterior_wrapper took 0.007443 seconds
2025-04-29 17:42:43,768 TPE using 119/119 trials with best loss inf
2025-04-29 17:42:43,894 build_posterior_wrapper took 0.009691 seconds
2025-04-29 17:42:43,894 TPE using 120/120 trials with best loss inf
2025-04-29 17:42:44,020 build_posterior_wrapper took 0.007299 seconds
2025-04-29 17:42:44,020 TPE using 121/121 trials with best loss inf
2025-04-29 17:42:44,150 build_posterior_wrapper took 0.012646 seconds
2025-04-29 17:42:44,150 TPE using 122/122 trials with best loss inf
2025-04-29 17:42:44,271 build_posterior_wrapper took 0.007092 seconds
2025-04-29 17:42:44,271 TPE using 123/123 trials with best loss inf
2025-04-29 17:42:44,391 build_posterior_wrapper took 0.009642 seconds
2025-04-29 17:42:44,392 TPE using 124/124 trials with best loss inf
2025-04-29 17:42:44,512 build_posterior_wrapper took 0.007731 seconds
2025-04-29 17:42:44,513 TPE using 125/125 trials with best loss inf
2025-04-29 17:42:44,630 build_posterior_wrapper took 0.008486 seconds
2025-04-29 17:42:44,630 TPE using 126/126 trials with best loss inf
2025-04-29 17:42:44,750 build_posterior_wrapper took 0.008157 seconds
2025-04-29 17:42:44,750 TPE using 127/127 trials with best loss inf
2025-04-29 17:42:44,866 build_posterior_wrapper took 0.010003 seconds
2025-04-29 17:42:44,867 TPE using 128/128 trials with best loss inf
2025-04-29 17:42:52,029 <core_utils.redis_cache.EvalRedis object at 0x79fa86183400>
2025-04-29 17:42:52,034 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:42:52,061 build_posterior_wrapper took 0.008286 seconds
2025-04-29 17:42:52,062 TPE using 128/128 trials with best loss inf
2025-04-29 17:53:00,465 <core_utils.redis_cache.EvalRedis object at 0x76896e96b430>
2025-04-29 17:53:00,471 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:53:00,498 build_posterior_wrapper took 0.007990 seconds
2025-04-29 17:53:00,500 TPE using 128/128 trials with best loss inf
2025-04-29 17:55:34,750 <core_utils.redis_cache.EvalRedis object at 0x70de5c38f430>
2025-04-29 17:55:34,755 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:55:34,782 build_posterior_wrapper took 0.007243 seconds
2025-04-29 17:55:34,783 TPE using 128/128 trials with best loss inf
2025-04-29 17:55:53,291 <core_utils.redis_cache.EvalRedis object at 0x723cf6b8b430>
2025-04-29 17:55:53,296 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:55:53,321 build_posterior_wrapper took 0.007567 seconds
2025-04-29 17:55:53,322 TPE using 128/128 trials with best loss inf
2025-04-29 17:57:35,834 <core_utils.redis_cache.EvalRedis object at 0x77ffe2a73460>
2025-04-29 17:57:35,839 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:57:35,866 build_posterior_wrapper took 0.008614 seconds
2025-04-29 17:57:35,867 TPE using 128/128 trials with best loss inf
2025-04-29 17:58:21,146 <core_utils.redis_cache.EvalRedis object at 0x7fa728d7b430>
2025-04-29 17:58:21,151 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:58:21,173 build_posterior_wrapper took 0.007499 seconds
2025-04-29 17:58:21,174 TPE using 128/128 trials with best loss inf
2025-04-29 17:58:50,769 <core_utils.redis_cache.EvalRedis object at 0x78b5afed7430>
2025-04-29 17:58:50,775 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 17:58:50,831 build_posterior_wrapper took 0.011770 seconds
2025-04-29 17:58:50,832 TPE using 128/128 trials with best loss inf
2025-04-29 18:02:20,808 <core_utils.redis_cache.EvalRedis object at 0x722d1267b430>
2025-04-29 18:02:20,817 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 18:02:20,871 build_posterior_wrapper took 0.017262 seconds
2025-04-29 18:02:20,872 TPE using 128/128 trials with best loss inf
2025-04-29 18:06:22,797 <core_utils.redis_cache.EvalRedis object at 0x75abfbf13550>
2025-04-29 18:06:22,802 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 18:06:22,828 build_posterior_wrapper took 0.008425 seconds
2025-04-29 18:06:22,829 TPE using 128/128 trials with best loss inf
2025-04-29 18:08:53,586 <core_utils.redis_cache.EvalRedis object at 0x7c3140d9f520>
2025-04-29 18:08:53,592 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-04-29 18:08:53,616 build_posterior_wrapper took 0.007320 seconds
2025-04-29 18:08:53,617 TPE using 128/128 trials with best loss inf
2025-05-02 09:34:00,019 <core_utils.redis_cache.EvalRedis object at 0x71fac2a7b4f0>
2025-05-02 09:34:00,034 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:34:00,058 build_posterior_wrapper took 0.008043 seconds
2025-05-02 09:34:00,060 TPE using 128/128 trials with best loss inf
2025-05-02 09:43:42,563 <core_utils.redis_cache.EvalRedis object at 0x731aacd8f520>
2025-05-02 09:43:42,573 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:43:42,599 build_posterior_wrapper took 0.007057 seconds
2025-05-02 09:43:42,600 TPE using 128/128 trials with best loss inf
2025-05-02 09:51:33,151 <core_utils.redis_cache.EvalRedis object at 0x76f06ab77460>
2025-05-02 09:51:33,156 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:51:33,204 build_posterior_wrapper took 0.017302 seconds
2025-05-02 09:51:33,205 TPE using 128/128 trials with best loss inf
2025-05-02 09:53:04,626 <core_utils.redis_cache.EvalRedis object at 0x712b2e197430>
2025-05-02 09:53:04,631 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:53:04,661 job exception: 
2025-05-02 09:54:31,400 <core_utils.redis_cache.EvalRedis object at 0x739d3cd73430>
2025-05-02 09:54:31,405 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:54:36,317 build_posterior_wrapper took 0.016969 seconds
2025-05-02 09:54:36,317 TPE using 1/1 trials with best loss inf
2025-05-02 09:54:36,358 build_posterior_wrapper took 0.016208 seconds
2025-05-02 09:54:36,358 TPE using 2/2 trials with best loss inf
2025-05-02 09:54:36,396 build_posterior_wrapper took 0.015617 seconds
2025-05-02 09:54:36,397 TPE using 3/3 trials with best loss inf
2025-05-02 09:54:36,441 build_posterior_wrapper took 0.017561 seconds
2025-05-02 09:54:36,441 TPE using 4/4 trials with best loss inf
2025-05-02 09:54:36,480 build_posterior_wrapper took 0.016218 seconds
2025-05-02 09:54:36,481 TPE using 5/5 trials with best loss inf
2025-05-02 09:54:36,529 build_posterior_wrapper took 0.018936 seconds
2025-05-02 09:54:36,530 TPE using 6/6 trials with best loss inf
2025-05-02 09:54:36,587 build_posterior_wrapper took 0.019835 seconds
2025-05-02 09:54:36,587 TPE using 7/7 trials with best loss inf
2025-05-02 09:54:36,633 build_posterior_wrapper took 0.016134 seconds
2025-05-02 09:54:36,634 TPE using 8/8 trials with best loss inf
2025-05-02 09:54:36,673 build_posterior_wrapper took 0.016486 seconds
2025-05-02 09:54:36,673 TPE using 9/9 trials with best loss inf
2025-05-02 09:54:36,719 build_posterior_wrapper took 0.020208 seconds
2025-05-02 09:54:36,720 TPE using 10/10 trials with best loss inf
2025-05-02 09:54:36,822 build_posterior_wrapper took 0.070257 seconds
2025-05-02 09:54:36,822 TPE using 11/11 trials with best loss inf
2025-05-02 09:54:36,862 build_posterior_wrapper took 0.015530 seconds
2025-05-02 09:54:36,862 TPE using 12/12 trials with best loss inf
2025-05-02 09:54:36,901 build_posterior_wrapper took 0.016133 seconds
2025-05-02 09:54:36,902 TPE using 13/13 trials with best loss inf
2025-05-02 09:54:36,944 build_posterior_wrapper took 0.016599 seconds
2025-05-02 09:54:36,945 TPE using 14/14 trials with best loss inf
2025-05-02 09:54:36,990 build_posterior_wrapper took 0.018665 seconds
2025-05-02 09:54:36,991 TPE using 15/15 trials with best loss inf
2025-05-02 09:54:37,041 build_posterior_wrapper took 0.021738 seconds
2025-05-02 09:54:37,042 TPE using 16/16 trials with best loss inf
2025-05-02 09:54:37,085 build_posterior_wrapper took 0.017110 seconds
2025-05-02 09:54:37,085 TPE using 17/17 trials with best loss inf
2025-05-02 09:54:37,126 build_posterior_wrapper took 0.016283 seconds
2025-05-02 09:54:37,127 TPE using 18/18 trials with best loss inf
2025-05-02 09:54:37,168 build_posterior_wrapper took 0.017120 seconds
2025-05-02 09:54:37,168 TPE using 19/19 trials with best loss inf
2025-05-02 09:54:37,216 build_posterior_wrapper took 0.019325 seconds
2025-05-02 09:54:37,217 TPE using 20/20 trials with best loss inf
2025-05-02 09:54:37,317 build_posterior_wrapper took 0.016162 seconds
2025-05-02 09:54:37,317 TPE using 21/21 trials with best loss inf
2025-05-02 09:54:37,414 build_posterior_wrapper took 0.017144 seconds
2025-05-02 09:54:37,414 TPE using 22/22 trials with best loss inf
2025-05-02 09:54:37,518 build_posterior_wrapper took 0.019021 seconds
2025-05-02 09:54:37,519 TPE using 23/23 trials with best loss inf
2025-05-02 09:54:37,621 build_posterior_wrapper took 0.018956 seconds
2025-05-02 09:54:37,622 TPE using 24/24 trials with best loss inf
2025-05-02 09:54:37,736 build_posterior_wrapper took 0.019342 seconds
2025-05-02 09:54:37,737 TPE using 25/25 trials with best loss inf
2025-05-02 09:54:37,840 build_posterior_wrapper took 0.017764 seconds
2025-05-02 09:54:37,841 TPE using 26/26 trials with best loss inf
2025-05-02 09:54:37,972 build_posterior_wrapper took 0.021741 seconds
2025-05-02 09:54:37,973 TPE using 27/27 trials with best loss inf
2025-05-02 09:54:38,107 build_posterior_wrapper took 0.023221 seconds
2025-05-02 09:54:38,108 TPE using 28/28 trials with best loss inf
2025-05-02 09:54:38,249 build_posterior_wrapper took 0.035490 seconds
2025-05-02 09:54:38,249 TPE using 29/29 trials with best loss inf
2025-05-02 09:54:38,385 build_posterior_wrapper took 0.021088 seconds
2025-05-02 09:54:38,386 TPE using 30/30 trials with best loss inf
2025-05-02 09:54:38,509 build_posterior_wrapper took 0.018758 seconds
2025-05-02 09:54:38,510 TPE using 31/31 trials with best loss inf
2025-05-02 09:54:38,653 build_posterior_wrapper took 0.030650 seconds
2025-05-02 09:54:38,654 TPE using 32/32 trials with best loss inf
2025-05-02 09:54:38,794 build_posterior_wrapper took 0.023081 seconds
2025-05-02 09:54:38,795 TPE using 33/33 trials with best loss inf
2025-05-02 09:54:38,940 build_posterior_wrapper took 0.024370 seconds
2025-05-02 09:54:38,941 TPE using 34/34 trials with best loss inf
2025-05-02 09:54:39,097 build_posterior_wrapper took 0.023144 seconds
2025-05-02 09:54:39,098 TPE using 35/35 trials with best loss inf
2025-05-02 09:54:39,216 build_posterior_wrapper took 0.019775 seconds
2025-05-02 09:54:39,216 TPE using 36/36 trials with best loss inf
2025-05-02 09:54:39,328 build_posterior_wrapper took 0.017749 seconds
2025-05-02 09:54:39,328 TPE using 37/37 trials with best loss inf
2025-05-02 09:54:39,437 build_posterior_wrapper took 0.017379 seconds
2025-05-02 09:54:39,437 TPE using 38/38 trials with best loss inf
2025-05-02 09:54:39,554 build_posterior_wrapper took 0.024956 seconds
2025-05-02 09:54:39,554 TPE using 39/39 trials with best loss inf
2025-05-02 09:54:39,688 build_posterior_wrapper took 0.019223 seconds
2025-05-02 09:54:39,688 TPE using 40/40 trials with best loss inf
2025-05-02 09:54:57,170 <core_utils.redis_cache.EvalRedis object at 0x7cce92b7b490>
2025-05-02 09:54:57,175 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:54:57,220 build_posterior_wrapper took 0.016377 seconds
2025-05-02 09:54:57,221 TPE using 40/40 trials with best loss inf
2025-05-02 09:55:14,747 <core_utils.redis_cache.EvalRedis object at 0x768117987490>
2025-05-02 09:55:14,761 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:55:14,807 build_posterior_wrapper took 0.017553 seconds
2025-05-02 09:55:14,808 TPE using 40/40 trials with best loss inf
2025-05-02 09:55:29,952 <core_utils.redis_cache.EvalRedis object at 0x76509e183490>
2025-05-02 09:55:29,958 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:55:30,009 build_posterior_wrapper took 0.019279 seconds
2025-05-02 09:55:30,010 TPE using 40/40 trials with best loss inf
2025-05-02 09:57:58,351 <core_utils.redis_cache.EvalRedis object at 0x77782ab8f490>
2025-05-02 09:57:58,356 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 09:57:58,404 build_posterior_wrapper took 0.017382 seconds
2025-05-02 09:57:58,405 TPE using 40/40 trials with best loss inf
2025-05-02 10:00:54,795 <core_utils.redis_cache.EvalRedis object at 0x7c745e173430>
2025-05-02 10:00:54,800 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 10:00:54,849 build_posterior_wrapper took 0.017900 seconds
2025-05-02 10:00:54,850 TPE using 40/40 trials with best loss inf
2025-05-02 10:01:00,522 build_posterior_wrapper took 0.016832 seconds
2025-05-02 10:01:00,522 TPE using 41/41 trials with best loss inf
2025-05-02 10:01:00,628 build_posterior_wrapper took 0.016447 seconds
2025-05-02 10:01:00,628 TPE using 42/42 trials with best loss inf
2025-05-02 10:01:00,738 build_posterior_wrapper took 0.018281 seconds
2025-05-02 10:01:00,738 TPE using 43/43 trials with best loss inf
2025-05-02 10:01:00,849 build_posterior_wrapper took 0.016627 seconds
2025-05-02 10:01:00,849 TPE using 44/44 trials with best loss inf
2025-05-02 10:01:00,982 build_posterior_wrapper took 0.017984 seconds
2025-05-02 10:01:00,982 TPE using 45/45 trials with best loss inf
2025-05-02 10:01:01,094 build_posterior_wrapper took 0.017941 seconds
2025-05-02 10:01:01,095 TPE using 46/46 trials with best loss inf
2025-05-02 10:01:01,257 build_posterior_wrapper took 0.067360 seconds
2025-05-02 10:01:01,258 TPE using 47/47 trials with best loss inf
2025-05-02 10:01:01,370 build_posterior_wrapper took 0.016699 seconds
2025-05-02 10:01:01,370 TPE using 48/48 trials with best loss inf
2025-05-02 10:01:01,494 build_posterior_wrapper took 0.018262 seconds
2025-05-02 10:01:01,495 TPE using 49/49 trials with best loss inf
2025-05-02 10:01:01,616 build_posterior_wrapper took 0.017932 seconds
2025-05-02 10:01:01,616 TPE using 50/50 trials with best loss inf
2025-05-02 10:01:01,745 build_posterior_wrapper took 0.019407 seconds
2025-05-02 10:01:01,746 TPE using 51/51 trials with best loss inf
2025-05-02 10:01:01,871 build_posterior_wrapper took 0.020493 seconds
2025-05-02 10:01:01,871 TPE using 52/52 trials with best loss inf
2025-05-02 10:01:02,004 build_posterior_wrapper took 0.018286 seconds
2025-05-02 10:01:02,004 TPE using 53/53 trials with best loss inf
2025-05-02 10:01:02,129 build_posterior_wrapper took 0.017292 seconds
2025-05-02 10:01:02,129 TPE using 54/54 trials with best loss inf
2025-05-02 10:01:02,257 build_posterior_wrapper took 0.017643 seconds
2025-05-02 10:01:02,257 TPE using 55/55 trials with best loss inf
2025-05-02 10:01:02,381 build_posterior_wrapper took 0.017302 seconds
2025-05-02 10:01:02,381 TPE using 56/56 trials with best loss inf
2025-05-02 10:01:02,515 build_posterior_wrapper took 0.017680 seconds
2025-05-02 10:01:02,515 TPE using 57/57 trials with best loss inf
2025-05-02 10:01:02,640 build_posterior_wrapper took 0.017596 seconds
2025-05-02 10:01:02,640 TPE using 58/58 trials with best loss inf
2025-05-02 10:01:02,772 build_posterior_wrapper took 0.018353 seconds
2025-05-02 10:01:02,773 TPE using 59/59 trials with best loss inf
2025-05-02 10:01:02,904 build_posterior_wrapper took 0.020875 seconds
2025-05-02 10:01:02,904 TPE using 60/60 trials with best loss inf
2025-05-02 10:01:13,994 <core_utils.redis_cache.EvalRedis object at 0x7ad7cab77460>
2025-05-02 10:01:13,999 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 10:01:14,045 build_posterior_wrapper took 0.016756 seconds
2025-05-02 10:01:14,046 TPE using 60/60 trials with best loss inf
2025-05-02 10:02:46,359 <core_utils.redis_cache.EvalRedis object at 0x7c8c50d8b4c0>
2025-05-02 10:02:46,363 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 10:02:46,410 build_posterior_wrapper took 0.017016 seconds
2025-05-02 10:02:46,410 TPE using 60/60 trials with best loss inf
2025-05-02 10:08:59,003 <core_utils.redis_cache.EvalRedis object at 0x7ecfde55f460>
2025-05-02 10:08:59,008 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 10:08:59,062 build_posterior_wrapper took 0.019194 seconds
2025-05-02 10:08:59,063 TPE using 60/60 trials with best loss inf
2025-05-02 10:09:03,790 build_posterior_wrapper took 0.016264 seconds
2025-05-02 10:09:03,791 TPE using 61/61 trials with best loss inf
2025-05-02 10:09:03,916 build_posterior_wrapper took 0.016922 seconds
2025-05-02 10:09:03,917 TPE using 62/62 trials with best loss inf
2025-05-02 10:09:04,050 build_posterior_wrapper took 0.016777 seconds
2025-05-02 10:09:04,050 TPE using 63/63 trials with best loss inf
2025-05-02 10:09:04,181 build_posterior_wrapper took 0.016371 seconds
2025-05-02 10:09:04,181 TPE using 64/64 trials with best loss inf
2025-05-02 10:09:04,318 build_posterior_wrapper took 0.017316 seconds
2025-05-02 10:09:04,318 TPE using 65/65 trials with best loss inf
2025-05-02 10:09:04,445 build_posterior_wrapper took 0.016778 seconds
2025-05-02 10:09:04,445 TPE using 66/66 trials with best loss inf
2025-05-02 10:09:04,659 build_posterior_wrapper took 0.090903 seconds
2025-05-02 10:09:04,660 TPE using 67/67 trials with best loss inf
2025-05-02 10:09:04,793 build_posterior_wrapper took 0.017936 seconds
2025-05-02 10:09:04,793 TPE using 68/68 trials with best loss inf
2025-05-02 10:29:04,240 <core_utils.redis_cache.EvalRedis object at 0x71696ab83490>
2025-05-02 10:29:04,245 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 10:29:04,293 build_posterior_wrapper took 0.017787 seconds
2025-05-02 10:29:04,294 TPE using 68/68 trials with best loss inf
2025-05-02 11:19:06,320 <core_utils.redis_cache.EvalRedis object at 0x78d568d7f4c0>
2025-05-02 11:19:06,327 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 11:19:06,383 build_posterior_wrapper took 0.019723 seconds
2025-05-02 11:19:06,384 TPE using 68/68 trials with best loss inf
2025-05-02 11:20:02,999 <core_utils.redis_cache.EvalRedis object at 0x74221647f4f0>
2025-05-02 11:20:03,013 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 11:20:03,064 build_posterior_wrapper took 0.017688 seconds
2025-05-02 11:20:03,065 TPE using 68/68 trials with best loss inf
2025-05-02 11:26:07,396 <core_utils.redis_cache.EvalRedis object at 0x77090bfb6170>
2025-05-02 11:26:07,399 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 11:26:07,455 build_posterior_wrapper took 0.004190 seconds
2025-05-02 11:26:07,455 TPE using 68/68 trials with best loss inf
2025-05-02 11:26:07,920 build_posterior_wrapper took 0.004001 seconds
2025-05-02 11:26:07,920 TPE using 69/69 trials with best loss inf
2025-05-02 11:26:08,331 build_posterior_wrapper took 0.003574 seconds
2025-05-02 11:26:08,331 TPE using 70/70 trials with best loss inf
2025-05-02 11:26:08,736 build_posterior_wrapper took 0.003591 seconds
2025-05-02 11:26:08,737 TPE using 71/71 trials with best loss inf
2025-05-02 11:26:09,156 build_posterior_wrapper took 0.003905 seconds
2025-05-02 11:26:09,156 TPE using 72/72 trials with best loss inf
2025-05-02 11:26:09,588 build_posterior_wrapper took 0.003849 seconds
2025-05-02 11:26:09,588 TPE using 73/73 trials with best loss inf
2025-05-02 11:26:10,111 build_posterior_wrapper took 0.004787 seconds
2025-05-02 11:26:10,111 TPE using 74/74 trials with best loss inf
2025-05-02 11:26:10,703 build_posterior_wrapper took 0.004489 seconds
2025-05-02 11:26:10,704 TPE using 75/75 trials with best loss inf
2025-05-02 11:26:11,157 build_posterior_wrapper took 0.004745 seconds
2025-05-02 11:26:11,157 TPE using 76/76 trials with best loss inf
2025-05-02 11:26:11,578 build_posterior_wrapper took 0.003627 seconds
2025-05-02 11:26:11,578 TPE using 77/77 trials with best loss inf
2025-05-02 11:26:12,008 build_posterior_wrapper took 0.003604 seconds
2025-05-02 11:26:12,008 TPE using 78/78 trials with best loss inf
2025-05-02 11:46:39,629 <core_utils.redis_cache.EvalRedis object at 0x723f44d8b4f0>
2025-05-02 11:46:39,635 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 11:46:39,741 build_posterior_wrapper took 0.038687 seconds
2025-05-02 11:46:39,743 TPE using 78/78 trials with best loss inf
2025-05-02 12:17:19,524 <core_utils.redis_cache.EvalRedis object at 0x71f7dd79b4f0>
2025-05-02 12:17:19,538 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:17:19,588 build_posterior_wrapper took 0.017929 seconds
2025-05-02 12:17:19,588 TPE using 78/78 trials with best loss inf
2025-05-02 12:18:02,654 <core_utils.redis_cache.EvalRedis object at 0x7176fe2774c0>
2025-05-02 12:18:02,659 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:18:29,380 <core_utils.redis_cache.EvalRedis object at 0x78d1b2bab4c0>
2025-05-02 12:18:29,385 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:18:44,720 <core_utils.redis_cache.EvalRedis object at 0x7f7b757874c0>
2025-05-02 12:18:44,725 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:20:16,345 build_posterior_wrapper took 0.029010 seconds
2025-05-02 12:20:16,346 TPE using 1/1 trials with best loss inf
2025-05-02 12:21:42,767 <core_utils.redis_cache.EvalRedis object at 0x721039c734c0>
2025-05-02 12:21:42,773 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:21:42,829 build_posterior_wrapper took 0.021266 seconds
2025-05-02 12:21:42,830 TPE using 1/1 trials with best loss inf
2025-05-02 12:23:05,916 <core_utils.redis_cache.EvalRedis object at 0x7ef96e1934f0>
2025-05-02 12:23:05,922 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:23:05,974 build_posterior_wrapper took 0.019125 seconds
2025-05-02 12:23:05,974 TPE using 1/1 trials with best loss inf
2025-05-02 12:32:13,080 <core_utils.redis_cache.EvalRedis object at 0x79453ebccfd0>
2025-05-02 12:32:13,085 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:32:13,136 build_posterior_wrapper took 0.020231 seconds
2025-05-02 12:32:13,137 TPE using 1/1 trials with best loss inf
2025-05-02 12:32:40,029 <core_utils.redis_cache.EvalRedis object at 0x7c85dc3c8fd0>
2025-05-02 12:32:40,036 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:32:40,089 build_posterior_wrapper took 0.020489 seconds
2025-05-02 12:32:40,090 TPE using 1/1 trials with best loss inf
2025-05-02 12:48:04,570 <core_utils.redis_cache.EvalRedis object at 0x72de18d97490>
2025-05-02 12:48:04,574 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 12:48:04,623 build_posterior_wrapper took 0.018515 seconds
2025-05-02 12:48:04,624 TPE using 1/1 trials with best loss inf
2025-05-02 12:48:30,127 build_posterior_wrapper took 0.017584 seconds
2025-05-02 12:48:30,127 TPE using 2/2 trials with best loss -68.219910
2025-05-02 12:48:41,571 build_posterior_wrapper took 0.021084 seconds
2025-05-02 12:48:41,571 TPE using 3/3 trials with best loss -68.219910
2025-05-02 15:10:50,262 <core_utils.redis_cache.EvalRedis object at 0x7c1e63597490>
2025-05-02 15:10:50,269 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 15:10:50,333 build_posterior_wrapper took 0.023656 seconds
2025-05-02 15:10:50,342 TPE using 3/3 trials with best loss -68.219910
2025-05-02 15:11:09,402 <core_utils.redis_cache.EvalRedis object at 0x7492c4d8f4c0>
2025-05-02 15:11:09,409 Redis cache configured with max_memory: 2500mb and eviction_policy: allkeys-lru
2025-05-02 15:11:09,483 build_posterior_wrapper took 0.024830 seconds
2025-05-02 15:11:09,484 TPE using 3/3 trials with best loss -68.219910
2025-05-29 14:55:15,926 <core_utils.redis_cache.EvalRedis object at 0x7ce1793a6c50>
2025-05-29 14:55:15,992 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-05-29 14:56:05,884 

2025-06-04 21:43:42,714 <core_utils.redis_cache.EvalRedis object at 0x772b8acea170>
2025-06-04 21:43:42,807 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 21:43:42,930 

2025-06-04 21:43:42,931 job exception: eval_filter_all_v2() got an unexpected keyword argument 'skip'
2025-06-04 21:43:57,687 <core_utils.redis_cache.EvalRedis object at 0x799e3af69cf0>
2025-06-04 21:43:57,754 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 21:43:57,849 

2025-06-04 21:52:37,528 <core_utils.redis_cache.EvalRedis object at 0x7a01903cdcc0>
2025-06-04 21:52:37,643 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 21:52:37,733 

2025-06-04 22:19:00,287 <core_utils.redis_cache.EvalRedis object at 0x75d619029d80>
2025-06-04 22:19:03,376 Redis connection error during configuration: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:06,450 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:12,773 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:18,826 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:24,973 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:31,128 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:37,267 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:43,437 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:49,648 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:19:55,999 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:02,269 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:08,472 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:14,723 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:21,032 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:27,186 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:33,618 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:39,833 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:46,215 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:52,342 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:20:58,584 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:04,754 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:10,897 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:17,041 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:23,443 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:29,582 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:36,176 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:42,319 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:48,596 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:21:54,769 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:01,295 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:07,481 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:14,064 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:20,239 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:26,606 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:32,754 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:39,346 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:45,527 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:51,696 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:22:57,873 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:04,145 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:10,322 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:16,655 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:22,818 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:31,234 <core_utils.redis_cache.EvalRedis object at 0x7aa15bf65c90>
2025-06-04 22:23:34,320 Redis connection error during configuration: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:37,602 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:43,785 Redis connection error during get_cache: Error 113 connecting to *************:6379. No route to host.
2025-06-04 22:23:47,927 <core_utils.redis_cache.EvalRedis object at 0x70c0534e1d80>
2025-06-04 22:23:47,941 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 22:26:29,138 <core_utils.redis_cache.EvalRedis object at 0x775e9bde5d80>
2025-06-04 22:26:29,143 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 22:54:51,769 <core_utils.redis_cache.EvalRedis object at 0x7d67567bdcf0>
2025-06-04 22:54:51,772 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 22:57:18,771 <core_utils.redis_cache.EvalRedis object at 0x7d128bf8a050>
2025-06-04 22:57:18,779 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-04 23:07:04,862 <core_utils.redis_cache.EvalRedis object at 0x7b81753a6140>
2025-06-04 23:07:04,875 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 09:41:56,130 <core_utils.redis_cache.EvalRedis object at 0x740bd41b1d20>
2025-06-05 09:41:56,133 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 14:10:06,474 <core_utils.redis_cache.EvalRedis object at 0x7733f03b22f0>
2025-06-05 14:10:06,480 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 14:11:29,023 <core_utils.redis_cache.EvalRedis object at 0x79bb1112a3e0>
2025-06-05 14:11:29,032 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 14:22:15,553 <core_utils.redis_cache.EvalRedis object at 0x7666ef566140>
2025-06-05 14:22:15,559 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 15:52:06,766 <core_utils.redis_cache.EvalRedis object at 0x779e2ce1e1a0>
2025-06-05 15:52:06,771 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:04:28,061 <core_utils.redis_cache.EvalRedis object at 0x7e616db59f60>
2025-06-05 16:04:28,068 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:07:52,217 <core_utils.redis_cache.EvalRedis object at 0x7ca1d03623b0>
2025-06-05 16:07:52,222 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:11:50,890 <core_utils.redis_cache.EvalRedis object at 0x7c8f840d62f0>
2025-06-05 16:11:50,895 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:17:35,413 <core_utils.redis_cache.EvalRedis object at 0x750e73556110>
2025-06-05 16:17:35,417 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:17:40,287 <core_utils.redis_cache.EvalRedis object at 0x7acdafd62140>
2025-06-05 16:17:40,293 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:20:03,773 <core_utils.redis_cache.EvalRedis object at 0x72b9382be1a0>
2025-06-05 16:20:03,777 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:20:15,614 <core_utils.redis_cache.EvalRedis object at 0x70246d4e6170>
2025-06-05 16:20:15,624 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:23:57,043 <core_utils.redis_cache.EvalRedis object at 0x7da311e3a110>
2025-06-05 16:23:57,048 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:33:08,772 <core_utils.redis_cache.EvalRedis object at 0x77118c31e1d0>
2025-06-05 16:33:08,777 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:44:24,814 <core_utils.redis_cache.EvalRedis object at 0x7588c9071d20>
2025-06-05 16:44:24,819 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:46:42,954 <core_utils.redis_cache.EvalRedis object at 0x7a5019ab6200>
2025-06-05 16:46:42,959 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:47:36,660 <core_utils.redis_cache.EvalRedis object at 0x738be26f6140>
2025-06-05 16:47:36,665 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:48:15,713 <core_utils.redis_cache.EvalRedis object at 0x7496d996e1a0>
2025-06-05 16:48:15,718 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-06-05 16:50:27,163 <core_utils.redis_cache.EvalRedis object at 0x795f0191e1a0>
2025-06-05 16:50:27,168 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:09:43,229 <core_utils.redis_cache.EvalRedis object at 0x76beba572140>
2025-07-28 10:09:43,260 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:15:38,932 <core_utils.redis_cache.EvalRedis object at 0x7bb7777a5f00>
2025-07-28 10:15:38,949 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:16:10,032 <core_utils.redis_cache.EvalRedis object at 0x720ee4b8f010>
2025-07-28 10:16:10,036 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:16:11,043 <core_utils.redis_cache.EvalRedis object at 0x79c92d1fafb0>
2025-07-28 10:16:11,047 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:16:19,482 <core_utils.redis_cache.EvalRedis object at 0x7235ed59a020>
2025-07-28 10:16:19,493 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:16:55,308 <core_utils.redis_cache.EvalRedis object at 0x7795d01e9f60>
2025-07-28 10:16:55,316 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:17:26,038 <core_utils.redis_cache.EvalRedis object at 0x7a3f79749f60>
2025-07-28 10:17:26,045 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:19:01,212 <core_utils.redis_cache.EvalRedis object at 0x75fa6c31e0b0>
2025-07-28 10:19:01,225 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:19:33,288 <core_utils.redis_cache.EvalRedis object at 0x7125e8d51f90>
2025-07-28 10:19:33,300 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:20:12,268 <core_utils.redis_cache.EvalRedis object at 0x7b55f011a050>
2025-07-28 10:20:12,275 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:20:34,213 <core_utils.redis_cache.EvalRedis object at 0x72339d90df60>
2025-07-28 10:20:34,228 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:21:09,725 <core_utils.redis_cache.EvalRedis object at 0x715e50652080>
2025-07-28 10:21:09,743 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:21:54,196 <core_utils.redis_cache.EvalRedis object at 0x794c0140e080>
2025-07-28 10:21:54,203 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:22:11,033 <core_utils.redis_cache.EvalRedis object at 0x72609759df30>
2025-07-28 10:22:11,040 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:22:42,664 <core_utils.redis_cache.EvalRedis object at 0x7fe6b4c32050>
2025-07-28 10:22:42,671 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:23:34,836 <core_utils.redis_cache.EvalRedis object at 0x7c5ac0be6080>
2025-07-28 10:23:34,848 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:26:33,678 <core_utils.redis_cache.EvalRedis object at 0x7b8c50a9df60>
2025-07-28 10:26:33,682 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:27:45,821 <core_utils.redis_cache.EvalRedis object at 0x7c4555c4e050>
2025-07-28 10:27:45,826 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:28:00,661 <core_utils.redis_cache.EvalRedis object at 0x79154eb8e140>
2025-07-28 10:28:00,668 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:29:17,420 <core_utils.redis_cache.EvalRedis object at 0x7b7809f75f30>
2025-07-28 10:29:17,424 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:29:50,065 <core_utils.redis_cache.EvalRedis object at 0x727582b7df00>
2025-07-28 10:29:50,073 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:30:17,156 <core_utils.redis_cache.EvalRedis object at 0x73c89da220e0>
2025-07-28 10:30:17,164 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:30:39,759 <core_utils.redis_cache.EvalRedis object at 0x735074982080>
2025-07-28 10:30:39,767 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:30:56,872 <core_utils.redis_cache.EvalRedis object at 0x78abfed11ff0>
2025-07-28 10:30:56,880 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:31:44,187 <core_utils.redis_cache.EvalRedis object at 0x718011229e70>
2025-07-28 10:31:44,195 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:31:59,588 <core_utils.redis_cache.EvalRedis object at 0x7d3e5d4c9f60>
2025-07-28 10:31:59,596 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:32:10,737 <core_utils.redis_cache.EvalRedis object at 0x79661bc71ea0>
2025-07-28 10:32:10,745 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:36:17,126 <core_utils.redis_cache.EvalRedis object at 0x77da85af1e70>
2025-07-28 10:36:17,135 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:37:50,117 <core_utils.redis_cache.EvalRedis object at 0x771a12775e70>
2025-07-28 10:37:50,129 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:52:37,113 <core_utils.redis_cache.EvalRedis object at 0x773a5530f040>
2025-07-28 10:52:37,117 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:53:49,194 <core_utils.redis_cache.EvalRedis object at 0x7f439ab470a0>
2025-07-28 10:53:49,196 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:55:07,459 <core_utils.redis_cache.EvalRedis object at 0x7fd5e0ec3010>
2025-07-28 10:55:07,462 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:55:24,384 <core_utils.redis_cache.EvalRedis object at 0x7acdde5b30d0>
2025-07-28 10:55:24,387 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 10:55:36,529 <core_utils.redis_cache.EvalRedis object at 0x7435be14efe0>
2025-07-28 10:55:36,532 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 11:26:07,836 <core_utils.redis_cache.EvalRedis object at 0x7b8919ad7040>
2025-07-28 11:26:07,846 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 11:26:27,917 <core_utils.redis_cache.EvalRedis object at 0x7f6c9834f070>
2025-07-28 11:26:27,925 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 11:26:49,165 <core_utils.redis_cache.EvalRedis object at 0x7b325638b010>
2025-07-28 11:26:49,176 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 11:27:01,194 <core_utils.redis_cache.EvalRedis object at 0x790bb4993070>
2025-07-28 11:27:01,196 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 11:27:10,875 <core_utils.redis_cache.EvalRedis object at 0x7f20c191b0a0>
2025-07-28 11:27:10,879 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
2025-07-28 13:09:33,848 <core_utils.redis_cache.EvalRedis object at 0x7483e5879f30>
2025-07-28 13:09:33,892 Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
