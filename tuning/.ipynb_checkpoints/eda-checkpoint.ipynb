#%%
import pandas as pd
from datetime import datetime
from tqdm import tqdm
import time
import os
from ast import literal_eval
from vnstock3 import Vnstock
import pandas as pd
import numpy as np
from sklearn.feature_selection import mutual_info_classif, chi2
from sklearn.linear_model import LogisticRegression
from minepy import MINE
import warnings
warnings.simplefilter(action='ignore')

exec(open('func_task.py').read())
exec(open('prepare.py').read())
from IPython.core.display_functions import display

#%%
def prepare_pdxy(list_processed_ticker, in_folder='../ticker_v1a/'):
    ## combine all ticker data into one file
    ll = []
    for ticker in list_processed_ticker:
        pd_xy = load_data(f'{in_folder}/{ticker}.csv', start=2014)
        pd_xy['Close_TT20'] = handle_input_data(pd_xy, 20)
        pd_xy['min_T20'] = pd_xy['Close'].rolling(20).min()
        pd_xy['min_T14'] = pd_xy['Close'].rolling(14).min()
        pd_xy['max_T14'] = pd_xy['Close'].rolling(14).max()
        pd_xy['Close_T10'] = pd_xy['Close'].shift(10)
        pd_xy['Close_T14'] = pd_xy['Close'].shift(14)
        
        pd_xy['EMA'] = pd_xy['Close'].ewm(span=20, adjust=False).mean()
        
        #Bollinger Bands
        std = pd_xy['Close'].rolling(20).std()
        pd_xy['BB_lower'] = pd_xy['MA20'] - (std * 2)

        #stochastic_oscillator
        smooth_k = 3
        pd_xy['SO_K'] = 100 * ((pd_xy['Close'] - pd_xy['min_T14']) / (pd_xy['max_T14'] - pd_xy['min_T14']))
        pd_xy['SO_D'] = pd_xy['SO_K'].rolling(smooth_k).mean()

        #Momentum Indicator
        pd_xy['MI_10'] = pd_xy['Close'] - pd_xy['Close_T10']
        pd_xy['MI_14'] = pd_xy['Close'] - pd_xy['Close_T14']
        
        for time in range(1, 6):
            col = f"minL{time}"
            pd_xy[col] = pd_xy['Close'].rolling(time).min()
        for name, w in zip(['minW5D', 'minW10D', 'minW20D'], [11, 21, 41]):
            pd_xy[name] = pd_xy['Close'].rolling(w, center=True).min()

        # names = ['minW5D', 'minW10D', 'minW20D']
        # for label_name in names:
        #     for time, col in enumerate(['minL1', 'minL2', 'minL3', 'minL4', 'minL5'], 1):
        #         pd_xy[f'{label_name}_{col}'] = pd_xy[label_name].rolling(time).max().fillna(0)

        pd_xy['Y1'] = 0.0
        for i in range(1, pd_xy.shape[0]):
            if pd_xy.at[i, 'minW10D'] == 1:
                pd_xy.at[i, 'Y1'] = 0.8
                pd_xy.at[i + 1, 'Y1'] = 0.9
                pd_xy.at[i + 2, 'Y1'] = 1.0

        # label

        pd_xy['label1'] = np.where(pd_xy['minL1'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label2'] = np.where(pd_xy['minL2'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label3'] = np.where(pd_xy['minL3'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label4'] = np.where(pd_xy['minL4'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label5'] = np.where(pd_xy['minL5'] == pd_xy['minW5D'], 1, 0)
        pd_xy.dropna(subset="Close_TT20", inplace=True)
        pd_xy.ffill(inplace=True)

        ll.append(pd_xy)
        # print(ticker)

    pdxy = pd.concat(ll, axis=0, ignore_index=True)  # .reset_index(drop=True)
    
    pdxy.replace([np.inf, -np.inf], np.nan, inplace=True)
    pdxy.dropna(axis=1, how='all', inplace=True) #drop column all nan
    # pdxy.dropna(how='all', inplace=True )#drop row nan
    pdxy.dropna(inplace=True)
    

    # minima_idxs = trendln.get_extrema((pd00[cname_key], None), extmethod=trendln.METHOD_NUMDIFF, accuracy=10)
    list_label = ['minW5D', 'minW10D', 'minW20D', 'minL1', 'minL2', 'minL3', 'minL4', 'minL5', 'label1',
                  'label2', 'label3', 'label4', 'label5']
    cname_feature = [f for f in pdxy.columns if f not in ['time', 'ticker', 'ymd', 'L1W', 'H1M', 'H3M', 'H1Y', 'H2Y',
                                                          'L1W', 'L1M', 'L3M', 'L1Y', 'L2Y', 'quarter',
                                                          'Open', 'Close', 'High', 'Low', 'Volume', 'Close_TT20', 'L1W', 'L1M', 'L3M', 'L1Y', 'L2Y', 'H1W', 'H1M', 'H3M', 'H1Y', 'H2Y'] + list_label ]

    # cname_feature = ['time', 'ticker', 'Close_TT20', 'Close_T10', 'min_T20', 'D_MACD', 'D_RSI','MA10', 'MA20', 'EMA', 'BB_lower', 'SO_K', 'SO_D', 'MI_10', 'MI_14']
    cname_tvt = 'tvt'
    split_tvt_v1(pdxy, cname_tvt)
    list_eval = get_list_eval(pdxy, cname_tvt=cname_tvt)

    return {'pdxy': pdxy,
            'cname_feature': cname_feature,
            'list_label': list_label,
            'list_eval': list_eval,
            }

stock_vci = Vnstock(show_log=False).stock(source='VCI')
list_ticker = [f for f in stock_vci.listing.symbols_by_group('VN30')]

dataset = prepare_pdxy(list_ticker)
pd_raw = dataset['pdxy']
feature_cols = dataset['cname_feature']
label_cols = dataset['list_label']
list_eval = dataset['list_eval']
#
cols = feature_cols + label_cols

train_dataset = pd_raw[list_eval[0][1]][cols]
val_dataset = pd_raw[list_eval[3][1]][cols]

train_dataset.to_csv('train.csv', index=False)
val_dataset.to_csv('val.csv', index=False)
#%%
def SMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['MA10']].copy()
    relationship_matrix(df, title=f'minL1 < MA10: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA20']].copy()
    relationship_matrix(df, title=f'minL1 < MA20: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA50']].copy()
    relationship_matrix(df, title=f'minL1 < MA50: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA100']].copy()
    relationship_matrix(df, title=f'minL1 < MA100: {df.shape[0]}')

    df = df_raw[df_raw['minL1'] < df_raw['MA200']].copy()
    relationship_matrix(df, title=f'minL1 < MA200: {df.shape[0]}')



def EMA(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['EMA']].copy()
    relationship_matrix(df, title=f'minL1 < EMA: {df.shape[0]}')


def RSI(df_raw):
    df = df_raw[df_raw['D_RSI'] < 0.1].copy()
    relationship_matrix(df, title=f'D_RSI < 0.1: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.2].copy()
    relationship_matrix(df, title=f'D_RSI < 0.2: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.3].copy()
    relationship_matrix(df, title=f'D_RSI < 0.3: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.4].copy()
    relationship_matrix(df, title=f'D_RSI < 0.4: {df.shape[0]}')

    df = df_raw[df_raw['D_RSI'] < 0.5].copy()
    relationship_matrix(df, title=f'D_RSI < 0.5: {df.shape[0]}')


def MACD(df):
    df = df[np.where((df['D_MACD'].shift(1) < df['D_MACDsign'].shift(1)) &
                     (df['D_MACD'] > df['D_MACDsign']), True, False)].copy()
    relationship_matrix(df, title=f'MACD cắt lên D_MACDsign : {df.shape[0]}')

def sollinger_bands(df_raw):
    df = df_raw[df_raw['minL1'] < df_raw['BB_lower']].copy()
    relationship_matrix(df, title=f'minL1 < BB_lower : {df.shape[0]}')

def stochastic_oscillator(df_raw):
    df = df_raw[(df_raw['SO_K'] < 20) & (df_raw['SO_D'] <20)].copy()
    relationship_matrix(df, title=f'Stochastic_Oscillator < 20 : {df.shape[0]}')

def momentum_indicator(df_raw):
    df = df_raw[(df_raw['MI_10'] > 0)].copy()
    relationship_matrix(df, title=f'MI_10 < 0 : {df.shape[0]}')
    df = df_raw[(df_raw['MI_14'] > 0)].copy()
    relationship_matrix(df, title=f'MI_14 < 0 : {df.shape[0]}')

def hammer_pattern(df_raw):
    body = abs(df_raw['Close'] - df_raw['Open'])
    lo_shadow = df_raw['High'] - df_raw['Low']
    up_shadow = df_raw['High'] - df_raw['Close']
    df = df_raw[(lo_shadow >= 2) & (up_shadow <= body) & (df_raw['Close'] > df_raw['Open'])]
    
    relationship_matrix(df, title=f'Hammer: {df.shape[0]}')

#%%
train_dataset = pd.read_csv('train.csv', converters={'Close_T20': literal_eval})
val_dataset = pd.read_csv('val.csv', converters={'Close_T20': literal_eval})

#%%

#%%
relationship_matrix(train_dataset, title=f'original: {train_dataset.shape[0]}')

condition1 = train_dataset[train_dataset['minL1'] > train_dataset['minL5']].copy()
relationship_matrix(condition1, title=f'minL1> minL5: {condition1.shape[0]}')

condition2 = train_dataset[train_dataset['minL2'] > train_dataset['minL5']].copy()
relationship_matrix(condition2, title=f'minL2> minL5: {condition2.shape[0]}')

condition3 = train_dataset[train_dataset['minL3'] > train_dataset['minL5']].copy()
relationship_matrix(condition3, title=f'minL3> minL5: {condition3.shape[0]}')

SMA(train_dataset)
EMA(train_dataset)
RSI(train_dataset)
MACD(train_dataset)
Bollinger_Bands(train_dataset)
momentum_indicator(train_dataset)
hammer_pattern(train_dataset)

#%%
relationship_variable_bin(train_dataset, 'D_RSI', 'minL3', 'minW5D')
relationship_scatter_plot(train_dataset, 'D_RSI', 'minL3', 'minW5D')
#%%
# train : feature 32K x 10 features / label 32K x 1
# test : feature 8K x 10 features / label 8K x 1
import xgboost as xgb
def train_xgboost(pdx, cname_features, cname_label, cname_tvt):
    ''' pdx : dataframe
        cname_features : list of column names
        cname_label : column name for label (0/1)
        cname_tvt : column name for train, validation, test ('train'/'val'/'test')
    Output:
        model
        pred
    '''
    # split data
    pdx_train = pdx.query(f'{cname_tvt}=="train"')
    pdx_val = pdx.query(f'{cname_tvt}=="val"')
    # train model
    model = xgb.XGBClassifier()
    # using early stopping on val
    model.fit(pdx_train[cname_features],pdx_train[cname_label],eval_set=[(pdx_val[cname_features],pdx_val[cname_label])],verbose=False)
    # predict
    pred = model.predict_proba(pdx[cname_features])[:,1]
    return model,pred

pdx['tvt'] = 'other'
pdx.loc[(pdx['minW5'].notnull()) & (pdx['time']<'2020'),'tvt']='train'
pdx.loc[(pdx['minW5'].notnull()) & (pdx['time']>='2020')  & (pdx['time']<'2021'),'tvt']='val'
pdx.loc[(pdx['minW5'].notnull()) & (pdx['time']>='2021'),'tvt']='test'
model,pdx['pred'] = train_xgboost(pdx, cname_features=list20neg + list20pos, cname_label='label3', cname_tvt='tvt')
get_bin_stats(pdx[ii & (pdx['tvt']=='test')],'pred','label3',bins=10)