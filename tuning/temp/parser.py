import pickle
import re
import pandas as pd

# <PERSON><PERSON><PERSON> dạng regex để trích xuất số trial
tpe_trial_pattern = re.compile(r'TPE using (\d+)/(\d+) trials')

# Đ<PERSON>nh dạng regex để trích xuất các giá trị từ dòng hit, si_deal, ...
metrics_pattern = re.compile(
    r"hit: (?P<hit>\S+), si_deal: (?P<si_deal>\S+), si_return: (?P<si_return>\S+), "
    r"win_hit: (?P<win_hit>\S+), wing_quarter: (?P<wing_quarter>\S+), "
    r"wing_8quarter: (?P<wing_8quarter>\S+), utilization: (?P<utilization>\S+), "
    r"diversity: (?P<diversity>\S+)"
)


def parse_log_to_dataframe(log_text):
    data = []
    trial = None

    for line in log_text.split("\n"):
        trial_match = tpe_trial_pattern.search(line)
        if trial_match:
            trial = int(trial_match.group(1))
            continue

        metrics_match = metrics_pattern.search(line)
        if metrics_match and trial is not None:
            row = {"trial": trial}
            for key, value in metrics_match.groupdict().items():
                row[key] = None if value == 'None' else float(value) if value.replace('.', '', 1).isdigit() else value
            data.append(row)
    return pd.DataFrame(data)


def parse_params_from_trial(trials):
    """Parse the parameters from a TPE trial. convert to dataframe"""
    data = []
    for i, t in enumerate(trials.trials):
        row = {"trial": t['tid'],
               "loss": (t['result']['loss'])}
        for key, value in t['misc']['vals'].items():
            row[key] = value[0]
        data.append(row)

    return pd.DataFrame(data)


# Đọc log từ file hoặc chuỗi log mẫu
trials = pickle.load(open("/home/<USER>/dev/ta/kaffa_v2/tuning/temp/optimize_pattern_weight.pkl", "rb"))
df_param = parse_params_from_trial(trials)

with open("/home/<USER>/dev/ta/kaffa_v2/tuning/monitor_tuning_weigh.log", "r") as f:
    log_sample = f.read()
# log_sample = """(đoạn log mẫu của bạn ở đây)"""
df = parse_log_to_dataframe(log_sample)

df = df.merge(df_param, on='trial', how='left')
df.to_csv("/home/<USER>/dev/ta/kaffa_v2/tuning/monitor_tuning_weigh.csv", index=False)