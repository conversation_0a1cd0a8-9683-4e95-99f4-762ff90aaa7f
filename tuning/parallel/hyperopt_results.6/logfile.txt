INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ce00173dd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x703dfe826c90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750c46233fe0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74cdd63a70b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72aedbee7cb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ef37651ac30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a74a148f080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d9e940fec30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7398078f6ed0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fd1f7f0b020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79e408da31a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x775ee8906fc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b1aa7086e10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b2c7c816930>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73157c81ee70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7300cb21b140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x716de6fab1a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75e0be42bcb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e50bdd27050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a9da0942cc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72cb85fcfdd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7eda446bb470>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c23407230b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f87510b2cc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x736ae2643020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c35e972f1d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x728568bfeea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ad5e662f0b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f0ae272b0e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7836b548f2f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x725714a3f2f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x717d16aafe00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c35e051eba0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x710dcfec6f60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70ec64cb2ed0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79aed4227230>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ccc719eb2f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x771e7a98e3c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71c2394130b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7273dbea2f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x6ffd5f093bc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c295f28af30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e34bc6fbd70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x6ffbb8433140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f41245e7ef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x751ca4a12ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72d96dd74dd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d5d7df26e40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x723c1e3e7290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x713a0a50ee70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bd88d6e3d10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x715076fa2b70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e43b9a3a960>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b86cde73f80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71f286172690>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e9551f8efc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79fa8812abd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71e1731f2ae0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71efbad87200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7726f4a42f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72a67622b290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7af542d2ecc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79d750636ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ae6b122b1a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73521fe23bf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e87fd71ecf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f335c8f70e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7508f938f260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70ef7d492cf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a107c437320>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77a991832f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72cba01f7e30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71a8ab71b140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70e28b9aed50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7609c093ed20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x744bed5e2ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71ee8cb23200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72dd2468f170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74b80de87ec0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78f39f917e30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cc05f572d80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77ae39caeb10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x709944211190>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x717139e279b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x738fccdaaf30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a8ae7d7ae10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x768a4f627170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7141fc89b260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79c03e83b380>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ffe200a6c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x707dfb0833e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bc88388aea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72b073a4f4a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71acf7f9ea50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70f083e8b1d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c76af79abd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x784878222ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f5405fff4a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76fc02b1aba0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77be8b60ed50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73f392f4ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e1b2e4f6f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x759b2a78fef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7784d2247170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e12cfa9ef30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7163f0e9b1d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dac69e93380>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7156e5a58ec0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79ed8789ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f279de8b020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71650788fc80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71e935fa2f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x738b2e6c63c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x723cf1f63bf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78ff97137560>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x720f2982ec90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73377d63f440>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ae8f49038c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ac3a31232f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78fb64c931d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a7e3729f6e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d657e51ee70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f8e2fa56450>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ff4836fce0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74ae8b1cbda0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7318aa1e6f60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x733e7cfb7ad0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x778a2f7ecc80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78bfc39c30b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73c5ec51ac30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e6eadc87d40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x760f5629ade0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7032858a2f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75b29697ae40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750691d874a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74d6ff543e00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7104da82b110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x764d9263f2c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74c4935076e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78789f673c20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75004d91ede0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f45f3102d80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77552c56ed50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fb004dd3e30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x786beec2b6e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74e18ab0ea80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d59f72bffb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c479eb0ae40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x744aef142e40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x731b31b2fef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x776c8dcb2ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x748584a83a70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x748933bab2f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73589ea23080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e9bdc887140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ee7790bae70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d13a1726f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f2a72fb6fc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77fe42c3af00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7255e73fef90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x724981e8abd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7faff469ad80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x749b8768af60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fc92b9c3650>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x774f02122f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x793fb1d1ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76ea5e287b30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7185ad212600>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x707357b9e600>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x749fabc75790>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7846c26e3c20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77b94c83b110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f44332c2d80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7782f8206f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ae8ee512180>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fc005771340>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a18f7b9ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b12888e3d40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b8091226f00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a1cdf9ba990>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73fd19adf350>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70a5c73ae3c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76dfc7220410>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d131eb771a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76c315eb3290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x773214f23380>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7583497c7f80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7647fa4e7bc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750d3151af30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fb2147c82c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x743311e46810>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fbb2babf170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7197b5092f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75b615822fc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x726bdd5bfe90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7071bbaa2c90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f588cb0ad50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x752fc25ffd10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7680ff05fd10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x771fc7c9ecc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d24b09e30e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x731de077ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ceb5396bef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78958f11e780>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75051bfc2db0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72c3af5fbc80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b4640a8af00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x765aeafffb60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a24d4a92f00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7828325faea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b67105e2c30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a4e1301f230>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c38f7c97320>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7aa5d2e87260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7988b18eaea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a8092327710>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79a7b8227fe0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7be6ff4aeb70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79e546a2ec00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f7320892900>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e9ee5f691c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73fd1ecf32f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x760faf4873b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x752cdb39f0b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e5662427050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7523d46bf230>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x755840c1e9c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ab9cefdfb90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d861d18fb30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x707fc6092f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x744b6ce36f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a83838a2cc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fa68d1ff530>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7068b5d070b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a063988bb30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71b8e77917c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c1e13706e10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d27301a6a80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75a38cd8bf80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b78dd3b7020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x735088b5e780>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7faa8b523e60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bd846b63e00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ea7f755a540>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x798293adfbf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79e8bca8b020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72535a08f200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f874b527c20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x722a517ef080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d76c861f020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ed13cce7da0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d9000a0bf80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72d26c165f10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d0a292ffef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7048ad5d3d10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x764d0301ed50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71f8d5c68200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76086e5a8110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x702be136bb30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77d8ac52aea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x709d7a419700>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d3d0e6bb260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b112822db0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x705cad836bd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ca36c58bdd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b2f67116ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ec0a36331a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b78ca3af60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71fc9148f0e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ac6ccb3ee40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a962b6b6e10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79ef1f3c7c50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fa08482f080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b96ecfb9e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x765ae7236ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77f2a9c230e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76cb5be8f170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76091422afc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x785ef3632e10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f795db172c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b40c6f6fe60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a8a8026fef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x727a5dd7ca10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74e6f0e05310>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c8b95dd7bc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x798c4e49ebd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bb07f4a70e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7af77612aff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x775fb17d3410>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b6b95ebae10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78d09c8eb6e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70c3a184b080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70fcf8832ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f09f271dfd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70a230f73110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7486f5ebfec0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e567238fb60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x710504bdf950>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d0c2c11f080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76d9c913acf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x783e098bf0b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7099361a8d40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b8c2823590>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7eadfe877170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74322b02ef30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x730eae0b2c30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x754f3348ad80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bacc507f080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ddf68a1afc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x798b70133380>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x791c2723afc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74632e2181d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7053e2722cc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a25579baa80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79bac1043470>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71e4af57b290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c68af0c2690>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75ade8827140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a2b765b6c60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x705c77ebeae0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70b89d402b70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x754b57096c60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x739c24046600>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75aa90d08740>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70987292eb70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72fbc30a2a80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x757e2f9a2cf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7faca542fc50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b2411b7ade0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76a2730284a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fb82bdbc320>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x719d18912f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77a1f0623e90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b3bda5a7350>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76b4139dbd70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f32a9826e70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70ca7a42ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7467ee767ce0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76fb1e82edb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75081f57b110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x704d2f2270e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c1b4ac2f140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x718e6c52e960>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x704270863d40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e6bcd29b110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7393189ea9f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a9372e53b30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7021aa22ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76cbf6786ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bd00c7270b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73d2b1e33650>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7afe21faad50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79896f7f69c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70f39468af30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75245c327440>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76a132886ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7015e784ec90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fd6a461ef30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x763819984050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7db19a8c2a50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c31959fe600>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b1e52d27050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7976c6a674d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x727545842de0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d0223c97bf0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ec812709d90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a8978e36d50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d0bbeb1ad20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72211da9eea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a3550f5ee40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78e89de27ec0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7312194f7bc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x752dcb633020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x779511db3350>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79f7960baf90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d97b42c6b70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fe7aeb36f60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77037a8beff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7138c635fdd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72deca68eba0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7de4d8967aa0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7162626a3230>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73015c31edb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dbe9a565b50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73824aa374a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73d0e6297110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76a58cc87a40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x787b85fe4350>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b0e629b83e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b1b20bbff80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73f94a49acc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77336c0a3110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7eac95f0af60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ef4c15a3050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c44f564be60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7df3f9a2ee70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7352cd896c30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7664ac72f920>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fc1848ffef0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70814f186d50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d2fe9d01fa0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x751a2d56eed0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x768a4d0f69c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d4641cb7140>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a3c4136ebd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b9a36055580>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7611da623020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x786708f9f620>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x723cff082e70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72719138efc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73d8a5b16de0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70173236ef30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a3eec59ae10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7690a0c970e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x730b3e52e180>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x726de2ddbf20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x708af3ca00e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cade8637050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75513b1af080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fbf6c958bc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cb5654d5910>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x741738416fc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73d2457728d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d5371887170>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77172d357c50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75ce5d227260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7779ad6b73e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72bf114ff8c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74b7497ffcb0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78ba5608f200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7034af4876b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x709e61cb6e10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x747950122ba0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x721044236ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x715c03fdea20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f16ab69ad50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7188e4fea810>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f877e88af30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71b51f3b2f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ce95f76b40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7248fe237020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x712cfb173b90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70134c3fef00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a907a11f050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7deea962bb60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79567bdef620>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7da73d222ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ebac39bac00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77d9ad44ef60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x716cd29c3d40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7720c551ed20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7943cf1a0200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78fc0fcebce0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fef4b496b70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70145ef1e3c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e8c11a43050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f3d83d90200>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ba6d3e8bfe0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79857ec739e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x725d39042810>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7567b94a7920>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fa3edb228a0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7170cca67b90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x717a45bb2f00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e5bdec0b290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f3b2569b320>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x735918deae40>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72ed999aae10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x762e8a21f050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d1fe87dfb90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74eb12c27080>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x772ca82c2bd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79e6a4e46de0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x703a6388e9f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dc15518a960>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dbe3bab2ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79f22a05a210>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dddd50c2f60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b0430f2eb10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70a5cf38ee10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71459852ebd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77948773b650>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x763d00952cc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e8211932de0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x715c7e15bc20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76edfa20b2f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ed2443e960>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73555372ede0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x749955255a30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7076397b2c30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78fd79712bd0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73506070ed80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c104a3370e0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7001632c30b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x795d9c11e240>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74c3926bef00>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76aa90732ea0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x734e1d961700>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x713e4df73ce0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e064f34f8c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d9c5bdb6f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72ffe9d831d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79460feaaf30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d2ab323b050>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77de7e53af60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70711311e9f0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c628182ad80>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ad469902b10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b240436fb60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73578e20f4d0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x760953717290>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70c8fc563e90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7002c67aede0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70c3d8276ed0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bc60f619310>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x759539122f30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x727383fbefc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b0d451929c0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79be1d43be30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78b13e103110>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a047c72ee10>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x721aed397230>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f07ba096e70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71ce1178ac60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a7361122f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bec7c512fc0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70c50d2bf0b0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7babcb7a6d20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79f9a7ca2e70>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x778053093fe0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x743f39296f90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76778351be30>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ec72ae9aff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f0b1a436a50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ab6419dfe60>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7db2adb2af90>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b6a4d30ff20>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71e3e2e23350>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cde3625f260>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
