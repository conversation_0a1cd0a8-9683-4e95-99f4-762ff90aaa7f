import glob
import json
import re

import numpy as np
import pandas as pd
from pymongo import MongoClient
from datetime import datetime
from bson.objectid import ObjectId

# Config
MONGO_URI = "mongodb://192.168.100.7:27017"
DB_NAME = "hyperopt_db"
COLLECTION_NAME = "jobs"
MODE = "insert"  # or "insert"
EXP_KEY = "exp1"

PATH = "/home/<USER>/dev/ta/kaffa_v2/tuning/parallel/fix"
all_pattern = glob.glob(f'{PATH}/*.csv')

# Load dữ liệu từ file JSON export
with open("/home/<USER>/dev/ta/kaffa_v2/tuning/parallel/hyperopt_db.jobs.json", "r") as f:
    sample = json.load(f)[0]

client = MongoClient(MONGO_URI)
db = client[DB_NAME]
collection = db[COLLECTION_NAME]

# Lấy tid lớn nhất hiện có để gán tiếp nếu insert
current_max_tid = collection.find_one(
    sort=[("tid", -1)]
)
base_tid = (current_max_tid["tid"] + 1) if current_max_tid else 0


# Fill log vào sample (giả sử từng log ứng với từng sample)
for pattern in all_pattern:
    data = pd.read_csv(pattern)
    exp_key = pattern.split("/")[-1].split(".")[0]

    key_weights = [key for key in data.columns if (key.startswith("w_") or re.fullmatch(r"w[0-9]+", key))]
    for i, ite in enumerate(data.to_dict(orient="records")):
        # Ensure 'result' key exists
        try:
            sample["result"]["loss"] = -float(ite['loss']) if not np.isinf(ite['loss']) else ite['loss']
        except:
            sample["result"]["loss"] = 999999.0  # fallback loss

        time = datetime.utcnow()
        sample["book_time"] = time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

        # You need to define logs or remove this block if not needed
        # if i < len(logs):
        #     log_data = logs[i]
        #     for k, v in log_data.items():
        #         if k not in doc:
        #             doc[k] = v

        sample.pop("_id", None)
        sample["tid"] = base_tid + i
        sample["exp_key"] = exp_key

        if "misc" in sample and "vals" in sample["misc"]:
            sample["misc"]["tid"] = sample["tid"]
            idxs = {}
            vals = {}
            for key in key_weights:
                idxs[key] = [sample["tid"]]
                vals[key] = [ite[key]]

            sample["misc"]["idxs"] = idxs
            sample["misc"]["vals"] = vals

        time = datetime.utcnow()
        sample["refresh_time"] = time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

        collection.insert_one(sample)

print("✅ Done processing trials.")
