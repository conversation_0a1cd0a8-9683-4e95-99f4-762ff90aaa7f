import os
import glob
import pandas as pd
import numpy as np


def find_csv_files(root_dir, output_dir):
    all_files = glob.glob(os.path.join(root_dir, '**', '*.csv'))
    return [file for file in all_files if not file.startswith(output_dir)]


def group_csv_files(csv_files):
    grouped_files = {}
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        if file_name not in grouped_files:
            grouped_files[file_name] = []
        grouped_files[file_name].append(file_path)
    return grouped_files


def merge_csv_files(file_paths, output_path):
    dfs = [pd.read_csv(file_path) for file_path in file_paths]
    merged_df = pd.concat(dfs, ignore_index=True)
    merged_df = merged_df[merged_df['loss'] != np.inf]
    merged_df = merged_df.sort_values(by='loss', ascending=False)
    merged_df.to_csv(output_path, index=False)


def process_and_merge_csv_files(root_dir, output_dir):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    csv_files = find_csv_files(root_dir, output_dir)
    grouped_files = group_csv_files(csv_files)

    for file_name, file_paths in grouped_files.items():
        output_path = os.path.join(output_dir, file_name)
        if len(file_paths) > 1:
            dfs = []
            for file_path in file_paths:
                prefix_data = file_path.split('/')[-2]

                read_df = pd.read_csv(file_path)
                read_df['VPS'] = prefix_data
                dfs.append(read_df)
            merged_df = pd.concat(dfs, ignore_index=True)
        else:
            prefix_data = file_paths[0].split('/')[-2]

            merged_df = pd.read_csv(file_paths[0])
            merged_df['VPS'] = prefix_data


        merged_df = merged_df[merged_df['loss'] != np.inf]
        merged_df = merged_df.sort_values(by='loss', ascending=False)
        merged_df.to_csv(output_path, index=False)
    print(f"Đã xử lý và merge các file CSV. Kết quả được lưu trong {output_dir}")


# Cách sử dụng:
process_and_merge_csv_files('/home/<USER>/dev/ta/kaffa_v2/tuning/parallel',
                            '/home/<USER>/dev/ta/kaffa_v2/tuning/parallel/final_result')
