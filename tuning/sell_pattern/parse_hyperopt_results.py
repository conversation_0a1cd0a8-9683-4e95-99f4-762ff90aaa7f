import glob
import json
import os
import sys
from typing import Dict, List, Any

import pandas as pd

# Đường dẫn và biến toàn cục
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning/sell_pattern", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# Đ<PERSON>nh nghĩa các pattern và path tương ứng
PATH = "tuning/parallel/final_result/sell"
RESULT_PATH = "tuning/results"
INIT_ASSETS = 50.0
INIT_SLOTS = 25
SI_TYPE = "Cash allocation"

KEY_MAPPING = {
    'w_ma21': 'MA21',
    'w_ma31': 'MA31',
    'w_ma41': 'MA41',
    'w_s13': 'S13',
    'w_selllowgrowth': 'SellLowGrowth',
    'w_sellresistance1y': 'SellResistance1Y',
    'w_sellresistance1m': 'SellResistance1M',
    'w_sellresistance': 'SellResistance',
    'w_sellbv': 'SellBV',
    'w_sellbv2': 'SellBV2',
    'w_sellpe': 'SellPE',
    'w_sellvolmax': 'SellVolMax',
    'w_beardvg2': 'BearDvg2'
}

INIT_FILTER = {
    "Init": "(Volume*Price/Inflation_7>10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
    "_PS1": "Price < 0",

}

PATTERN_CONFIGS = {
    'MA21': {
        'checkpoint_path': f'{PATH}/trials_MA21.pkl',
        'log_path': f'{PATH}/trials_MA21.csv',
        'pattern_template': "(MA20/MA50<{w1}) & (MA20_T1/MA50_T1>{w2}) & (D_RSI/D_RSI_T1W < {w3}) & (Close < {w4}*VAP1M) & (D_MACDdiff< {w5}) & (Close/Close_T1W < {w6})"
    },
    'MA31': {
        'checkpoint_path': f'{PATH}/trials_MA31.pkl',
        'log_path': f'{PATH}/trials_MA31.csv',
        'pattern_template': "(MA10/MA200<{w1}) & (MA10_T1/MA200_T1>{w2}) & (Close < {w3}*VAP3M) & (Close/Close_T1W < {w4})& (D_RSI/D_RSI_T1W < {w5}) & (D_RSI < {w6}) & (D_MACDdiff< {w7})& (NP_P0/NP_P1 < {w8}) & (Volume>{w9}*Volume_3M_P50)"
    },
    'MA41': {
        'checkpoint_path': f'{PATH}/trials_MA41.pkl',
        'log_path': f'{PATH}/trials_MA41.csv',
        'pattern_template': "(Close > {w1}*MA200) & (NP_P0/NP_P1 < {w2}) & (Volume>{w3}*Volume_3M_P50) & (Close/Close_T1W < {w4}) & (Close < {w5}*VAP1M)"
    },
    'S13': {
        'checkpoint_path': f'{PATH}/trials_S13.pkl',
        'log_path': f'{PATH}/trials_S13.csv',
        'pattern_template': "(C_L1W>={w1}) & (D_CMB_Peak_T1>{w2}*D_CMB) & (Close>{w3}*MA10) & (D_CMB_XFast<{w4})"
    },
    'SellBV': {
        'checkpoint_path': f'{PATH}/trials_SellBV.pkl',
        'log_path': f'{PATH}/trials_SellBV.csv',
        'pattern_template': "(Close > {w1}*BVPS) & (NP_P0 /NP_P1 < {w2}) & (Close < {w3}*VAP1M) & (Close_T1W > {w4}*VAP1M) & (Volume > {w5}* Volume_3M_P50) & (ICB_Code != 8633)"
    },
    'SellBV2': {
        'checkpoint_path': f'{PATH}/trials_SellBV2.pkl',
        'log_path': f'{PATH}/trials_SellBV2.csv',
        'pattern_template': "(PB > {w1}*PB_MA5Y + {w2}*PB_SD5Y) & (NP_P0 /NP_P1 < {w3}) & (Close < {w4}*VAP1M) & (Close_T1W > {w5}*VAP1M) & (D_RSI > {w6}) & (Volume > {w7}*Volume_3M_P50)"
    },
    'SellPE': {
        'checkpoint_path': f'{PATH}/trials_SellPE.pkl',
        'log_path': f'{PATH}/trials_SellPE.csv',
        'pattern_template': "(PE >= {w1}*PE_MA5Y  + {w2}*PE_SD5Y) & (NP_P0 /NP_P1 < {w3}) & (Close < {w4}*VAP3M) & (Close_T1W > {w5}*VAP3M) & (Close/Close_T1W < {w6}) & (Volume > {w7}*Volume_3M_P50)"
    },
    'SellResistance': {
        'checkpoint_path': f'{PATH}/trials_SellResistance.pkl',
        'log_path': f'{PATH}/trials_SellResistance.csv',
        'pattern_template': "(Open/Close< {w1}) & (Close  <  {w2}*Res_1Y) & (Close/LO_3M_T1 > {w3}) & (Volume > {w4}*Volume_3M_P50)"
    },
    'SellResistance1M': {
        'checkpoint_path': f'{PATH}/trials_SellResistance1M.pkl',
        'log_path': f'{PATH}/trials_SellResistance1M.csv',
        'pattern_template': "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= {w1}) & (Close < {w2}*VAP1M) & (Close_T1 >  {w3}*VAP1M) & (Volume > {w4}* Volume_3M_P50)& (D_RSI > {w5})"
    },
    'SellResistance1Y': {
        'checkpoint_path': f'{PATH}/trials_SellResistance1Y.pkl',
        'log_path': f'{PATH}/trials_SellResistance1Y.csv',
        'pattern_template': "(PB > {w1}*PB_MA5Y + {w2}*PB_SD5Y) & (NP_P0 /NP_P1 < {w3}) & (Close < {w4}*Res_1Y) & (Volume  > {w5}*Volume_3M_P50) & (Close_T1W > {w6}*VAP1M) & (D_RSI > {w7})"
    },
    'BearDvg2': {
        'checkpoint_path': f'{PATH}/trials_BearDvg2.pkl',
        'log_path': f'{PATH}/trials_BearDvg2.csv',
        'pattern_template': "(D_RSI_Max1W/D_RSI > {w1}) & (D_RSI_Max3M > {w2}) & (D_RSI_Max1W < {w3}) & (D_RSI_Max1W >{w4}) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > {w5}) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > {w6}) & (D_RSI_T1/D_RSI > {w7}) & (Volume > {w8}*Volume_1M)"
    },
    'SellVolMax': {
        'checkpoint_path': f'{PATH}/trials_SellVolMax.pkl',
        'log_path': f'{PATH}/trials_SellVolMax.csv',
        'pattern_template': "(Close/Volume_MaxTop5_2Y_Close < {w1}) & (ID_Current - Volume_MaxTop5_2Y_ID <={w2}) & (Close < {w3}*VAP1W) & (D_RSI > {w4}) & (Close/Close_T1 < {w5}) & (D_RSI/D_RSI_T1W < {w6}) & (Close_T1/LO_3M_T1 > {w7})"
    },
    'BearDvgVNI': {
        'checkpoint_path': f'{PATH}/trials_BearDvgVNI.pkl',
        'log_path': f'{PATH}/trials_BearDvgVNI.csv',
        'pattern_template': "(time>='2000-01-01') & (time<='2025-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > {w1})  & (D_RSI_Max3M > {w2}) & (D_RSI_Max1W < {w3}) & (D_RSI_Max1W>{w4}) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > {w5}) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>{w6}) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > {w7}) & (D_RSI_MinT3 > {w8}) & (D_CMF < {w9})"
    }
}


def parse_logging_results(pattern_name: str, log_path: str, top_n: int = 50) -> List[Dict[str, Any]]:
    if pattern_name not in PATTERN_CONFIGS:
        raise ValueError(f"Pattern {pattern_name} không tồn tại trong cấu hình")

    config = PATTERN_CONFIGS[pattern_name]
    # log_path = config['log_path']
    pattern_template = config['pattern_template']

    if not os.path.exists(log_path):
        raise FileNotFoundError(f"Không tìm thấy file log tại {log_path}")

    # Đọc dữ liệu từ file log
    data = pd.read_csv(log_path)
    data = data[data['loss'] != float('inf')]

    # # Tính toán ranking
    # data['ranking'] = 0.4 * data['si_return'] + \
    #                   0.3 * data['win_deal'] + \
    #                   0.15 * data['win_quarter'] + \
    #                   0.1 * data['winblock_20quarters'] + \
    #                   0.05 * data['winblock_24months']
    #                   # 0.1 * data['deal'] / data['deal'].max() * 100

    # Lấy top_n kết quả tốt nhất
    top_data = data.nlargest(top_n, 'loss').copy()
    # Tạo cột filter
    top_data['filter'] = top_data.apply(lambda row: pattern_template.format(**row.to_dict()), axis=1)

    # Sắp xếp theo ranking
    # top_data.sort_values(by='ranking', ascending=False, inplace=True)

    # Chuyển đổi DataFrame thành list of dictionaries
    results = top_data.to_dict('records')

    # Thêm thông tin về pattern_name và rank
    for i, result in enumerate(results):
        result['pattern_name'] = pattern_name
        result['rank'] = i + 1

    return results


def save_results_to_final_profile(results):
    filters = INIT_FILTER.copy()

    for result in results:
        filters[f'~{result["pattern"]}'] = "{Init} & " + result['filter']

    new_filter = {
        "filter": json.dumps(filters),
        "weight": '{}',
        "params": {
            'cutloss': 100,
            'si_type': SI_TYPE,
            'si_slot': INIT_SLOTS,
            'si_asset': INIT_ASSETS,
            'w_lookback': 10,
            'w_thres_buy': 1.0,
            'w_thres_sell': -1.0,
            'w_k_exp': 0.0,
            'co_rank': "ranking_point",

        },
        "combine": {}
    }

    output_path = f'{RESULT_PATH}/config_results.json'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    df_r = pd.DataFrame(results)
    df_r.to_csv(f'{RESULT_PATH}/df_sell_picked.csv', index=False)
    if os.path.exists(output_path):
        with open(output_path, 'r') as f:
            existing_data = json.load(f)
        existing_data['sell_selected'] = new_filter
        with open(output_path, 'w') as f:
            json.dump(existing_data, f, indent=4)
    else:
        with open(output_path, 'w') as f:
            json.dump({'sell_selected': new_filter}, f, indent=4)


def create_synthetic_profile_from_log(all_patterns_csv_path):
    results = []
    for path in all_patterns_csv_path:
        pattern_name = path.split("/")[-1].split("_")[2].split(".")[0]
        print(pattern_name)
        result = parse_logging_results(pattern_name, path)
        results.append(result[0])

    save_results_to_final_profile(results)


if __name__ == "__main__":
    results = []
    all_pattern = glob.glob(f'{PATH}/*.csv')
    create_synthetic_profile_from_log(all_pattern)
