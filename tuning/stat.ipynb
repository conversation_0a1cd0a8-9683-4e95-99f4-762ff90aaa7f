#%%
import os
import re
import sys
import warnings
from datetime import datetime
from datetime import timedelta
from pathos.multiprocessing import ProcessingPool as Pool
import pandas as pd
from joblib import Memory
import numpy as np
import os

from requests import session

current_dir = os.getcwd()
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)
FPATH = 'ticker_v1a/'

import warnings

warnings.filterwarnings('ignore')

# Tắt thêm warning nội bộ của IPython (nếu có)
from IPython.display import display
import logging

logging.getLogger('py.warnings').setLevel(logging.ERROR)
#%%
# def eval(pdxx, p_col='P3M', value=20, com='greater'):
#     ticker = 'VNINDEX'
#     pdxx['s_time'] = pd.to_datetime(pdxx['time'])
#     pdxx['week'] = pdxx['s_time'].dt.strftime('%Y-%W')
#     pdxx['biweek'] = pdxx['s_time'].dt.to_period('2W').astype(str)
#     pdxx['month'] = pdxx['s_time'].dt.strftime('%Y-%m')
#
#     pdxx['TD_MA1M'] = pdxx['Trading_Session'].rolling(20).mean()
#     pdxx['TD_MA1M_center'] = pdxx['Trading_Session'].rolling(20, center=True).mean()
#     pdxx['ratio_3M_TD_MA1M'] = pdxx['TD_MA1M'].shift(-60) / (pdxx['TD_MA1M_center'])
#
#     pdxx['PE/PE_MA5Y'] = pdxx['VNINDEX_PE'] / pdxx['VNINDEX_PE_MA5Y']
#     pdxx['ratio_3M_PE_PE_MA5Y'] = pdxx['PE/PE_MA5Y'].shift(-60) / (pdxx['PE/PE_MA5Y'])
#
#     pdxx['ratio_sup1y'] = pdxx['Close'] / (pdxx['Sup_1Y'])
#
#     try:
#         if com == 'greater':
#             pd_all = pdxx.query(f"{p_col} >= {value}")  # chuỗi dương
#         else:
#             pd_all = pdxx.query(f"{p_col} <= {value}")  # chuỗi âm
#
#         """
#         A: data frame have P6M <= -20
#         A deduplicate
#         group all hit in same period find group_start_time and group_end_time
#
#         Result:
#         If there is no buy hit in same period so that need to consider
#
#         """
#
#         # pd_all = pd_all.drop_duplicates(subset=['month'], keep='first')
#
#         pd_all['month'] = pd.to_datetime(pd_all['month'])
#         pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)
#
#         # Group consecutive months (month_diff <= 1)
#         pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
#         pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('first')
#         pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('last') + pd.Timedelta(days=3 * 30)
#         pd_all['mean_profit'] = pd_all.groupby('group')[p_col].transform('mean')
#         pd_all['max_profit'] = pd_all.groupby('group')[p_col].transform('max')
#         pd_all['min_profit'] = pd_all.groupby('group')[p_col].transform('min')
#         pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#
#         pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
#         # Group consecutive group (group_diff <= 1)
#         pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
#         pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('first')
#         pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('last')
#         pd_all['mean_profit'] = pd_all.groupby('group')['mean_profit'].transform('mean')
#         pd_all['max_profit'] = pd_all.groupby('group')['max_profit'].transform('max')
#         pd_all['min_profit'] = pd_all.groupby('group')['min_profit'].transform('min')
#
#         pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
#         pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
#         pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#
#         # path = 'tuning/results/'
#         # res_month = pd.DataFrame(res_month)
#         # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
#
#         return pd_all[
#             ['start_group', 'end_group', 'start_group_q', 'end_group_q', 'mean_profit', 'max_profit', 'min_profit',
#              'ratio_3M_TD_MA1M', 'ratio_3M_PE_PE_MA5Y', 'ratio_sup1y']]
#     except Exception as error:
#         print(f"Error: {ticker}: {error}")
#         pass
#
#
# pd_period = eval(df_vnindex, 'P3M', 22.65, com='greater')
# # pd_period = eval(df_vnindex, 'P3M', -14.3, com='less')
#%%
# def eval_MA(pdxx, p_col='MA20/MA200', value=20, com='greater'):
#     ticker = 'VNINDEX'
#     try:
#         if com == 'greater':
#             pd_all = pdxx.query(f"{p_col} >= {value}")  # chuỗi dương
#         else:
#             pd_all = pdxx.query(f"{p_col} <= {value}")  # chuỗi âm
#
#         pd_all['s_time'] = pd.to_datetime(pd_all['time'])
#         pd_all['week'] = pd_all['s_time'].dt.strftime('%Y-%W')
#         pd_all['biweek'] = pd_all['s_time'].dt.to_period('2W').astype(str)
#         pd_all['month'] = pd_all['s_time'].dt.strftime('%Y-%m')
#         pd_all['day'] = pd_all.index
#
#         pd_all['TD_MA1M'] = pd_all['Trading_Session'].rolling(20).mean()
#         pd_all['TD_MA1M_center'] = pd_all['Trading_Session'].rolling(20, center=True).mean()
#         pd_all['ratio_3M_TD_MA1M'] = pd_all['TD_MA1M'].shift(-60) / (pd_all['TD_MA1M_center'])
#
#         pd_all['PE/PE_MA5Y'] = pd_all['VNINDEX_PE'] / pd_all['VNINDEX_PE_MA5Y']
#         pd_all['ratio_3M_PE_PE_MA5Y'] = pd_all['PE/PE_MA5Y'].shift(-60) / (pd_all['PE/PE_MA5Y'])
#
#         pd_all['ratio_sup1y'] = pd_all['Close'] / (pd_all['Sup_1Y'])
#         """
#         A: data frame have P6M <= -20
#         A deduplicate
#         group all hit in same period find group_start_time and group_end_time
#
#         Result:
#         If there is no buy hit in same period so that need to consider
#
#         """
#
#         # pd_all = pd_all.drop_duplicates(subset=['month'], keep='first')
#
#         pd_all['month'] = pd.to_datetime(pd_all['month'])
#         pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)
#         pd_all['day_diff'] = round(pd_all['day'].diff()).fillna(0)
#         pd_all['group'] = (pd_all['day_diff'] > 1).cumsum()
#
#         # Group consecutive months (month_diff <= 1)
#         # pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
#         pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('first')
#         pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('last')
#         pd_all['mean_MA20/MA200'] = pd_all.groupby('group')[p_col].transform('mean')
#         pd_all['max_MA20/MA200'] = pd_all.groupby('group')[p_col].transform('max')
#         pd_all['min_MA20/MA200'] = pd_all.groupby('group')[p_col].transform('min')
#
#         # pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#         # pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
#         # # Group consecutive group (group_diff <= 1)
#         # pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
#         # pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('first')
#         # pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('last')
#         # pd_all['mean_profit'] = pd_all.groupby('group')['mean_profit'].transform('mean')
#         # pd_all['max_profit'] = pd_all.groupby('group')['max_profit'].transform('max')
#         # pd_all['min_profit'] = pd_all.groupby('group')['min_profit'].transform('min')
#
#         pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
#         pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
#         pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#
#         # path = 'tuning/results/'
#         # res_month = pd.DataFrame(res_month)
#         # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
#
#         return pd_all[
#             ['start_group', 'end_group', 'start_group_q', 'end_group_q', 'mean_MA20/MA200', 'max_MA20/MA200',
#              'min_MA20/MA200', 'ratio_3M_TD_MA1M', 'ratio_3M_PE_PE_MA5Y', 'ratio_sup1y']]
#     except Exception as error:
#         print(f"Error: {ticker}: {error}")
#         pass
#
#
# pd_period = eval_MA(df_vnindex, 'MA20_MA200', 11.15, com='greater')
# pd_period_p = eval(df_vnindex, 'P3M', 22.65, com='greater')
#%%
def multi_process(list_processed_ticker):
    def func(ticker):
        try:
            df_ticker = pd.read_csv(f'{FPATH}/{ticker}.csv',
                                    usecols=['time', 'ticker', 'Close', 'Volume', 'O1M', 'O3M', 'O6M', 'O1Y', 'Open_1D',
                                             'Risk_Rating', 'OShares', 'Price'])
            df_ticker['P1Y'] = 100 * (df_ticker['O1Y'] - 1)
            df_ticker['P9M'] = 100 * ((df_ticker['Open_1D'].shift(-20 * 9) / df_ticker['Open_1D']) - 1)
            df_ticker['P6M'] = 100 * (df_ticker['O6M'] - 1)
            df_ticker['P3M'] = 100 * (df_ticker['O3M'] - 1)
            df_ticker['P1M'] = 100 * (df_ticker['O1M'] - 1)
            df_ticker['m_cap'] = df_ticker['OShares'] * df_ticker['Price']
            df_ticker['Close3M'] = df_ticker['Close'].shift(-60)

            return df_ticker[
                ['time', 'ticker', 'm_cap', 'Risk_Rating', 'P1M', 'P3M', 'P6M', 'P9M', 'P1Y', 'OShares', 'Price',
                 'Close', 'Close3M']]
        except:
            return None

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(func, list_processed_ticker)

    lres = [res for res in lres if res is not None and not res.empty]
    pd_all = pd.concat(lres, axis=0).reset_index(drop=True)

    return pd_all
#%%
FPATH = 'ticker_v1a'
df_vnindex = pd.read_csv(f'{FPATH}/VNINDEX.csv')

# percentile P6M, P3M of vnindex
# pd00['Open'].shift(-n - delay) / buyPrice
df_vnindex['P1Y'] = 100 * (df_vnindex['O1Y'] - 1)
df_vnindex['P9M'] = 100 * ((df_vnindex['Open_1D'].shift(-20 * 9) / df_vnindex['Open_1D']) - 1)
df_vnindex['P6M'] = 100 * (df_vnindex['O6M'] - 1)
df_vnindex['P3M'] = 100 * (df_vnindex['O3M'] - 1)
df_vnindex['P1M'] = 100 * (df_vnindex['O1M'] - 1)
df_vnindex['MA20_MA200'] = (df_vnindex['MA20'] / df_vnindex['MA200'] - 1) * 100

percentiles = [0.05, 0.1, 0.2, 0.5, 0.7, 0.8, 0.85, 0.9, 0.95]

dict_percentiles = {
    'P1Y': df_vnindex['P1Y'].quantile(percentiles).values,
    'P9M': df_vnindex['P9M'].quantile(percentiles).values,
    'P6M': df_vnindex['P6M'].quantile(percentiles).values,
    'P3M': df_vnindex['P3M'].quantile(percentiles).values,
    'P1M': df_vnindex['P1M'].quantile(percentiles).values,
    'VNINDEX_PE': df_vnindex['VNINDEX_PE'].quantile(percentiles).values,
    'PE_PE_MA5Y': (df_vnindex['VNINDEX_PE'] / df_vnindex['VNINDEX_PE_MA5Y']).quantile(percentiles).values,
    'MA20_MA200': df_vnindex['MA20_MA200'].quantile(percentiles).values,
}

for i in range(60, 245, 5):
    df_vnindex[f'P{i}'] = 100 * ((df_vnindex['Open_1D'].shift(-i) / df_vnindex['Open_1D']) - 1)
    dict_percentiles[f'P{i}'] = df_vnindex[f'P{i}'].quantile(percentiles).values

df_percentiles = pd.DataFrame(dict_percentiles, index=[f'{int(p * 100)}%' for p in percentiles])

print(df_percentiles)

#%%
def eval(pdxx, df_percentiles, p_col='P3M', value=20, com='greater'):
    ticker = 'VNINDEX'
    pdxx['s_time'] = pd.to_datetime(pdxx['time'])
    pdxx['week'] = pdxx['s_time'].dt.strftime('%Y-%W')
    pdxx['biweek'] = pdxx['s_time'].dt.to_period('2W').astype(str)
    pdxx['month'] = pdxx['s_time'].dt.strftime('%Y-%m')

    pdxx['TD_MA1M'] = pdxx['Trading_Session'].rolling(20, min_periods=5).mean()
    pdxx['TD_MA1M_center'] = pdxx['Trading_Session'].rolling(20, center=True, min_periods=5).mean()
    pdxx['ratio_3M_TD_MA1M'] = pdxx['TD_MA1M'].shift(-60) / (pdxx['TD_MA1M_center'])

    pdxx['PE/PE_MA5Y'] = pdxx['VNINDEX_PE'] / pdxx['VNINDEX_PE_MA5Y']

    pdxx['ratio_3M_PE_PE_MA5Y'] = pdxx['PE/PE_MA5Y'].shift(-60) / (pdxx['PE/PE_MA5Y'])

    pdxx['ratio_sup1y'] = pdxx['Close'] / (pdxx['Sup_1Y'])

    try:
        if com == 'greater':
            pd_all = pdxx.query(f"{p_col} >= {value}")  # chuỗi dương
        else:
            pd_all = pdxx.query(f"{p_col} <= {value}")  # chuỗi âm

        """
        A: data frame have P6M <= -20
        A deduplicate
        group all hit in same period find group_start_time and group_end_time

        Result:
        If there is no buy hit in same period so that need to consider

        """
        pd_all['start_group'] = pd_all['s_time']
        pd_all['end_group'] = pd_all['s_time']

        for i_group in range(pd_all.shape[0]):
            row_a = pd_all.iloc[i_group]
            start_group = row_a['start_group']
            start_index = df_vnindex[df_vnindex['time'] == start_group.strftime('%Y-%m-%d')].index[0]
            # if start_group < end_group:
            #     continue
            session_break = None
            break_count = 0
            for per in df_percentiles.columns:
                session = per.split('P')[1]
                try:
                    if (df_vnindex.iloc[start_index + int(session)]['Open_1D'] / df_vnindex.iloc[start_index][
                        'Open_1D'] - 1) * 100 <= df_percentiles.loc['90%', per]:
                        break_count += 1
                    else:
                        break_count = 0

                    if break_count == 8:
                        break

                except Exception as error:
                    # print(f"Error: {error}")
                    # print(f"Error: {start_group}")
                    # print(f"Error: {session}")
                    break
            try:
                offset_dates = df_vnindex.iloc[start_index + int(session)]['time']
            except:
                offset_dates = df_vnindex.iloc[-1]['time']

            # session = session_break if session_break is not None else session
            # pd_all.at[pd_all.index[i_group], 'end_group'] = start_group + pd.Timedelta(
            #     days=int((int(session) - 5) / 20 * 31))
            pd_all.at[pd_all.index[i_group], 'end_group'] = offset_dates
            pd_all.at[pd_all.index[i_group], 'profit'] = (df_vnindex.loc[
                                                              df_vnindex['time'] == offset_dates, 'Close'].values[0] -
                                                          row_a['Close']) / row_a['Close']

        pd_all['month'] = pd.to_datetime(pd_all['month'])
        pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)

        # Group consecutive months (month_diff <= 1)
        # pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
        # pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
        # pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
        # pd_all['C_H2Y'] = pd_all.groupby('group')['C_H2Y'].transform('min')

        # pd_all['mean_profit'] = pd_all.groupby('group')['profit'].transform('mean')
        # pd_all['max_profit'] = pd_all.groupby('group')['profit'].transform('max')
        # pd_all['min_profit'] = pd_all.groupby('group')['profit'].transform('min')

        # pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

        # pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
        # # # Group consecutive group (group_diff <= 1)
        # pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
        # pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
        # pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
        # pd_all['C_H2Y'] = pd_all.groupby('group')['C_H2Y'].transform('min')

        # pd_all['mean_profit'] = pd_all.groupby('group')['mean_profit'].transform('mean')
        # pd_all['max_profit'] = pd_all.groupby('group')['max_profit'].transform('max')
        # pd_all['min_profit'] = pd_all.groupby('group')['min_profit'].transform('min')
        # #
        pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
        pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
        # pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
        pd_all = pd_all.reset_index(drop=True)
        # path = 'tuning/results/'
        # res_month = pd.DataFrame(res_month)
        # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
        # return pd_all[['start_group', 'end_group']]

        return pd_all[
            ['start_group', 'end_group', 'start_group_q', 'end_group_q',
             'ratio_3M_TD_MA1M', 'ratio_3M_PE_PE_MA5Y', 'ratio_sup1y', 'C_H2Y', 'profit']]
    except Exception as error:
        print(f"Error: {ticker}: {error}")
        pass

#%%

#%%
# def eval(pdxx, df_percentiles, p_col='P3M', value=20, com='greater'):
#     ticker = 'VNINDEX'
#     pdxx['s_time'] = pd.to_datetime(pdxx['time'])
#     pdxx['week'] = pdxx['s_time'].dt.strftime('%Y-%W')
#     pdxx['biweek'] = pdxx['s_time'].dt.to_period('2W').astype(str)
#     pdxx['month'] = pdxx['s_time'].dt.strftime('%Y-%m')
#
#     pdxx['TD_MA1M'] = pdxx['Trading_Session'].rolling(20, min_periods=5).mean()
#     pdxx['TD_MA1M_center'] = pdxx['Trading_Session'].rolling(20, center=True, min_periods=5).mean()
#     pdxx['ratio_3M_TD_MA1M'] = pdxx['TD_MA1M'].shift(-60) / (pdxx['TD_MA1M_center'])
#
#     pdxx['PE/PE_MA5Y'] = pdxx['VNINDEX_PE'] / pdxx['VNINDEX_PE_MA5Y']
#
#     pdxx['ratio_3M_PE_PE_MA5Y'] = pdxx['PE/PE_MA5Y'].shift(-60) / (pdxx['PE/PE_MA5Y'])
#
#     pdxx['ratio_sup1y'] = pdxx['Close'] / (pdxx['Sup_1Y'])
#
#     try:
#         if com == 'greater':
#             pd_all = pdxx.query(f"{p_col} >= {value}")  # chuỗi dương
#         else:
#             pd_all = pdxx.query(f"{p_col} <= {value}")  # chuỗi âm
#
#         """
#         A: data frame have P6M <= -20
#         A deduplicate
#         group all hit in same period find group_start_time and group_end_time
#
#         Result:
#         If there is no buy hit in same period so that need to consider
#
#         """
#         pd_all['start_group'] = pd_all['s_time']
#         pd_all['end_group'] = pd_all['s_time']
#         pd_all['close_group'] = pd_all['Close']
#
#         for i_group in range(pd_all.shape[0]):
#             row_a = pd_all.iloc[i_group]
#             start_group = row_a['start_group']
#             start_index = df_vnindex[df_vnindex['time'] == start_group.strftime('%Y-%m-%d')].index[0]
#             # if start_group < end_group:
#             #     continue
#             session_break = None
#             break_count = 0
#             for per in df_percentiles.columns:
#                 session = per.split('P')[1]
#                 try:
#                     if (df_vnindex.iloc[start_index + int(session)]['Open_1D'] / df_vnindex.iloc[start_index][
#                         'Open_1D'] - 1) * 100 <= df_percentiles.loc['90%', per]:
#                         break_count += 1
#                     else:
#                         break_count = 0
#
#                     if break_count == 8:
#                         break
#
#                 except Exception as error:
#                     print(f"Error: {error}")
#                     print(f"Error: {start_group}")
#                     print(f"Error: {session}")
#                     break
#             try:
#                 offset_dates = df_vnindex.iloc[start_index + int(session)]['time']
#             except:
#                 offset_dates = df_vnindex.iloc[-1]['time']
#
#             # session = session_break if session_break is not None else session
#             # pd_all.at[pd_all.index[i_group], 'end_group'] = start_group + pd.Timedelta(
#             #     days=int((int(session) - 5) / 20 * 31))
#             pd_all.at[pd_all.index[i_group], 'end_group'] = offset_dates
#             pd_all.at[pd_all.index[i_group], 'profit'] = (df_vnindex.loc[df_vnindex['time'] == offset_dates, 'Close'].values[0] - row_a['Close']) / row_a['Close']
#
#         pd_all['month'] = pd.to_datetime(pd_all['month'])
#         pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)
#
#         # Group consecutive months (month_diff <= 1)
#         pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
#         pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
#         pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
#         pd_all['C_H2Y'] = pd_all.groupby('group')['C_H2Y'].transform('min')
#
#         pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#
#         pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
#         # # Group consecutive group (group_diff <= 1)
#         pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
#         pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
#         pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
#         pd_all['C_H2Y'] = pd_all.groupby('group')['C_H2Y'].transform('min')
#
#         # pd_all['mean_profit'] = pd_all.groupby('group')['mean_profit'].transform('mean')
#         # pd_all['max_profit'] = pd_all.groupby('group')['max_profit'].transform('max')
#         # pd_all['min_profit'] = pd_all.groupby('group')['min_profit'].transform('min')
#         #
#         pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
#         pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
#         pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
#         pd_all = pd_all.reset_index(drop=True)
#         # path = 'tuning/results/'
#         # res_month = pd.DataFrame(res_month)
#         # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
#         # return pd_all[['start_group', 'end_group']]
#
#         return pd_all[
#             ['start_group', 'end_group', 'start_group_q', 'end_group_q',
#              'ratio_3M_TD_MA1M', 'ratio_3M_PE_PE_MA5Y', 'ratio_sup1y', 'C_H2Y']]
#     except Exception as error:
#         print(f"Error: {ticker}: {error}")
#         pass
#
#
# pd_period = eval(df_vnindex, df_percentiles, 'P3M', 22.65, com='greater')
# # pd_period = eval(df_vnindex, 'P3M', -14.3, com='less')
#%%
import traceback


def eval(df_vni, df_percentiles, p_col='P3M', value=20, com='greater'):
    ticker = 'VNINDEX'
    df_vni = df_vni.copy()
    df_vni['s_time'] = pd.to_datetime(df_vni['time'])
    df_vni['week'] = df_vni['s_time'].dt.strftime('%Y-%W')
    df_vni['biweek'] = df_vni['s_time'].dt.to_period('2W').astype(str)
    df_vni['month'] = df_vni['s_time'].dt.strftime('%Y-%m')

    df_vni['TD_MA1M'] = df_vni['Trading_Session'].rolling(20, min_periods=5).mean()
    df_vni['TD_MA1M_center'] = df_vni['Trading_Session'].rolling(20, center=True, min_periods=5).mean()
    df_vni['ratio_3M_TD_MA1M'] = df_vni['TD_MA1M'].shift(-60) / (df_vni['TD_MA1M_center'])

    df_vni['PE/PE_MA5Y'] = df_vni['VNINDEX_PE'] / df_vni['VNINDEX_PE_MA5Y']

    df_vni['ratio_3M_PE_PE_MA5Y'] = df_vni['PE/PE_MA5Y'].shift(-60) / (df_vni['PE/PE_MA5Y'])

    df_vni['ratio_sup1y'] = df_vni['Close'] / (df_vni['Sup_1Y'])
    df_vni['max_High_2Y'] = df_vni['High'].rolling(240 * 2, min_periods=240).max()

    try:
        if com == 'greater':
            pd_all = df_vni.query(f"{p_col} >= {value}")  # chuỗi dương
        else:
            pd_all = df_vni.query(f"{p_col} <= {value}")  # chuỗi âm

        """
        A: data frame have P6M <= -20
        A deduplicate
        group all hit in same period find group_start_time and group_end_time

        Result:
        If there is no buy hit in same period so that need to consider

        """
        pd_all['start_group'] = pd_all['s_time']
        pd_all['end_group'] = pd_all['s_time']
        # Chuẩn hoá df_vnindex để tra cứu
        df_base = df_vni[['s_time', 'time', 'Open_1D', 'Close', 'max_High_2Y']].copy()
        df_base = df_base.sort_values('s_time').reset_index(drop=True)

        for i_group in range(pd_all.shape[0]):
            row_a = pd_all.iloc[i_group]
            start_group = row_a['start_group']
            try:
                start_index = df_base.index[df_base['s_time'] == start_group][0]
            except:
                # nếu không khớp exact (hiếm), dùng merge_asof để tìm mốc trước đó
                start_index = pd.merge_asof(
                    pd.DataFrame({'s_time': [start_group]}).sort_values('s_time'),
                    df_base[['s_time']].sort_values('s_time'),
                    on='s_time', direction='backward'
                ).index[0]

            break_count = 0

            for per in df_percentiles.columns:
                session = per.split('P')[1]
                try:
                    cur = df_base.iloc[start_index + int(session)]['Open_1D']
                    base = df_base.iloc[start_index]['Open_1D']
                    ret = (cur / base - 1) * 100

                    if ret <= df_percentiles.loc['90%', per]:
                        break_count += 1
                    else:
                        break_count = 0

                    if break_count == 4:
                        break
                except Exception as error:
                    print(f"Error: {error}")
                    print(f"Error: {start_group}")
                    print(f"Error: {session}")
                    session_break = int(session)
                    break

            session_break = int(session) - break_count * 5

            try:
                offset_dates = df_base.iloc[start_index + int(session_break)]['time']
            except:
                offset_dates = df_base.iloc[-1]['time']

            pd_all.at[pd_all.index[i_group], 'end_group'] = offset_dates
            end_close = df_base.loc[df_base['time'] == offset_dates, 'Close'].values[0]
            pd_all.at[pd_all.index[i_group], 'profit'] = (end_close - row_a['Close']) / row_a['Close']

        pd_all['month'] = pd.to_datetime(pd_all['month'])
        pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)

        # Group consecutive months (month_diff <= 1)
        pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
        pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')

        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')

        pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
        # # Group consecutive group (group_diff <= 1)
        pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
        pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')

        #
        pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
        pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first').reset_index(drop=True)

        ###########
        price_t = df_vni[['s_time', 'Close']].sort_values('s_time').reset_index(drop=True)
        times = price_t['s_time'].to_numpy()
        closes = price_t['Close'].to_numpy()

        def range_max(t1, t2):
            i = np.searchsorted(times, np.datetime64(t1), side='left')
            j = np.searchsorted(times, np.datetime64(t2), side='right') - 1
            if j < i:
                return np.nan
            return np.nanmax(closes[i:j + 1])

        pd_all['max_Close'] = [range_max(s, e) for s, e in zip(pd_all['start_group'], pd_all['end_group'])]

        # exact map
        map_series = df_base.set_index('s_time')['max_High_2Y']
        pd_all['max_High_2Y'] = pd_all['start_group'].map(map_series)

        # fallback merge_asof (lấy giá trị trước đó gần nhất nếu không khớp exact)
        na_mask = pd_all['max_High_2Y'].isna()
        if na_mask.any():
            base_ch = df_base[['s_time', 'max_High_2Y']].dropna().drop_duplicates('s_time', keep='last').sort_values(
                's_time')
            filled = pd.merge_asof(
                pd_all.loc[na_mask, ['start_group']].sort_values('start_group'),
                base_ch, left_on='start_group', right_on='s_time', direction='backward'
            )
            filled.index = pd_all.loc[na_mask].sort_values('start_group').index
            pd_all.loc[na_mask, 'max_High_2Y'] = filled['max_High_2Y'].values

        # ==== Tính Cmax_H2Y ====
        pd_all['Cmax_H2Y'] = pd_all['max_Close'] / pd_all['max_High_2Y']
        # path = 'tuning/results/'
        # res_month = pd.DataFrame(res_month)
        # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
        # return pd_all[['start_group', 'end_group']]

        return pd_all[
            ['start_group', 'end_group', 'start_group_q', 'end_group_q',
             'ratio_3M_TD_MA1M', 'ratio_3M_PE_PE_MA5Y', 'ratio_sup1y', 'max_High_2Y', 'max_Close', 'Cmax_H2Y']]
    except Exception as error:
        print(f"Error: {ticker}: {error}")
        print(traceback.format_exc())
        pass


pd_period = eval(df_vnindex, df_percentiles, 'P3M', 22.65, com='greater')
# pd_period = eval(df_vnindex, 'P3M', -14.3, com='less')
#%%
import traceback


def eval_PE(df_vni, df_percentiles, p_col='P3M', value=20, com='greater'):
    ticker = 'VNINDEX'
    df_vni = df_vni.copy()
    df_vni['s_time'] = pd.to_datetime(df_vni['time'])
    df_vni['week'] = df_vni['s_time'].dt.strftime('%Y-%W')
    df_vni['biweek'] = df_vni['s_time'].dt.to_period('2W').astype(str)
    df_vni['month'] = df_vni['s_time'].dt.strftime('%Y-%m')

    df_vni['TD_MA1M'] = df_vni['Trading_Session'].rolling(20, min_periods=5).mean()
    df_vni['TD_MA1M_center'] = df_vni['Trading_Session'].rolling(20, center=True, min_periods=5).mean()
    df_vni['ratio_3M_TD_MA1M'] = df_vni['TD_MA1M'].shift(-60) / (df_vni['TD_MA1M_center'])

    df_vni['PE/PE_MA5Y'] = df_vni['VNINDEX_PE'] / df_vni['VNINDEX_PE_MA5Y']

    df_vni['ratio_3M_PE_PE_MA5Y'] = df_vni['PE/PE_MA5Y'].shift(-60) / (df_vni['PE/PE_MA5Y'])

    df_vni['ratio_sup1y'] = df_vni['Close'] / (df_vni['Sup_1Y'])
    df_vni['max_High_2Y'] = df_vni['High'].rolling(240 * 2, min_periods=240).max()

    # Chuẩn hoá df_vnindex để tra cứu
    df_base = df_vni[['s_time', 'time', 'Open_1D', 'Close', 'max_High_2Y']].copy()
    df_base = df_base.sort_values('s_time').reset_index(drop=True)
    try:

        if com == 'greater':
            pd_all = df_vni.query(f"{p_col} >= {value}")  # chuỗi dương
        else:
            pd_all = df_vni.query(f"{p_col} <= {value}")  # chuỗi âm

        # pd_all['month'] = pd.to_datetime(pd_all['month'])
        # pd_all['month_diff'] = round(pd_all['month'].diff().dt.days / 31).fillna(0)
        #
        # # Group consecutive months (month_diff <= 1)
        # pd_all['group'] = (pd_all['month_diff'] > 1).cumsum()
        # pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('min')
        # pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('max')
        #
        # pd_all = pd_all.drop_duplicates(subset=['group'], keep='first')
        #
        # pd_all['group_diff'] = round((pd_all['start_group'] - pd_all['end_group'].shift(1)).dt.days / 31).fillna(0)
        # # # Group consecutive group (group_diff <= 1)
        # pd_all['group'] = (pd_all['group_diff'] > 1).cumsum()
        # pd_all['start_group'] = pd_all.groupby('group')['start_group'].transform('min')
        # pd_all['end_group'] = pd_all.groupby('group')['end_group'].transform('max')
        #
        #
        # #
        # pd_all['start_group_q'] = pd_all['start_group'].dt.to_period('Q').astype(str)
        # pd_all['end_group_q'] = pd_all['end_group'].dt.to_period('Q').astype(str)
        # pd_all = pd_all.drop_duplicates(subset=['group'], keep='first').reset_index(drop=True)
        pd_all['date_diff'] = pd_all['s_time'].diff().dt.days
        pd_all['group'] = (pd_all['date_diff'] > 15).cumsum()
        pd_all['start_group'] = pd_all.groupby('group')['s_time'].transform('min')
        pd_all['end_group'] = pd_all.groupby('group')['s_time'].transform('max')
        pd_all = pd_all.drop_duplicates(subset=['group'], keep='first').reset_index(drop=True)

        ###########
        price_t = df_vni[['s_time', 'Close', 'VNINDEX_PE']].sort_values('s_time').reset_index(drop=True)
        times = price_t['s_time'].to_numpy()
        closes = price_t['Close'].to_numpy()
        pe = price_t['VNINDEX_PE'].to_numpy()

        def range_max(t1, t2):
            i = np.searchsorted(times, np.datetime64(t1), side='left')
            j = np.searchsorted(times, np.datetime64(t2), side='right') - 1
            if j < i:
                return np.nan
            return np.nanmax(closes[i:j + 1])

        def range_mean(t1, t2):
            i = np.searchsorted(times, np.datetime64(t1), side='left')
            j = np.searchsorted(times, np.datetime64(t2), side='right') - 1
            if j < i:
                return np.nan
            return np.nanmean(pe[i:j + 1])

        pd_all['max_Close'] = [range_max(s, e) for s, e in zip(pd_all['start_group'], pd_all['end_group'])]
        pd_all['VN_PE'] = [range_mean(s, e) for s, e in zip(pd_all['start_group'], pd_all['end_group'])]

        # exact map
        map_series = df_base.set_index('s_time')['max_High_2Y']
        pd_all['max_High_2Y'] = pd_all['start_group'].map(map_series)

        # fallback merge_asof (lấy giá trị trước đó gần nhất nếu không khớp exact)
        na_mask = pd_all['max_High_2Y'].isna()
        if na_mask.any():
            base_ch = df_base[['s_time', 'max_High_2Y']].dropna().drop_duplicates('s_time', keep='last').sort_values(
                's_time')
            filled = pd.merge_asof(
                pd_all.loc[na_mask, ['start_group']].sort_values('start_group'),
                base_ch, left_on='start_group', right_on='s_time', direction='backward'
            )
            filled.index = pd_all.loc[na_mask].sort_values('start_group').index
            pd_all.loc[na_mask, 'max_High_2Y'] = filled['max_High_2Y'].values

        # ==== Tính Cmax_H2Y ====
        pd_all['Cmax_H2Y'] = pd_all['max_Close'] / pd_all['max_High_2Y']
        # path = 'tuning/results/'
        # res_month = pd.DataFrame(res_month)
        # res_month.to_csv(f"{path}res_month_VNINDEX_20_3M.csv", index=False)
        # return pd_all[['start_group', 'end_group']]

        return pd_all[
            ['start_group', 'end_group', 'VN_PE', 'Cmax_H2Y']]
    except Exception as error:
        print(f"Error: {ticker}: {error}")
        print(traceback.format_exc())


pd_period = eval_PE(df_vnindex, df_percentiles, 'VNINDEX_PE', 22.65, com='greater')
PERCENTILES = {
    'PE_P95': 18.078,
    'PE_P90': 17.33,
    'PE_10': 12.3,
    'PE_20': 13.07,
}

for p in PERCENTILES:
    if p in ['PE_P95', 'PE_P90']:
        com = 'greater'
    else:
        com = 'less'
    pd_period = eval_PE(df_vnindex, df_percentiles, 'VNINDEX_PE', PERCENTILES[p], com=com)
    print(p, PERCENTILES[p])
    print(pd_period)
    print('*' * 100)
# pd_period = eval(df_vnindex, 'P3M', -14.3, com='less')
#%%
def sell_vnindex(df_vnindex):
    filter = {
        "~BearDvgVNI1": "(ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
        "~BearDvgVNI2": "(ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
    }
    cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'C1W', 'C2W', 'C3W', 'C1M', 'C2M', 'C3M',
            'C6M', 'C1Y', 'C2Y', 'L1W', 'L2W', 'L3W', 'L1M', 'L2M', 'L3M', 'L6M', 'L1Y', 'L2Y', 'H1W', 'H2W',
            'H3W',
            'H1M', 'H2M', 'H3M', 'H6M', 'H1Y', 'H2Y', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M', 'O6M', 'O1Y',
            'O2Y'] + ['filter']
    df_result = []
    for f in filter:
        try:
            result = df_vnindex.query(f'({filter[f]})').copy()
            result['filter'] = f[1:]
            df_result.append(result)
        except Exception as e:
            pass
    df_result = pd.concat(df_result, axis=0).reset_index(drop=True)
    df_result = df_result.sort_values('time', ascending=True).reset_index(drop=True)
    df_result['month'] = df_result['time'].map(lambda x: x[:7])
    df_result.drop_duplicates(subset=['month'], keep='first', inplace=True)

    df_result['s_time'] = pd.to_datetime(df_result['time'])
    df_result['date_diff'] = df_result['s_time'].diff().dt.days
    df_result['group'] = (df_result['date_diff'] > 15).cumsum()

    df_result = df_result.drop_duplicates(subset=['group'], keep='first')
    df_result = df_result
    return df_result


pd_filter = sell_vnindex(df_vnindex)
#%%
pd_period = eval(df_vnindex, 'P3M', 22.65, com='greater')
pd_period = eval(df_vnindex, 'P3M', -14.3, com='less')

# pd_period = eval_MA(df_vnindex, 'MA20_MA200', 20.77, com='greater')
# pd_period = eval_MA(df_vnindex, 'MA20_MA200', -13.1, com='less')
#%%

#%%
TICKER_PATH = 'ticker_v1a/'
list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(TICKER_PATH) if f.endswith('.csv')]
pd_riskrate = pd.read_csv('webui/risk_rating_bin.csv')
pd_all = multi_process(list_processed_ticker, pd_riskrate)
pd_all = pd_all.sort_values('time', ascending=True).reset_index(drop=True)
#%%
# handle
result = []
df_after = []
for i in range(pd_period.shape[0]):
    start_date = pd_period.iloc[i]['start_group']
    end_date = pd_period.iloc[i]['end_group']
    print(pd_period.iloc[i]['start_group'], pd_period.iloc[i]['end_group'])

    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    dates = pd.date_range(start=end_date - pd.Timedelta(days=30), end=start_date, freq='-30D')
    if dates[-1] - start_date > pd.Timedelta(days=15):
        dates = dates.insert(len(dates), start_date)
    if end_date in dates:
        dates = dates.drop(end_date)

    dates = dates.drop_duplicates()
    dates = dates.sort_values(ascending=True)
    dates = dates.to_numpy(dtype='datetime64[ns]')

    df_times = pd.to_datetime(df_vnindex['time'].copy(), errors='coerce').values

    dates_list = []
    for d in dates:
        idx = np.searchsorted(df_times, d, side='left')
        while idx < len(df_times) and (pd.isna(df_vnindex['P1M'].iloc[idx]) or df_vnindex['P1M'].iloc[idx] is None):
            idx += 1
        if idx < len(df_times):
            matched_day = df_times[idx]
            dates_list.append(matched_day)

    df_all_slice = pd_all[
        (pd_all['time'] >= start_date.strftime('%Y-%m-%d')) & (
                pd_all['time'] <= (end_date + pd.Timedelta(days=31)).strftime('%Y-%m-%d'))]

    for d in dates_list:
        d_str = pd.Timestamp(d).strftime('%Y-%m-%d')
        result.append({
            'time_series': i,
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'date': (d + pd.Timedelta(days=30)).strftime('%Y-%m-%d'),
            'ratio_3M_trading_session': pd_period.iloc[i]['ratio_3M_TD_MA1M'],
            'ratio_3M_PE/PE_MA5Y': pd_period.iloc[i]['ratio_3M_PE_PE_MA5Y'],
            'ratio_sup1y': pd_period.iloc[i]['ratio_sup1y'],
            'P_mean': pd_period.iloc[i]['mean_profit'],
            'P1M_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1")['P1M'].mean(),
            'P1M_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2")['P1M'].mean(),
            'P1M_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3")['P1M'].mean(),
            'P1M_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4")['P1M'].mean(),
            'P1M_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5")['P1M'].mean(),
            'count_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1").shape[0],
            'count_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2").shape[0],
            'count_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3").shape[0],
            'count_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4").shape[0],
            'count_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5").shape[0],
        })

    #########

    idx = np.searchsorted(df_times, np.datetime64(end_date), side='left')
    while idx < len(df_times) and (pd.isna(df_vnindex['P3M'].iloc[idx]) or df_vnindex['P3M'].iloc[idx] is None):
        idx += 1
    if idx < len(df_times):
        matched_day = df_times[idx]

    d_str = pd.Timestamp(matched_day).strftime('%Y-%m-%d')
    df_after.append({
        'time_series': i,
        'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
        'P3M_index': df_vnindex.query(f"time == '{d_str}'")['P3M'].values[0],
        'P3M_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1")['P3M'].mean(),
        'P3M_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2")['P3M'].mean(),
        'P3M_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3")['P3M'].mean(),
        'P3M_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4")['P3M'].mean(),
        'P3M_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5")['P3M'].mean(),
        'P6M_index': df_vnindex.query(f"time == '{d_str}'")['P6M'].values[0],
        'P6M_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1")['P6M'].mean(),
        'P6M_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2")['P6M'].mean(),
        'P6M_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3")['P6M'].mean(),
        'P6M_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4")['P6M'].mean(),
        'P6M_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5")['P6M'].mean(),
        'P9M_index': df_vnindex.query(f"time == '{d_str}'")['P9M'].values[0],
        'P9M_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1")['P9M'].mean(),
        'P9M_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2")['P9M'].mean(),
        'P9M_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3")['P9M'].mean(),
        'P9M_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4")['P9M'].mean(),
        'P9M_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5")['P9M'].mean(),
        'P1Y_index': df_vnindex.query(f"time == '{d_str}'")['P1Y'].values[0],
        'P1Y_1': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 1")['P1Y'].mean(),
        'P1Y_2': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 2")['P1Y'].mean(),
        'P1Y_3': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 3")['P1Y'].mean(),
        'P1Y_4': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 4")['P1Y'].mean(),
        'P1Y_5': df_all_slice.query(f"time == '{d_str}' and Risk_Rating == 5")['P1Y'].mean(),

    })

df_result = pd.DataFrame(result)
df_p3m = pd.DataFrame(df_after)

# df_p3m[['P3M_1', 'P3M_2', 'P3M_3', 'P3M_4', 'P3M_4']].n
#%%
#handle for hit
# handle
from numpy import median

PERCENTILES = {
    'PE_P95': df_percentiles_1.loc['95%', 'VNINDEX_PE'],
    'PE_P90': df_percentiles_1.loc['90%', 'VNINDEX_PE'],
    'PE_P85': df_percentiles_1.loc['85%', 'VNINDEX_PE'],
    'PE_P80': df_percentiles_1.loc['80%', 'VNINDEX_PE'],
}


def _safe_div(num_series, denom):
    return np.nan if (denom == 0) or (pd.isna(denom)) else num_series.sum() / denom


hit_result = []
for i in range(pd_filter.shape[0]):

    #########
    hit_time = pd_filter.iloc[i]['time']
    VNINDEX_PE = pd_filter.iloc[i]['VNINDEX_PE']

    if VNINDEX_PE > PERCENTILES['PE_P95']:
        VNINDEX_PE = 'PE above 95% percentile'
    elif VNINDEX_PE > PERCENTILES['PE_P90']:
        VNINDEX_PE = 'PE above 90% percentile'
    elif VNINDEX_PE > PERCENTILES['PE_P85']:
        VNINDEX_PE = 'PE above 85% percentile'
    elif VNINDEX_PE > PERCENTILES['PE_P80']:
        VNINDEX_PE = 'PE above 80% percentile'

    start_date = None
    end_date = None

    flag_in_p90 = False
    profit_hit2start = None
    del_day_show = None

    for id_period in range(pd_period.shape[0]):
        start_date = pd_period.iloc[id_period]['start_group']
        end_date = pd_period.iloc[id_period]['end_group']

        if start_date < pd.Timestamp(hit_time) < end_date:
            flag_in_p90 = True
            hit_close = df_vnindex.loc[df_vnindex['time'] == hit_time, 'Close'].values[0]
            start_close = df_vnindex.loc[df_vnindex['time'] == start_date.strftime('%Y-%m-%d'), 'Close'].values[0]
            profit_hit2start = 100 * (hit_close / start_close - 1)
            start_date_show = start_date.strftime('%Y-%m-%d')
            end_date_show = end_date.strftime('%Y-%m-%d')
            break
        del_date = (pd.Timestamp(hit_time) - end_date).days
        if (pd.Timestamp(hit_time) > end_date) and (del_date < 365):
            hit_close = df_vnindex.loc[df_vnindex['time'] == hit_time, 'Close'].values[0]
            start_close = df_vnindex.loc[df_vnindex['time'] == start_date.strftime('%Y-%m-%d'), 'Close'].values[0]
            profit_hit2start = 100 * (hit_close / start_close - 1)
            del_day_show = (pd.Timestamp(hit_time) - end_date).days
            start_date_show = start_date.strftime('%Y-%m-%d')
            end_date_show = end_date.strftime('%Y-%m-%d')
            flag_in_p90 = True


    pd_a = pd_all.copy()
    pd_a['s_time'] = pd.to_datetime(pd_a['time'])
    pd_daa = pd_a[pd_a['s_time'] == pd.Timestamp(hit_time)]
    pd_daa = pd_daa[pd.notna(pd_daa['m_cap'])]

    # Chuẩn hóa profit weight
    pd_daa['weight'] = pd_daa['m_cap'] / pd_daa['m_cap'].sum()
    pd_daa['weight'] = pd_daa['weight'].clip(upper=0.1)
    pd_daa['weight'] = pd_daa['weight'] / pd_daa['weight'].sum()
    for col in ['P1M', 'P3M', 'P6M', 'P9M', 'P1Y']:
        pd_daa[f'w_{col}'] = pd_daa[col] * pd_daa['weight']
    if hit_time == '2004-03-30':
        a = pd_daa.copy()
    print('hit_time', hit_time)
    print(pd_daa['s_time'].unique())
    print(pd_daa.shape[0])
    print(pd_filter.iloc[i])

    hit_result.append({
        'time_series': hit_time,
        'VNINDEX_PE': VNINDEX_PE,
        'period': f"{start_date_show} to {end_date_show}" if flag_in_p90 else None,
        'profit_hit2start': profit_hit2start,
        'del_day': del_day_show,
        'ticker_count': pd_daa.shape[0],

        'P3M_index': 100 * (pd_filter.iloc[i]['O3M'] - 1),
        'P3M': (pd_daa['w_P3M']).sum(),
        'P3M_1': _safe_div(pd_daa.query(" Risk_Rating == 1")['w_P3M'],
                           pd_daa.query(" Risk_Rating == 1")['weight'].sum()),
        'P3M_2': _safe_div(pd_daa.query(" Risk_Rating == 2")['w_P3M'],
                           pd_daa.query(" Risk_Rating == 2")['weight'].sum()),
        'P3M_3': _safe_div(pd_daa.query(" Risk_Rating == 3")['w_P3M'],
                           pd_daa.query(" Risk_Rating == 3")['weight'].sum()),
        'P3M_4': _safe_div(pd_daa.query(" Risk_Rating == 4")['w_P3M'],
                           pd_daa.query(" Risk_Rating == 4")['weight'].sum()),
        'P3M_5': _safe_div(pd_daa.query(" Risk_Rating == 5")['w_P3M'],
                           pd_daa.query(" Risk_Rating == 5")['weight'].sum()),

        'P6M_index': 100 * (pd_filter.iloc[i]['O6M'] - 1),
        'P6M': (pd_daa['w_P6M']).sum(),
        'P6M_1': _safe_div(pd_daa.query(" Risk_Rating == 1")['w_P6M'],
                           pd_daa.query(" Risk_Rating == 1")['weight'].sum()),
        'P6M_2': _safe_div(pd_daa.query(" Risk_Rating == 2")['w_P6M'],
                           pd_daa.query(" Risk_Rating == 2")['weight'].sum()),
        'P6M_3': _safe_div(pd_daa.query(" Risk_Rating == 3")['w_P6M'],
                           pd_daa.query(" Risk_Rating == 3")['weight'].sum()),
        'P6M_4': _safe_div(pd_daa.query(" Risk_Rating == 4")['w_P6M'],
                           pd_daa.query(" Risk_Rating == 4")['weight'].sum()),
        'P6M_5': _safe_div(pd_daa.query(" Risk_Rating == 5")['w_P6M'],
                           pd_daa.query(" Risk_Rating == 5")['weight'].sum()),

        # NOTE: nếu df_vnindex['P9M'] là tỉ lệ (≈1.x), đổi sang 100*(...-1)
        'P9M_index': df_vnindex.query("time == @hit_time")['P9M'].values[0],
        'P9M': (pd_daa['w_P9M']).sum(),
        'P9M_1': _safe_div(pd_daa.query(" Risk_Rating == 1")['w_P9M'],
                           pd_daa.query(" Risk_Rating == 1")['weight'].sum()),
        'P9M_2': _safe_div(pd_daa.query(" Risk_Rating == 2")['w_P9M'],
                           pd_daa.query(" Risk_Rating == 2")['weight'].sum()),
        'P9M_3': _safe_div(pd_daa.query(" Risk_Rating == 3")['w_P9M'],
                           pd_daa.query(" Risk_Rating == 3")['weight'].sum()),
        'P9M_4': _safe_div(pd_daa.query(" Risk_Rating == 4")['w_P9M'],
                           pd_daa.query(" Risk_Rating == 4")['weight'].sum()),
        'P9M_5': _safe_div(pd_daa.query(" Risk_Rating == 5")['w_P9M'],
                           pd_daa.query(" Risk_Rating == 5")['weight'].sum()),

        'P1Y_index': 100 * (pd_filter.iloc[i]['O1Y'] - 1),
        'P1Y': (pd_daa['w_P1Y']).sum(),
        'P1Y_1': _safe_div(pd_daa.query(" Risk_Rating == 1")['w_P1Y'],
                           pd_daa.query(" Risk_Rating == 1")['weight'].sum()),
        'P1Y_2': _safe_div(pd_daa.query(" Risk_Rating == 2")['w_P1Y'],
                           pd_daa.query(" Risk_Rating == 2")['weight'].sum()),
        'P1Y_3': _safe_div(pd_daa.query(" Risk_Rating == 3")['w_P1Y'],
                           pd_daa.query(" Risk_Rating == 3")['weight'].sum()),
        'P1Y_4': _safe_div(pd_daa.query(" Risk_Rating == 4")['w_P1Y'],
                           pd_daa.query(" Risk_Rating == 4")['weight'].sum()),
        'P1Y_5': _safe_div(pd_daa.query(" Risk_Rating == 5")['w_P1Y'],
                           pd_daa.query(" Risk_Rating == 5")['weight'].sum()),
    })

hit_result = pd.DataFrame(hit_result)
#%%
df_result
#%%
### gdp
df_gdp = pd.read_csv('tuning/API_NY.GDP.MKTP.CD_DS2_en_csv_v2_122379.csv', skiprows=3)
gdp_vn = df_gdp[df_gdp['Country Code'] == 'VNM']

gdp_columns = [col for col in gdp_vn.columns if col.isdigit()]
gdp_vn = gdp_vn[gdp_columns]
gdp_vn = gdp_vn.T.reset_index()
gdp_vn.columns = ['year', 'gdp']
gdp_vn.dropna(inplace=True)
gdp_vn['year'] = (gdp_vn['year'].astype(int) + 1).astype(str)


#%%
### capitalization
pd_all['time'].unique()
df_cap = []
for t in pd_all['time'].unique():
    df_cap.append({
        'time': t,
        'cap': pd_all.query(f"time == '{t}'")['m_cap'].sum()
    })

df_cap = pd.DataFrame(df_cap)
df_cap['year'] = df_cap['time'].str[:4]
#%%
df_cap = df_cap.merge(gdp_vn, on='year', how='left')
df_cap['cap_per_gdp'] = df_cap['cap'] / df_cap['gdp']
df_cap['gdp_per_cap'] = df_cap['gdp'] / df_cap['cap']
#%%
percentiles = [i for i in np.arange(0.1, 1, 0.05)]

df_cap_temp = df_cap[df_cap['year'] > '2010']
step_gdp = []
for p in percentiles:
    step_gdp.append(df_cap_temp['cap_per_gdp'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'cap_per_gdp': df_cap_temp['cap_per_gdp'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)


#%%
