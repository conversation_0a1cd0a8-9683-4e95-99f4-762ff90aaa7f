import random

import pandas as pd
import numpy as np
import trendln
from vnstock3 import Vnstock
import os
from utils import *


def label_data(df):
    """
    Label data to identify bottoms as described.
    """
    df['Label'] = 0
    for i in range(5, len(df) - 5):
        if df['Low'][i] == df['Low'][i - 2:i + 3].min() and df['Low'][i] == df['Low'][i - 5:i + 6].min():
            df.at[df.index[i], 'Label'] = 1
    return df


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt_v1(pdxy, cname_tvt='tvt', test_size=10, YMD_test='2022-01-01'):
    """
    Split data into train, val, test
    """
    list_ticker = list(pdxy['ticker'].unique())
    list_ticker_test = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]
    iV = ((pdxy['Volume'] * pdxy['Price']).rolling(20).mean() > 500e6)
    pdxy[cname_tvt] = 'other'
    iA = pdxy['time'] < YMD_test
    iB = ~iA
    iTest = pdxy['ticker'].isin(list_ticker_test)
    iTrain = ~iTest
    pdxy.loc[iV & iA & iTrain, cname_tvt] = 'train'
    pdxy.loc[iV & iB & iTrain, cname_tvt] = 'val'
    pdxy.loc[iV & iA & iTest, cname_tvt] = 'test1'
    pdxy.loc[iV & iB & iTest, cname_tvt] = 'test2'
    return pdxy


def handle_input_data(pdxy, len_series):
    pdxy = pdxy.copy()
    pdxy["x"] = np.nan
    pdxy['x'] = pdxy['x'].astype('object')
    for i in range(len_series, pdxy.shape[0]):
        pdxy.at[i, "x"] = list((pdxy['Close'][i - len_series: i]).values)
    return pdxy["x"]


def prepare_pdxy(list_processed_ticker, in_folder='../ticker_v1a/'):
    ## combine all ticker data into one file
    ll = []
    for ticker in list_processed_ticker:
        pd_xy = load_data(f'{in_folder}/{ticker}.csv', start=2014)
        pd_xy['Close_T20'] = handle_input_data(pd_xy, 20)
        pd_xy['min_T20'] = pd_xy['Close'].rolling(20).min()
        pd_xy['EMA'] = pd_xy['Close'].ewm(span=20, adjust=False).mean()
        # Bollinger Bands
        std = pd_xy['Close'].rolling(20).std()
        pd_xy['BB_lower'] = pd_xy['MA20'] - (std * 2)

        for time in range(1, 6):
            col = f"minL{time}"
            pd_xy[col] = pd_xy['Close'].rolling(time).min()
            # pd_xy[col] = pd_xy['Close'].rolling(20).apply(lambda x: 1 if (x.min() == x[20 - 1 - time: 20]).any() else 0)
        for name, w in zip(['minW5D', 'minW10D', 'minW20D'], [11, 21, 41]):
            pd_xy[name] = pd_xy['Close'].rolling(w, center=True).min()
            # pd_xy[name] = pd_xy['Close'].rolling(w, center=True).apply(lambda x: 1 if x[w // 2] == x.min() else 0,
            #                                                            raw=True)


        pd_xy['Y1'] = 0.0
        for i in range(1, pd_xy.shape[0]):
            if pd_xy.at[i, 'minW10D'] == 1:
                pd_xy.at[i, 'Y1'] = 0.8
                pd_xy.at[i + 1, 'Y1'] = 0.9
                pd_xy.at[i + 2, 'Y1'] = 1

        pd_xy['label1'] = np.where(pd_xy['minL1'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label2'] = np.where(pd_xy['minL2'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label3'] = np.where(pd_xy['minL3'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label4'] = np.where(pd_xy['minL4'] == pd_xy['minW5D'], 1, 0)
        pd_xy['label5'] = np.where(pd_xy['minL5'] == pd_xy['minW5D'], 1, 0)

        pd_xy.dropna(subset="Close_T20", inplace=True)
        pd_xy.ffill(inplace=True)
        ll.append(pd_xy)

    pdxy = pd.concat(ll, axis=0, ignore_index=True).reset_index(drop=True)  #
    pdxy = pdxy.dropna(axis=1, how='all') # drop columns with all NaN

    # pdxy.replace(np.inf, np.nan, inplace=True)
    # pdxy.replace(-np.inf, np.nan, inplace=True)
    # pdxy.dropna(inplace=True)


    cname_feature = [f for f in pdxy.columns if f not in ['time', 'ticker', 'ymd', 'quarter',
                                                          'Open', 'Close', 'High', 'Low', 'Close_TT20',
                                                          'L1W', 'L2W', 'L3w', 'L1M', 'L2M', 'L3M', 'L1Y', 'L2Y', 'H1W',
                                                          'H2W', 'H3W', 'H1M', 'H2M', 'H3M', 'H1Y', 'H2Y', 'C1W', 'C2W',
                                                          'C3W', 'C1M', 'C2M', 'C3M', 'C1Y', 'C2Y']]

    cname_tvt = 'tvt'
    split_tvt_v1(pdxy, cname_tvt)
    list_eval = get_list_eval(pdxy, cname_tvt=cname_tvt)
    list_label = ['minW5D', 'minW10D', 'minW20D', 'minL1', 'minL2', 'minL3', 'minL4', 'minL5', 'label1',
                  'label2', 'label3', 'label4', 'label5', 'Y1']
    return {'pdxy': pdxy,
            'cname_feature': cname_feature,
            'list_label': list_label,
            'list_eval': list_eval,
            }

# if __name__ == '__main__':
#     a = prepare_pdxy_v1()
#     pass
