import logging
import os
import pickle
import re
import sys
import warnings
from datetime import timedelta

import numpy as np
from hyperopt import hp, fmin, tpe, STATUS_OK, STATUS_FAIL
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from hyperopt.pyll.base import scope
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime
from core_utils.base_eval import TickerEval, PreProcess, Simulation

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()


class TickerEval_tune(TickerEval):
    def __init__(self, stock, df_all, dict_filter, cutloss=0.15, cache_service=None):
        super().__init__(stock, df_all, dict_filter, cutloss, cache_service)

    @TickerEval.cache_prefix_result(unique=False, expiry=18000)
    def sell_signals(self):
        # Apply sell filters
        s_cols = ['ticker', 'time', 'Close', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M',
                  'P3M', 'P1Y',
                  'P2Y']

        now = self.df_all.iloc[-1:].copy()
        now['Sell_filter'] = 'Hold'

        sell_data = [now]

        for f in self.dictFilter:
            if f.startswith('~'):
                try:
                    pd_Sell_filtered = self.pd_query(f, self.dictFilter[f])
                    sell_data.append(pd_Sell_filtered)
                except Exception as e:
                    pass
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")

        _sell_data = [data for data in sell_data if not data.empty]
        pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True)
        for period in ['1W', '2W', '3W', '1M', '2M', '3M', '1Y', '2Y']:
            pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"C{period}"]) * (
                    pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
                                            pd_sell[f"H{period}"] >= 1 + self.cutloss)

        pd_sell = pd_sell[s_cols]
        return pd_sell

    @TickerEval.cache_prefix_result(unique=False, expiry=18000)
    def buy_signals(self):
        b_cols = ['time', 'ticker', 'Close', 'Open_1D', 'Volume', 'C1M', 'C1W', 'C1Y', 'C2M', 'C2W', 'C2Y', 'C3M',
                  'C3W', 'P1W', 'P1M', 'P3M', 'P1Y', 'P2Y', 'filter', 'hit', 'month']

        # Apply buy filters
        buy_data = []

        for f in self.dictFilter:
            if f.startswith('_'):
                try:
                    pd_buy_filtered = self.pd_query(f, self.dictFilter[f])
                    buy_data.append(pd_buy_filtered)
                except Exception as e:
                    pass
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")

        _buy_data = [data for data in buy_data if not data.empty]
        pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]
        pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else pd.DataFrame(
            columns=b_cols)

        for period in ['1W', '1M', '3M', '1Y', '2Y']:
            pd_buy[f"P{period}"] = 100. * (pd_buy[f"C{period}"] - 1) * (
                    pd_buy[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
                                           pd_buy[f"L{period}"] <= 1 - self.cutloss)
        pd_buy['hit'] = 1
        pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])

        return pd_buy[b_cols]


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


def eval_filter_all_v2(dictFilter, CUTLOSS=0.15, skip=None):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    if skip is None:
        skip = []

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval_tune(ticker, pdxx, dictFilter, cutloss=CUTLOSS, cache_service=redis_cache)
            res = eval_ticker.eval_by_deal()
            return res
        except Exception as error:
            # print(f"Error: {ticker}: {error}")
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        # lres = p.amap(eval, list_processed_ticker)
        lres = p.map(eval, list_processed_ticker)

        # lres.extend(lres.get())
    try:
        lres = [res for res in lres if res is not None and not res.empty]

        pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)
        if pd_deal.shape[0] < 150:
            print(pd_deal.shape[0])
            return {
                'si_return': -np.inf,
                'si_std': -np.inf,
                '%win_deal': -np.inf,
                'deal': 0,
                'status': STATUS_FAIL
            }

        df_process = PreProcess()
        pd_deal = df_process.deals(pd_deal)

        start_date, _ = parse_time(dictFilter)
        simulation = Simulation(pd_deal, start_date=start_date, initial_assets=1e8, max_deals=10,
                                cache_service=memory, num_proc=8)
        result = simulation.run_fast(iterate=50)
        si_return = 0
        std = 0

        for pattern, value in result.items():
            si_return += value['return']
            std += value['return_std']

        _pdd_d = df_process.group_by(pd_deal, ['filter'])
        _pdd_d['%win_deal'] = (_pdd_d['count_win'].astype(int) / _pdd_d['deal'].astype(int)) * 100

        # return si_return, std, _pdd_d['%win_deal'].values[0]
        return {
            'si_return': si_return,
            'si_std': std,
            '%win_deal': _pdd_d['%win_deal'].values[0],
            'deal': _pdd_d['deal'].values[0],
            'status': STATUS_OK
        }
    except:
        return {
            'si_return': -np.inf,
            'si_std': -np.inf,
            '%win_deal': -np.inf,
            'deal': 1,
            'status': STATUS_OK
        }


if __name__ == "__main__":

    import random

    random.seed(0)


    def objective(params):
        skip = []

        w1 = params['w1']
        w2 = params['w2']
        w3 = params['w3']
        w4 = params['w4']
        w5 = params['w5']
        w6 = params['w6']
        w7 = params['w7']
        w8 = params['w8']
        w9 = params['w9']
        w10 = params['w10']
        w11 = params['w11']
        w12 = params['w12']
        w13 = params['w13']
        w14 = params['w14']
        w15 = params['w15']
        # w16 = params['w16']

        key_map = {
            'w_ma21': 'MA21',
            'w_ma31': 'MA31',
            'w_ma41': 'MA41',
            'w_s13': 'S13',
            'w_selllowgrowth': 'SellLowGrowth',
            'w_sellresistance1y': 'SellResistance1Y',
            'w_sellresistance1m': 'SellResistance1M',
            'w_sellresistance': 'SellResistance',
            'w_sellbv': 'SellBV',
            'w_sellbv2': 'SellBV2',
            'w_sellpe': 'SellPE',
            'w_sellvolmax': 'SellVolMax',
            'w_beardvg2': 'BearDvg2'
        }

        sell_filter = {
            "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
            "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
            "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
            "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
            "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
            "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
            "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
            "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
            "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
            "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
            "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
            "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
            "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
        }

        filter = {
            "_BuyPattern": f"(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')  & (Close > {w1}*Volume_Max5Y_High) & (Close_T1W < {w2}*Volume_Max5Y_High) & (Volume > {w3}*Volume_3M_P50) & (PE >{w4}) & (PE < {w5}) & (PB<{w6}) & (PCF > {w7}) & (((NP_P0 > {w8}*NP_P1)& (PCF < {w9}) & (ROE_Min5Y > {w10})) | ((((NP_P0 - NP_P4)/abs(NP_P4) > {w11})) & (PCF < {w12})))  & (ID_Current-Volume_Max5Y_ID<={w13})  & (Volume_Max5Y_High/LO_3M_T1 < {w14}) & (FSCORE > {w15})"
        }
        filter.update(sell_filter)

        b2s = ""
        for key, value in key_map.items():
            if params[key] > 0:
                b2s += f"{value}, "

        filter['$BuyPattern'] = b2s[:-2]
        print(filter)

        result = eval_filter_all_v2(filter, skip=skip)

        si_return = result['si_return']
        si_std = result['si_std']
        windeal = result['%win_deal']
        deal = result['deal']

        loss = si_return + windeal

        logging.info(f"\n")
        logging.info(f'return: {si_return}, std: {si_std}, windeal: {windeal}, deal :{deal}, loss: {-loss}')

        print(f'return: {si_return}, std: {si_std}, windeal: {windeal}, deal :{deal}, loss: {-loss}')
        return {'loss': -loss, 'status': result['status']}

    search_space = {
        # (Close > {w1}*Volume_Max5Y_High)
        'w1': hp.quniform('w1', 1.0, 1.2, 0.01),

        # (Close_T1W < {w2}*Volume_Max5Y_High)
        'w2': hp.quniform('w2', 0.8, 1.1, 0.01),

        # (Volume > {w3}*Volume_3M_P50)
        'w3': hp.quniform('w3', 0.8, 1.2, 0.05),

        # (PE > {w4})
        'w4': hp.quniform('w4', 0, 5, 0.1),

        # (PE < {w5})
        'w5': hp.quniform('w5', 10, 20, 0.2),

        # (PB < {w6})
        'w6': hp.quniform('w6', 2, 5, 0.1),

        # (PCF > {w7})
        'w7': hp.quniform('w7', 0, 5, 0.1),

        # (NP_P0 > {w8}*NP_P1)
        'w8': hp.quniform('w8', 1.0, 1.5, 0.05),

        # (PCF < {w9})
        'w9': hp.quniform('w9', 15, 30, 0.2),

        # (ROE_Min5Y > {w10})
        'w10': hp.quniform('w10', 0.01, 0.05, 0.005),

        # ((NP_P0 - NP_P4)/abs(NP_P4) > {w11})
        'w11': hp.quniform('w11', 0.5, 1.5, 0.1),

        # (PCF < {w12})
        'w12': hp.quniform('w12', 10, 20, 0.2),

        # (ID_Current - Volume_Max5Y_ID <= {w13})
        'w13': hp.quniform('w13', 100, 150, 5),

        # (Volume_Max5Y_High / LO_3M_T1 < {w14})
        'w14': hp.quniform('w14', 1.0, 1.5, 0.05),

        # (FSCORE > {w15})
        'w15': hp.quniform('w15', 2, 5, 1),

        'w_ma21': hp.quniform('w_ma21', 0, 1, 1),
        'w_ma31': hp.quniform('w_ma31', 0, 1, 1),
        'w_ma41': hp.quniform('w_ma41', 0, 1, 1),
        'w_s13': hp.quniform('w_s13', 0, 1, 1),
        'w_selllowgrowth': hp.quniform('w_selllowgrowth', 0, 1, 1),
        'w_sellresistance1y': hp.quniform('w_sellresistance1y', 0, 1, 1),
        'w_sellresistance1m': hp.quniform('w_sellresistance1m', 0, 1, 1),
        'w_sellresistance': hp.quniform('w_sellresistance', 0, 1, 1),
        'w_sellbv': hp.quniform('w_sellbv', 0, 1, 1),
        'w_sellbv2': hp.quniform('w_sellbv2', 0, 1, 1),
        'w_sellpe': hp.quniform('w_sellpe', 0, 1, 1),
        'w_sellvolmax': hp.quniform('w_sellvolmax', 0, 1, 1),
        'w_beardvg2': hp.quniform('w_beardvg2', 0, 1, 1),
    }

    init_vals = [{
        'w1': 1.02,  # Close > 1.02 * Volume_Max5Y_High
        'w2': 1.0,  # Close_T1W < Volume_Max5Y_High
        'w3': 1.0,  # Volume > Volume_3M_P50
        'w4': 0.0,  # PE > 0
        'w5': 16,  # PE < 16
        'w6': 3.5,  # PB < 3.5
        'w7': 0.0,  # PCF > 0
        'w8': 1.2,  # NP_P0 > 1.2 * NP_P1
        'w9': 25,  # PCF < 25
        'w10': 0.03,  # ROE_Min5Y > 0.03
        'w11': 1.0,  # (NP_P0 - NP_P4)/abs(NP_P4) > 1
        'w12': 15,  # PCF < 15
        'w13': 120,  # ID_Current - Volume_Max5Y_ID <= 120
        'w14': 1.3,  # Volume_Max5Y_High / LO_3M_T1 < 1.3
        'w15': 3.0,  # FSCORE > 3

        'w_ma21': 1,
        'w_ma31': 1,
        'w_ma41': 1,
        'w_s13': 1,
        'w_selllowgrowth': 1,
        'w_sellresistance1y': 1,
        'w_sellresistance1m': 1,
        'w_sellresistance': 1,
        'w_sellbv': 1,
        'w_sellbv2': 1,
        'w_sellpe': 1,
        'w_sellvolmax': 1,
        'w_beardvg2': 1,
    }]
    # objective(init_vals[0])

    trial_name = "tuning/trials_new_VOLMAX.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)
    # trials = pickle.load(open("tuning/trials_BullDvg.pkl", "rb"))
    # trials = Trials()

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=1000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(1000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
