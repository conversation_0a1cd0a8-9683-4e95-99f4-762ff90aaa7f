from datetime import datetime

import numpy as np
import pandas as pd
from pathos.multiprocessing import ProcessingPool as Pool
from report.utils import eval_lookback_filter, filter_functions
import xgboost as xgb
from core_utils.base_eval import Simulation, TickerEval
import streamlit as st
import requests
import websocket


class TickerEval_tuning(TickerEval):
    def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, k_model=(None, None, None), cache_service=None):
        super().__init__(stock, data_frame, dict_filter, cutloss, cache_service=cache_service)

    def eval_ai_deal(self):
        pass

    def init_ai_model(self, kwargs=None):
        model, threshold, c_feature, c_label = kwargs
        self.model = model
        self.threshold = threshold
        self.cfeature = c_feature
        self.c_label = c_label
        self.cfeature = ['time_x', 'Open_x', 'High_x', 'Low_x', 'Close_x', 'Volume_x',
                         'ticker_x', 'ICB_Code_x', 'Price_x', 'C_H1M_x',
                         'VN30_MACDdiff_T1W_y', 'VNINDEX_PE_y', 'VNINDEX_LNST_y', 'T1W_y',
                         'T2W_y', 'T1M_y', 'T3M_y', 'sell_index', 'holding_session',
                         'sell_profit']

    def ai_hit(self):
        duration = 120

        # df_buy['buy_index'] = df_buy.index

        df_hit = []
        df_all = self.df_all.copy()
        df_buy = self.df_buy.copy()
        df_buy['buy_index'] = df_buy.index
        df_all['sell_index'] = df_all.index
        df_all['buy_index'] = np.nan
        for idx_buy, curr_buy in self.df_buy.iterrows():
            start = df_all[df_all['time'] == curr_buy['time']].index[0] + 3
            end = start + duration if start + duration < df_all.shape[0] else df_all.shape[0]

            df_sell = df_all.iloc[start:end].copy()
            df_sell['buy_index'] = idx_buy
            df_sell.loc[:, 'holding_session'] = list(range(3, 3 + len(df_sell)))
            df_sell.loc[:, 'sell_profit'] = ((df_sell['Open_1D'] / curr_buy['Open_1D']) - 1)
            df_hit.append(df_sell)
            # df_hit.append(df_sell.assign(**curr_buy))

        df_sell = pd.concat(df_hit, axis=0).reset_index(drop=True)

        temp_cfeature = list(set([c_f.replace('_x', '').replace('_y', '') for c_f in self.cfeature]))
        b_cfeature = [f for f in temp_cfeature if f in df_buy.columns]
        s_cfeature = [f for f in temp_cfeature if f in df_sell.columns]
        df_hit = df_buy[b_cfeature + ['buy_index', 'time']].merge(df_sell[s_cfeature + ['buy_index', 'time']],
                                                                  on=['buy_index'], how='left', suffixes=('_x', '_y'))

        # Predict
        # ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
        # cname_label = ['pattern_label']
        df_hit['pred'] = self.model.predict_proba(df_hit[self.cfeature])[:, 1]

        # Find first hit
        df_result = []
        for idx_buy, d_buy in df_buy.iterrows():
            df_label = df_hit.loc[(df_hit['buy_index'] == idx_buy)].copy()

            d = {}
            for i in range(df_label.shape[0]):
                d_sell = df_label.iloc[i]
                if d_sell['pred'] > self.threshold:
                    d = {
                        'ticker': d_sell['ticker'],
                        'buy_index': d_sell['buy_index'],
                        'sell_index': d_sell['sell_index'],
                        'time': d_sell['time_x'],
                        'sell_time': d_sell['time_y'],
                        'holding_session': d_sell['holding_session'],
                        'profit': d_sell['sell_profit'],
                        'sell_reason': 'ml'
                    }
                    break

                if i == df_label.shape[0] - 1:
                    d = {
                        'ticker': d_sell['ticker'],
                        'buy_index': d_sell['buy_index'],
                        'sell_index': d_sell['sell_index'],
                        'time': d_sell['time_x'],
                        'sell_time': d_sell['time_y'],
                        'holding_session': d_sell['holding_session'],
                        'profit': d_sell['sell_profit'],
                        'sell_reason': np.nan
                    }

            df_result.append(pd.DataFrame(data=d, index=[d_buy['buy_index']]))
        df_result = pd.concat(df_result, axis=0).reset_index(drop=True)

        return df_result
        # Predict

        # df_map = df_sell[['time', 'ticker', 'buy_index', 'pattern_label']].copy()
        # df_map['buy_index'] = df_map['buy_index'].apply(lambda x: x.split(',')).copy()
        # df_map = df_map.explode('buy_index')
        # df_map['buy_index'] = df_map['buy_index'].astype(int)
        # df_map['sell_index'] = df_map.index
        # df_map.sort_values(['buy_index', 'sell_index'], inplace=True)
        # df_map = df_map[['buy_index', 'sell_index', 'pattern_label']]
        # df_map.reset_index(drop=True, inplace=True)
        # df_map = df_map.merge(df_buy[['buy_index', 'time', 'ticker']], on='buy_index', how='left')
        # df_sell.drop(columns=['buy_index'], inplace=True)

    def sell_signals_by_ml(self):
        """
        Use xgboots model to predict sell signal
        input : xgboots model
        output:
        pd_sell:
        """

        s_cols = ['time', 'Close', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P1Y', 'P2Y']

        cname_features = []
        self.df_all['pred'] = self.model.predict_proba(self.df_all[cname_features])[:, 1]

        pd_sell = self.df_all[self.df_sell['pred'] > self.threshold].sort_values('time', ascending=True).copy()

        for period in ['1W', '2W', '3W', '1M', '2M', '3M', '1Y', '2Y']:
            pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"C{period}"]) * (
                    pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
                                            pd_sell[f"H{period}"] >= 1 + self.cutloss)

        pd_sell = pd_sell[s_cols]
        return pd_sell


# class TickerEval:
#     """
#     Evaluate filters for a given ticker
#     """
#
#     def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, k_model=(None, None, None)):
#         """
#         Initialize the TickerEval class.
#
#         Args:
#             stock (str): The stock ticker symbol.
#             data_frame (pd.DataFrame): The data frame containing the stock data.
#             dict_filter (dict): A dictionary of filters to apply to the data.
#             cutloss (float, optional): The cut loss threshold. Defaults to 0.15.
#         """
#
#         self.ticker = stock
#         self.df_all = data_frame
#         self.dictFilter = dict_filter
#         self.cutloss = cutloss
#         # self.init_ai_model(k_model)
#         self.ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
#
#         self.df_buy = self.buy_signals()
#         # self.ai_hit()
#         self.buy_2_sell = self.buy_to_sell()
#         self.df_sell = self.sell_signals()
#
#     def eval_by_deal(self):
#         deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
#                      'profit_vni']
#         l_pd_deal = []
#         buy_reasons = list(self.df_buy['filter'].unique())
#         for buy_reason in buy_reasons:
#             buy_reason_indexes = list(self.df_buy[self.df_buy['filter'] == buy_reason].index)
#
#             sell_matching = self.buy_2_sell["all"]
#             if buy_reason in self.buy_2_sell.keys():
#                 sell_matching = self.buy_2_sell[buy_reason]
#
#             pd_sell_temp = self.df_sell.query(f"Sell_filter == {sell_matching}").copy()
#             sell_indexes = list(pd_sell_temp.index)
#             sell_reasons = list(pd_sell_temp['Sell_filter'])
#
#             result = self.find_sell(self.df_all[['time', 'Close', 'Open', 'VNINDEX']], buy_reason_indexes, sell_indexes,
#                                     sell_reasons, self.cutloss)
#
#             deal_time = self.df_all.loc[result[0]]['time'].values
#             deal_sell_time = self.df_all.loc[result[2]]['time'].values
#             pd_deal_pa = pd.DataFrame({
#                 'time': deal_time,
#                 'buy_price': result[1],
#                 'sell_price': result[3],
#                 'profit': result[4],
#                 'sell_filter': result[5],
#                 'sell_time': deal_sell_time,
#                 'profit_vni': result[6],
#             })
#
#             pd_deal_pa.insert(0, 'ticker', self.ticker)
#             pd_deal_pa['filter'] = buy_reason
#
#             l_pd_deal.append(pd_deal_pa)
#         pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)
#
#         return pd_deal
#
#     def eval_by_hit(self):
#         b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Volume', 'P1W', 'P1M', 'P3M', 'P1Y',
#                   'P2Y']
#         s_filters = self.df_sell['Sell_filter'].unique()
#         sell_indexes = list(self.df_sell.index)
#
#         pd_buy = self.df_buy[b_cols].copy()
#         pd_buy["Sell_time"] = None
#         pd_buy["Sell_filter"] = None
#         pd_buy["Sell_profit"] = np.nan
#         pd_buy['P_cutloss'] = np.nan
#         for s_filter in s_filters:
#             pd_buy[f"P_{s_filter}"] = np.nan
#
#         j = 0
#         result_buy = []
#         for b_index, b_row in pd_buy.iterrows():
#             while j < len(sell_indexes) - 1 and sell_indexes[j] <= b_index:
#                 j += 1
#             sell_matching = self.buy_2_sell["all"]
#             if b_row['filter'] in self.buy_2_sell.keys():
#                 sell_matching = self.buy_2_sell[b_row['filter']]
#
#             found_flag = False
#
#             for k in range(j, len(sell_indexes)):
#                 s_index = sell_indexes[k]
#                 s_rows = self.df_sell.loc[s_index:s_index]
#
#                 # Check T+3
#                 if s_index - b_index < 3:
#                     if s_index == sell_indexes[-1]:  # s_index is hold's index
#                         s_row = s_rows.iloc[-1]
#                         b_row['P_Hold'] = (s_row['Close'] / b_row['Close'] - 1) * 100
#                         b_row['Sell_time'] = s_row['time']
#                         b_row['Sell_filter'] = 'Hold'
#                         b_row['Sell_profit'] = b_row['P_Hold']
#                     continue
#
#                 # cutloss
#                 cutloss_threshold = b_row['Close'] * (1 - self.cutloss)
#                 if self.df_all.iloc[b_index:s_index + 1]['Close'].min() <= cutloss_threshold:
#                     # find cutloss time
#                     cutloss_indexes = list(self.df_all.iloc[b_index:s_index + 1][(
#                             self.df_all.iloc[b_index:s_index + 1]['Close'] < cutloss_threshold)].index)
#
#                     for c_index in cutloss_indexes:
#                         if c_index - b_index >= 3:  # T + 3
#                             next_open = self.df_all.iloc[c_index + 1]['Open'] if c_index + 1 <= self.df_all.shape[
#                                 0] - 1 else \
#                                 self.df_all.iloc[c_index]['Close']
#
#                             b_row['P_cutloss'] = (next_open / b_row['Close'] - 1) * 100
#                             b_row['Sell_time'] = self.df_all.iloc[c_index]['time']
#                             b_row['Sell_filter'] = 'cutloss'
#                             b_row['Sell_profit'] = b_row['P_cutloss']
#                             found_flag = True
#                             break
#                     if found_flag:
#                         break
#
#                 # Sell
#                 # move hold to last
#                 if len(s_rows) > 1:
#                     if s_rows.iloc[0]['Sell_filter'] == 'Hold':
#                         s_rows = s_rows.iloc[1:]._append(s_rows.iloc[0]).reset_index(drop=True)
#
#                 for idx in range(len(s_rows)):
#                     s_row = s_rows.iloc[idx]
#                     if s_row['Sell_filter'] not in sell_matching:
#                         continue
#
#                     next_open = self.df_all.iloc[s_index + 1]['Open'] if s_index + 1 <= self.df_all.shape[0] - 1 \
#                         else s_row['Close']
#
#                     b_row[f"P_{s_row['Sell_filter']}"] = (next_open / b_row['Close'] - 1) * 100
#                     b_row["Sell_time"] = s_row['time']
#                     b_row['Sell_filter'] = s_row['Sell_filter']
#                     b_row['Sell_profit'] = b_row[f"P_{s_row['Sell_filter']}"]
#                     found_flag = True
#
#                 if found_flag:
#                     break
#
#             result_buy.append(b_row)
#
#         result_buy = [data for data in result_buy if not data.empty]
#         pd_buy = pd.concat(result_buy, axis=1).T if result_buy else pd_buy
#
#         return pd_buy
#
#     def eval_short_sell(self):
#         deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'buy_filter', 'buy_time', 'filter',
#                      'profit_vni']
#         l_pd_deal = []
#         sell_reasons = list(self.df_sell['Sell_filter'].unique())
#
#         for sell_reason in sell_reasons:
#             sell_reason_indexes = list(self.df_sell[self.df_sell['Sell_filter'] == sell_reason].index)
#
#             buy_indexes = list(self.df_buy.index)
#             buy_reasons = list(self.df_buy['filter'])
#
#             result = self.find_short(self.df_all[['time', 'Close', 'Open', 'VNINDEX']], sell_reason_indexes,
#                                      buy_indexes, buy_reasons, self.cutloss)
#
#             deal_time = self.df_all.loc[result[0]]['time'].values
#             deal_buy_time = self.df_all.loc[result[2]]['time'].values
#             # (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
#             #  list_deal_result, list_deal_market_price)
#             pd_deal_pa = pd.DataFrame({
#                 'time': deal_time,
#                 'sell_price': result[1],
#                 'buy_price': result[3],
#                 'profit': result[4],
#                 'buy_filter': result[5],
#                 'buy_time': deal_buy_time,
#                 'profit_vni': result[6],
#             })
#
#             pd_deal_pa.insert(0, 'ticker', self.ticker)
#             pd_deal_pa['filter'] = sell_reason
#
#             l_pd_deal.append(pd_deal_pa)
#         pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)
#         pd_deal = pd_deal.merge(self.df_sell[['time', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P1Y', 'P2Y']],
#                                 on='time', how='left')
#         pd_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
#         return pd_deal
#
#     def eval_ai_deal(self):
#         pass
#
#     def init_ai_model(self, kwargs=None):
#         model, threshold, c_feature, c_label = kwargs
#         self.model = model
#         self.threshold = threshold
#         self.cfeature = c_feature
#         self.c_label = c_label
#         self.cfeature = ['time_x', 'Open_x', 'High_x', 'Low_x', 'Close_x', 'Volume_x',
#                          'ticker_x', 'ICB_Code_x', 'Price_x', 'C_H1M_x',
#                          'VN30_MACDdiff_T1W_y', 'VNINDEX_PE_y', 'VNINDEX_LNST_y', 'T1W_y',
#                          'T2W_y', 'T1M_y', 'T3M_y', 'sell_index', 'holding_session',
#                          'sell_profit']
#
#     def ai_hit(self):
#         duration = 120
#
#         # df_buy['buy_index'] = df_buy.index
#
#         df_hit = []
#         df_all = self.df_all.copy()
#         df_buy = self.df_buy.copy()
#         df_buy['buy_index'] = df_buy.index
#         df_all['sell_index'] = df_all.index
#         df_all['buy_index'] = np.nan
#         for idx_buy, curr_buy in self.df_buy.iterrows():
#             start = df_all[df_all['time'] == curr_buy['time']].index[0] + 3
#             end = start + duration if start + duration < df_all.shape[0] else df_all.shape[0]
#
#             df_sell = df_all.iloc[start:end].copy()
#             df_sell['buy_index'] = idx_buy
#             df_sell.loc[:, 'holding_session'] = list(range(3, 3 + len(df_sell)))
#             df_sell.loc[:, 'sell_profit'] = ((df_sell['Open_1D'] / curr_buy['Open_1D']) - 1)
#             df_hit.append(df_sell)
#             # df_hit.append(df_sell.assign(**curr_buy))
#
#         df_sell = pd.concat(df_hit, axis=0).reset_index(drop=True)
#
#         temp_cfeature = list(set([c_f.replace('_x', '').replace('_y', '') for c_f in self.cfeature]))
#         b_cfeature = [f for f in temp_cfeature if f in df_buy.columns]
#         s_cfeature = [f for f in temp_cfeature if f in df_sell.columns]
#         df_hit = df_buy[b_cfeature + ['buy_index', 'time']].merge(df_sell[s_cfeature + ['buy_index', 'time']],
#                                                                   on=['buy_index'], how='left', suffixes=('_x', '_y'))
#
#         # Predict
#         # ignore_cols = ['time_x', 'time_y', 'time', 'ticker', 'tvt', 'Open_1D_x', 'Open_1D_y', 'sell_profit']
#         # cname_label = ['pattern_label']
#         df_hit['pred'] = self.model.predict_proba(df_hit[self.cfeature])[:, 1]
#
#         # Find first hit
#         df_result = []
#         for idx_buy, d_buy in df_buy.iterrows():
#             df_label = df_hit.loc[(df_hit['buy_index'] == idx_buy)].copy()
#
#             d = {}
#             for i in range(df_label.shape[0]):
#                 d_sell = df_label.iloc[i]
#                 if d_sell['pred'] > self.threshold:
#                     d = {
#                         'ticker': d_sell['ticker'],
#                         'buy_index': d_sell['buy_index'],
#                         'sell_index': d_sell['sell_index'],
#                         'time': d_sell['time_x'],
#                         'sell_time': d_sell['time_y'],
#                         'holding_session': d_sell['holding_session'],
#                         'profit': d_sell['sell_profit'],
#                         'sell_reason': 'ml'
#                     }
#                     break
#
#                 if i == df_label.shape[0] - 1:
#                     d = {
#                         'ticker': d_sell['ticker'],
#                         'buy_index': d_sell['buy_index'],
#                         'sell_index': d_sell['sell_index'],
#                         'time': d_sell['time_x'],
#                         'sell_time': d_sell['time_y'],
#                         'holding_session': d_sell['holding_session'],
#                         'profit': d_sell['sell_profit'],
#                         'sell_reason': np.nan
#                     }
#
#             df_result.append(pd.DataFrame(data=d, index=[d_buy['buy_index']]))
#         df_result = pd.concat(df_result, axis=0).reset_index(drop=True)
#
#         return df_result
#         # Predict
#
#         # df_map = df_sell[['time', 'ticker', 'buy_index', 'pattern_label']].copy()
#         # df_map['buy_index'] = df_map['buy_index'].apply(lambda x: x.split(',')).copy()
#         # df_map = df_map.explode('buy_index')
#         # df_map['buy_index'] = df_map['buy_index'].astype(int)
#         # df_map['sell_index'] = df_map.index
#         # df_map.sort_values(['buy_index', 'sell_index'], inplace=True)
#         # df_map = df_map[['buy_index', 'sell_index', 'pattern_label']]
#         # df_map.reset_index(drop=True, inplace=True)
#         # df_map = df_map.merge(df_buy[['buy_index', 'time', 'ticker']], on='buy_index', how='left')
#         # df_sell.drop(columns=['buy_index'], inplace=True)
#
#     def sell_signals_by_ml(self):
#         """
#         Use xgboots model to predict sell signal
#         input : xgboots model
#         output:
#         pd_sell:
#         """
#
#         s_cols = ['time', 'Close', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P1Y', 'P2Y']
#
#         cname_features = []
#         self.df_all['pred'] = self.model.predict_proba(self.df_all[cname_features])[:, 1]
#
#         pd_sell = self.df_all[self.df_sell['pred'] > self.threshold].sort_values('time', ascending=True).copy()
#
#         for period in ['1W', '2W', '3W', '1M', '2M', '3M', '1Y', '2Y']:
#             pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"C{period}"]) * (
#                     pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
#                                             pd_sell[f"H{period}"] >= 1 + self.cutloss)
#
#         pd_sell = pd_sell[s_cols]
#         return pd_sell
#
#     def sell_signals(self):
#         # Apply sell filters
#         s_cols = ['time', 'Close', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P1Y', 'P2Y']
#
#         now = self.df_all.iloc[-1:].copy()
#         now['Sell_filter'] = 'Hold'
#
#         sell_data = [now]
#         for f in self.dictFilter:
#             if f.startswith("~") and (f in filter_functions):
#                 sell_data.append(eval_lookback_filter(f, self.df_all))
#
#         for f in self.dictFilter:
#             if f.startswith('~'):
#                 try:
#                     pd_Sell_filtered = self.df_all.query(f'({self.dictFilter[f]})').copy()
#                     pd_Sell_filtered['Sell_filter'] = f[1:]
#                     sell_data.append(pd_Sell_filtered)
#                 except Exception as e:
#                     pass
#                     # print(f"{self.ticker}--{f[1:]}--error: {e}")
#
#         pd_sell = pd.concat(sell_data, axis=0).sort_values('time', ascending=True)
#         for period in ['1W', '2W', '3W', '1M', '2M', '3M', '1Y', '2Y']:
#             # pd_sell[f"P{period}"] = 100. * (pd_sell[f"C{period}"] - 1) * (
#             #         pd_sell[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
#             #                                 pd_sell[f"L{period}"] <= 1 - self.cutloss)
#             pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"C{period}"]) * (
#                     pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
#                                             pd_sell[f"H{period}"] >= 1 + self.cutloss)
#
#         pd_sell = pd_sell[s_cols]
#         return pd_sell
#
#     def buy_signals(self):
#         # Apply buy filters
#         buy_data = []
#         for f in self.dictFilter:
#             if f.startswith("_") and (f in filter_functions):
#                 buy_data.append(eval_lookback_filter(f, self.df_all))
#
#         for f in self.dictFilter:
#             if not (f.startswith('_')) or (f in filter_functions):
#                 continue
#             try:
#                 pd_buy_filtered = self.df_all.query(f'({self.dictFilter[f]})').copy()
#                 pd_buy_filtered['filter'] = f[1:]
#                 buy_data.append(pd_buy_filtered)
#             except Exception as e:
#                 pass
#                 # print(f"{self.ticker}--{f[1:]}--error: {e}")
#
#         _buy_data = [data for data in buy_data if not data.empty]
#         pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]
#
#         # for period in ['1W', '1M', '3M', '1Y', '2Y']:
#         #     pd_buy[f"P{period}"] = 100. * (pd_buy[f"C{period}"] - 1) * (
#         #             pd_buy[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
#         #                                    pd_buy[f"L{period}"] <= 1 - self.cutloss)
#         pd_buy['hit'] = 1
#         pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])
#
#         return pd_buy
#
#     def buy_to_sell(self):
#         all_filters = ["Hold"]
#         for f in self.dictFilter:
#             if f.startswith('~'):
#                 all_filters.append(f[1:])
#
#         buy2sell = {
#             "all": all_filters
#         }
#
#         for f in self.dictFilter:
#             if f.startswith("$"):
#                 sell_key = f[1:].replace("_", "")
#                 value = [x.replace("~", "").strip() for x in self.dictFilter[f].split(",")]
#                 value.append("Hold")
#                 buy2sell[sell_key] = value
#
#         return buy2sell
#
#     def get_buy_signals(self):
#         return self.df_buy
#
#     def get_sell_signals(self):
#         return self.df_sell
#
#     def get_deal_full(self, df_deal):
#         cols = ['time']
#         for col in self.df_all.columns:
#             if col not in df_deal.columns:
#                 cols.append(col)
#         df_deal = df_deal.merge(self.df_all[cols], on=['time'], how='left')
#         df_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
#         return df_deal
#
#     @staticmethod
#     def find_sell(df_price: pd.DataFrame, list_buy_index: list, list_sell_index: list, list_sell_reason: list, cutloss):
#         """
#         Find sell for pair (buy_pattern - sell reason)
#         Arguments:
#         - df_price: data frame with columns ymd, Close, Open
#         - list_buy_index: list of Buy indexes in df_price
#         - list_sell_index: list of Sell indexes in df_price
#         - list_sell_reason: list of sell reasons
#         - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
#         Output:
#         - list_deal_index: list of first buy indexes for each deal
#         - list_deal_buy_price:  list of buying price (open price of T+1)
#         - list_deal_sell_index: list of first sell indexes for each deal
#         - list_deal_sell_price: list of selling price (open price of T+1)
#         - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
#         - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
#         """
#         # Initialize signal list with "none"
#         list_signal = [None] * df_price.shape[0]
#
#         # Mark sell signals with corresponding reasons
#         for i, j in zip(list_sell_index, list_sell_reason):
#             list_signal[i] = j
#
#         # Mark buy signals
#         for i in list_buy_index:
#             list_signal[i] = "buy"
#
#         # Initialize output lists
#         list_deal_index = []
#         list_deal_buy_price = []
#         list_deal_sell_index = []
#         list_deal_sell_price = []
#         list_deal_profit = []
#         list_deal_result = []
#         list_deal_market_price = []
#
#         current_status = None  # Status can be None or "buy"
#
#         for i in range(df_price.shape[0]):
#             close = df_price['Close'].iloc[i]
#             price = df_price['Open'].iloc[i + 1] if (i < df_price.shape[0] - 1) else close
#
#             # Look for the buy signal
#             if current_status is None:
#                 if list_signal[i] != "buy":
#                     continue
#                 if i < df_price.shape[0] - 1:  # Do not evaluate deal happens today
#                     list_deal_index.append(i)
#                     list_deal_buy_price.append(price)
#                     current_status = "buy"
#             # Look for the cutloss or sell signal
#             elif current_status == "buy":
#                 if i == df_price.shape[0] - 1:  # Reaching the end
#                     list_deal_sell_index.append(i)
#                     list_deal_sell_price.append(price)
#                     list_deal_result.append("hold")
#                     list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#                     continue
#
#                 if i < list_deal_index[-1] + 3:  # T+3
#                     continue
#
#                 if close < list_deal_buy_price[-1] * (1 - cutloss):  # Cutloss
#                     list_deal_sell_index.append(i)
#                     list_deal_sell_price.append(price)
#                     list_deal_result.append("cutloss")
#                     list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#                     continue
#
#                 if list_signal[i] != "buy" and list_signal[i] is not None:  # Sell signal
#                     list_deal_sell_index.append(i)
#                     list_deal_sell_price.append(price)
#                     list_deal_result.append(list_signal[i])
#                     list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
#
#                     current_status = None
#                     list_deal_market_price.append((df_price['VNINDEX'].iloc[i + 1] / df_price['VNINDEX'].iloc[
#                         list_deal_index[-1] + 1] - 1.0) * 100)
#
#         return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
#                 list_deal_result, list_deal_market_price)
#
#     @staticmethod
#     def find_short(df_price: pd.DataFrame, list_sell_index: list, list_buy_index: list, list_buy_reason: list, cutloss):
#         """
#         Find buy for pair (sell_pattern - buy reason)
#         Arguments:
#         - df_price: data frame with columns ymd, Close, Open
#         - list_sell_index: list of Sell indexes in df_price
#         - list_buy_index: list of Buy indexes in df_price
#         - list_buy_reason: list of buy reasons
#         - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
#         Output:
#         - list_deal_index: list of first sell indexes for each deal
#         - list_deal_sell_price: list of selling price (open price of T+1)
#         - list_deal_buy_index: list of first buy indexes for each deal
#         - list_deal_buy_price: list of buying price (open price of T+1)
#         - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
#         - list_deal_result: list of buy reason for each group (cutloss, MA2, MA3, S13, hold)
#         """
#
#         skip_period = 22
#         limit_period = 22 * 3
#         list_signal = [None] * df_price.shape[0]
#
#         # Mark buy signals with corresponding reasons
#         for i, j in zip(list_buy_index, list_buy_reason):
#             list_signal[i] = j
#
#         # Mark sell signals
#         for i in list_sell_index:
#             list_signal[i] = "sell"
#
#         # Initialize output lists
#         list_deal_index = []
#         list_deal_sell_price = []
#         list_deal_buy_index = []
#         list_deal_buy_price = []
#         list_deal_profit = []
#         list_deal_result = []
#         list_deal_market_price = []
#
#         current_status = None  # Status can be None or "sell"
#
#         for i in range(df_price.shape[0]):
#             close = df_price['Close'].iloc[i]
#             price = df_price['Open'].iloc[i + 1] if (i < df_price.shape[0] - 1) else close
#
#             # Look for the sell signal
#             if current_status is None:
#                 if list_signal[i] != "sell":
#                     continue
#                 if i < df_price.shape[0] - 1:
#                     list_deal_index.append(i)
#                     list_deal_sell_price.append(price)
#                     current_status = "sell"
#
#             # Look for the cutloss or buy signal
#             elif current_status == "sell":
#
#                 if i > list_deal_index[-1] + limit_period:  # Reaching the time limit
#                     list_deal_result.append("endperiod")
#                     list_deal_buy_index.append(i)
#                     list_deal_buy_price.append(price)
#                     # list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
#                     list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
#
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#                     continue
#
#                 if i == df_price.shape[0] - 1:  # Reaching the end
#                     list_deal_result.append("hold")
#                     list_deal_buy_index.append(i)
#                     list_deal_buy_price.append(price)
#                     # list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
#                     list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
#
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#                     continue
#
#                 if i < list_deal_index[-1] + 3:  # T+3
#                     continue
#                 # if i < list_deal_index[-1] + skip_period:
#                 #     continue
#
#                 if close > list_deal_sell_price[-1] * (1 + cutloss):  # Cutloss
#                     list_deal_result.append("cutloss")
#                     list_deal_buy_index.append(i)
#                     list_deal_buy_price.append(price)
#                     # list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
#                     list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
#
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#                     continue
#
#                 if list_signal[i] != "sell" and list_signal[i] is not None:  # Buy signal
#                     list_deal_result.append(list_signal[i])
#                     list_deal_buy_index.append(i)
#                     list_deal_buy_price.append(price)
#                     # list_deal_profit.append(list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0)
#                     list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
#                     current_status = None
#                     list_deal_market_price.append(
#                         (df_price['VNINDEX'].iloc[i] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)
#
#         return (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
#                 list_deal_result, list_deal_market_price)


class PreProcess:
    def __init__(self):
        self.dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'count_cutloss': 'count',
                     'n_month': 'sum',
                     'count_hold': 'count', 'count_win': 'count', 'count_loss': 'count', 'count_sell': 'count',
                     'n_quarter': 'sum', 'sum_profit': 'sum',
                     }

    def group_by(self, df: pd.DataFrame, by: list):
        df = df.groupby(by, as_index=False) \
            .agg({f: self.dAgg.get(f, 'mean') for f in list(df.columns) if (
                f not in ['filter', 'ticker', 'time', 'quarter', 'month', 'week', 'Close', 'sell_time',
                          'sell_filter', 'Sell_filter', 'buy_time', 'buy_filter', 'half_of_year']) and (
                          "time" not in f)})
        return df

    def deals(self, pd_deal):
        pd_deal[['p_hold', 'p_cutloss']] = np.nan
        for idx, result in enumerate(pd_deal['sell_filter'].values):
            if f'p_{result}' not in pd_deal.columns:
                pd_deal[f'p_{result}'] = np.nan
            pd_deal.loc[idx, f'p_{result}'] = pd_deal.loc[idx, 'profit']

        p_sell_columns = [col for col in pd_deal.columns if
                          (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
        pd_deal["p_sell_pattern"] = pd.concat([pd_deal[col].dropna() for col in p_sell_columns])

        pd_deal['p_win'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] > 0, np.nan)
        pd_deal['p_loss'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] <= 0, np.nan)

        pd_deal['count_win'] = pd_deal['p_win'].copy()
        pd_deal['count_loss'] = pd_deal['p_loss'].copy()
        pd_deal['count_hold'] = pd_deal['p_hold'].copy()
        pd_deal['count_cutloss'] = pd_deal['p_cutloss'].copy()

        pd_deal["quarter"] = pd.to_datetime(pd_deal['time']).dt.to_period('Q').astype(str)
        pd_deal['holding_period'] = (pd.to_datetime(pd_deal['sell_time']) - pd.to_datetime(pd_deal['time'])).dt.days
        pd_deal['half_of_year'] = pd_deal['time'].str[:4] + "H" + pd.to_datetime(pd_deal['time']).dt.month.gt(6).add(
            1).astype(str)

        pd_deal['deal'] = 1
        for ticker in pd_deal['ticker'].unique():
            pd_deal.loc[pd_deal['ticker'] == ticker, 'corr'] = pd_deal[pd_deal['ticker'] == ticker]['profit'].corr(
                pd_deal[pd_deal['ticker'] == ticker]['profit_vni'])

        for b_pattern in pd_deal['filter'].unique():
            pd_deal.loc[pd_deal['filter'] == b_pattern, 'entropy'] = self.calculate_entropy(
                pd_deal[pd_deal['filter'] == b_pattern]['half_of_year'])
            pd_deal.loc[pd_deal['filter'] == b_pattern, 'n_half'] = \
                pd_deal[pd_deal['filter'] == b_pattern]['half_of_year'].unique().shape[0]

        return pd_deal

    def hits(self, pd_b):
        pd_b['week'] = self.convert_to_yyyy_w(pd_b['time'])
        pd_b['P_Sell'] = pd_b[(pd_b['Sell_filter'] != 'Hold') & (pd_b['Sell_filter'] != 'cutloss')]['Sell_profit']
        pd_b['count_win'] = pd_b['P_Sell'].where(pd_b['P_Sell'] > 0, np.nan)
        pd_b['count_loss'] = pd_b['P_Sell'].where(pd_b['P_Sell'] <= 0, np.nan)
        pd_b['count_hold'] = pd_b['P_Hold'].copy()
        pd_b['count_cutloss'] = pd_b['P_cutloss'].copy()
        pd_b['holding_period'] = (pd.to_datetime(pd_b['Sell_time']) - pd.to_datetime(pd_b['time'])).dt.days

        return pd_b

    def shortsell(self, pd_short):
        pd_short[['p_hold', 'p_cutloss']] = np.nan
        for idx, result in enumerate(pd_short['buy_filter'].values):
            if f'p_{result}' not in pd_short.columns:
                pd_short[f'p_{result}'] = np.nan
            pd_short.loc[idx, f'p_{result}'] = pd_short.loc[idx, 'profit']

        p_buy_columns = [col for col in pd_short.columns if
                         (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
        pd_short["p_buy_pattern"] = pd.concat([pd_short[col].dropna() for col in p_buy_columns])

        pd_short['p_win'] = pd_short['p_buy_pattern'].where(pd_short['p_buy_pattern'] > 0, np.nan)
        pd_short['p_loss'] = pd_short['p_buy_pattern'].where(pd_short['p_buy_pattern'] <= 0, np.nan)

        pd_short['count_win'] = pd_short['p_win'].copy()
        pd_short['count_loss'] = pd_short['p_loss'].copy()
        pd_short['count_hold'] = pd_short['p_hold'].copy()
        pd_short['count_cutloss'] = pd_short['p_cutloss'].copy()

        pd_short['holding_period'] = (pd.to_datetime(pd_short['buy_time']) - pd.to_datetime(pd_short['time'])).dt.days
        # pd_short["quarter"] = pd.to_datetime(pd_short['time']).dt.to_period('Q').astype(str)
        # pd_short['half_of_year'] = pd_short['time'].str[:4] + "H" + pd.to_datetime(pd_short['time']).dt.month.gt(6).add(
        #     1).astype(str)

        pd_short['deal'] = 1
        for ticker in pd_short['ticker'].unique():
            pd_short.loc[pd_short['ticker'] == ticker, 'corr'] = pd_short[pd_short['ticker'] == ticker]['profit'].corr(
                pd_short[pd_short['ticker'] == ticker]['profit_vni'])

        # for s_pattern in pd_short['filter'].unique():
        #     pd_short.loc[pd_short['filter'] == s_pattern, 'entropy'] = calculate_entropy(
        #         pd_short[pd_short['filter'] == s_pattern]['half_of_year'])
        #     pd_short.loc[pd_short['filter'] == s_pattern, 'n_half'] = \
        #         pd_short[pd_short['filter'] == s_pattern]['half_of_year'].unique().shape[0]
        return pd_short

    @staticmethod
    def calculate_entropy(df):
        # Count value
        counts = df.value_counts()
        # Count probabilities
        probabilities = counts / counts.sum()
        # entropy
        entropy = -np.sum(probabilities * np.log2(probabilities))

        return entropy

    @staticmethod
    def convert_to_yyyy_w(date_str):
        """
        Convert date to 'yyyy/w'.
        """

        def convert_date(date):
            year = date.year
            # month = date.month
            week = date.isocalendar()[1]
            return f"{year}-{week:02}"

        date_series = pd.to_datetime(date_str)
        return date_series.apply(convert_date)


class Simulation:
    """
    Simulate trading strategy
    """

    def __init__(self, df_deals, start_date='2017-01-01', initial_assets=1e8, max_deals=10, num_proc=5):
        self.df_deals = df_deals.copy().sort_values('time', ascending=True).reset_index(drop=True)
        self.start_date = start_date
        self.initial_assets = initial_assets
        self.max_deals = max_deals
        self.num_proc = num_proc

    def shuffle_by_date(self, seed):
        df_list = []
        for date, group in self.df_deals.groupby('time'):
            df_list.append(group.sample(frac=1, random_state=seed))
        self.df_deals = pd.concat(df_list, ignore_index=True)

    def simulation(self, i):
        result = {}
        np.random.seed(i)
        self.shuffle_by_date(i)
        for b_pattern in self.df_deals['filter'].unique():
            result[b_pattern] = self.simulate(self.df_deals[self.df_deals['filter'] == b_pattern], self.start_date,
                                              self.initial_assets, self.max_deals)
        # print(result)
        return result

    def all_simulation(self, i):
        np.random.seed(i)
        self.shuffle_by_date(i)
        result = self.simulate(self.df_deals, self.start_date,
                               self.initial_assets, self.max_deals)

        return result

    @staticmethod
    def simulate(deals, start_date, initial_assets, max_deals):
        """
        Simulate trading strategy
        input: deals, start_date, initial_assets
        deals: pd.DataFrame (ticker, time, sell_time, ticker, profit)
        """
        available_assets = initial_assets
        active_deals = []
        completed_deals = []
        miss_deals = []
        profit = 0
        # time_in_market
        start_time = []
        end_time = []

        sell_time_remove = []
        for i, deal in deals.iterrows():
            # process complete deal
            keep_deals = []
            # sell_time_remove = []
            for j, active_deal in enumerate(active_deals[:]):
                if active_deal['sell_time'] <= deal['time']:
                    profit += active_deal['profit']
                    # for tax
                    available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.998
                    completed_deals.append(active_deal)
                    sell_time_remove.append(active_deal['sell_time'])
                else:
                    keep_deals.append(active_deal)

            active_deals = keep_deals.copy()

            # end in time_in_market
            if (not active_deals) and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Check the budget for the new deal
            if len(active_deals) < max_deals:
                investment_amount = (available_assets / (max_deals - len(active_deals)))
                deal['investment_amount'] = investment_amount
                # for tax
                available_assets -= investment_amount * 1.001

                # start in time_in_market
                if not active_deals:
                    start_time.append(deal['time'])
                active_deals.append(deal)

            else:
                miss_deals.append(deal)

        # sell_time_remove = []
        for active_deal in (active_deals[:]):
            profit += active_deal['profit']
            available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100))
            completed_deals.append(active_deal)
            sell_time_remove.append(active_deal['sell_time'])

        today = datetime.today().strftime('%Y-%m-%d')
        end_time.append(max(sell_time_remove))

        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(today) - pd.to_datetime(start_date)).days

        utilization = sum([(pd.to_datetime(deal['sell_time']) - pd.to_datetime(deal['time'])).days for deal in
                           completed_deals]) / (total_time * max_deals)

        return {
            'match_deals': len(completed_deals),
            'total_time': total_time,
            'time_in_market': time_in_market,
            'profit': profit / len(completed_deals),
            'cash_profit': ((available_assets / initial_assets) - 1) * 100,
            'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
            'available_assets': available_assets,
            'utilization': utilization,
            # 'completed_deals': pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame(),
            # 'miss_deals': pd.concat(miss_deals, axis=1).T if miss_deals else pd.DataFrame(),
        }

    @staticmethod
    def convert_to_dict(serial_data):
        result = {}

        for item in serial_data.items():
            key, value = item
            main_key, sub_key = key.split('.', 1)

            if main_key not in result:
                result[main_key] = {}

            result[main_key][sub_key] = value

        return result

    def run(self, iterate=10):
        all_results = []
        for time in range(iterate):
            all_results.append(self.simulation(time))

        df = pd.json_normalize(all_results)
        df_result = pd.Series(index=df.columns)

        for col in df.columns:
            if 'return' in col:
                df_result[col] = df[col].mean()
                df_result[col.replace('return', 'return_std')] = df[col].std()
            df_result[col] = df[col].mean()

        return self.convert_to_dict(df_result)

    def run_fast(self, iterate=10):
        with Pool(processes=self.num_proc) as pool:
            # all_results = pool.map(self.simulation, [None] * iterate)
            all_results = pool.map(self.simulation, list(range(iterate)))

        df = pd.json_normalize(all_results)
        df_result = pd.Series(index=df.columns)

        for col in df.columns:
            if 'return' in col:
                df_result[col] = df[col].mean()
                df_result[col.replace('return', 'return_std')] = df[col].std()
            df_result[col] = df[col].mean()

        return self.convert_to_dict(df_result)

    def all_run(self, iterate=10):
        with Pool(processes=self.num_proc) as pool:
            all_results = pool.map(self.all_simulation, list(range(iterate)))

        df = pd.json_normalize(all_results)
        df_result = pd.Series(index=df.columns)

        for col in df.columns:
            if 'return' in col:
                df_result[col] = df[col].mean()
                df_result[col.replace('return', 'return_std')] = df[col].std()
            df_result[col] = df[col].mean()
        return df_result


def submit_job(params):
    resp = requests.post("http://localhost:8000/jobs/", json=params)
    return resp.json()["job_id"]

def listen_progress(job_id):
    ws = websocket.WebSocketApp(f"ws://localhost:8000/ws/{job_id}",
        on_message=lambda ws, msg: st.write(msg))
    ws.run_forever()

job_id = submit_job({...})
st.write(f"Job ID: {job_id}")
listen_progress(job_id)
# Khi xong, GET /jobs/{job_id} để lấy path file kết quả


if __name__ == "__main__":
    # filter = {
    #     "Init": "(Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')",
    #     "_BKMA200": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>150) & (MA50/MA200>1) & (MA10/MA200<1.2) & (ROE5Y >0.08) & (PE <20)",
    #     "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
    #     "~MA3": "(PE<15)  &  (Close/MA200<1)  &  (Close_T1/MA200_T1>1)",
    #     "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
    #     "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)",
    #     # "$BKMA200": "MA3",
    #     "_TL3M": "(HI_3M_T1/LO_3M_T1<1.3) &  (Volume>Volume_3M_P90)  & (Close > 1.2*HI_3M_T1)",
    #     "_VolMax5Y": "(Volume >= Volume_Max5Y) & (ROE5Y>0.1)&(PE<20)& (NP_P0 > 1.2*NP_P4) & (PE >0) & (PCF>0)& (Price*Volume>1000e+6)",
    #     "_T3P4": "(Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01') & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R>0)&(PE<10)&(C_H2Y<0.7) & (C_H2Y>0.5)",
    #     "_BKVOL5Y": "(Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01') & (Volume >= Volume_Max5Y) & (ROE5Y>0.05)  & (NP_P0 > NP_P1*1.2) & (PE<20) & (FSCORE>=5)"
    #
    # }

    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # current_dir = current_dir.replace("/webui", "")
    # os.chdir(current_dir)
    # sys.path.insert(0, current_dir)
    #
    # FPATH = 'ticker_v1a'
    # ticker = 'HPG'
    # pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
    #
    # filter = {
    #     "Init": "(Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')",
    #     "~MA2": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01') & (Close > 10000) &(PE>= 15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1) &  (Close/Open < 1) &  (NP_P0 < NP_P1 | NP_P0 < 0) & (Volume>=Volume_1M)",
    #     "_PS": "(MA20/MA50>1) & (MA20_T1/MA50_T1<1) & (Close/Close_T1>1.01) | (NP_P0/NP_P1 >1.01 )",
    #     "~VAPM1": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01') & (Close > 10000)&(PE>= 15) &  (NP_P0 /NP_P1 < 0.85 | NP_P0 < 0) & (Close < VAP1M) & (Volume>=Volume_1M)"
    #
    # }
    # eval_ticker = TickerEval(ticker, pdxx, filter, cutloss=0.15)
    # res_d = eval_ticker.eval_by_deal()
    # res_b = eval_ticker.get_deal_full(res_d)
    # res = eval_ticker.evaluation()
    # res = eval_ticker.eval_short_sell()

    # pd_deal = pd.read_csv('pd_deal.csv').reset_index(drop=True)
    # simulation = Simulation(pd_deal, start_date='2017-01-01', initial_assets=1e8, max_deals=10)
    import time

    #
    # now = time.time()
    # result = simulation.all_run(10)
    # # print(result)
    # # print(time.time() - now)
    # # now = time.time()
    # # result = simulation.run_fast(10)
    # print(result)
    # print(time.time() - now)
    # 1.0 Map buy to all sell during 120-D
