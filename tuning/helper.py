import json
import pickle
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)


def export_filter_to_webui_profile(result):
    dict_filter = json.load(open('filter.json', 'r'))
    dict_filter = json.dumps(dict_filter)
    with open("filter_string.json", "w") as config_file:
        json.dump(dict_filter, config_file)


def parse_param_for_pattern_weight(trials):
    result = {}
    for i, t in enumerate(trials.trials):
        if t['result']['loss'] < -52:
            w_bkma200 = t['misc']['vals']['w_bkma200'][0]
            w_volmax5y = t['misc']['vals']['w_volmax5y'][0]
            w_tl3m = t['misc']['vals']['w_tl3m'][0]
            w_rsilow30 = t['misc']['vals']['w_rsilow30'][0]
            w_underbv = t['misc']['vals']['w_underbv'][0]
            w_supergrowth = t['misc']['vals']['w_supergrowth'][0]
            w_surpriseearning = t['misc']['vals']['w_surpriseearning'][0]
            w_conservative = t['misc']['vals']['w_conservative'][0]

            # w_ma2 = t['misc']['vals']['w_ma2'][0]
            w_ma21 = t['misc']['vals']['w_ma21'][0]
            w_ma31 = t['misc']['vals']['w_ma31'][0]
            w_ma41 = t['misc']['vals']['w_ma41'][0]
            w_s13 = t['misc']['vals']['w_s13'][0]
            w_selllowgrowth = t['misc']['vals']['w_selllowgrowth'][0]
            w_sellresistance3m = t['misc']['vals']['w_sellresistance3m'][0]
            w_sellresistance1m = t['misc']['vals']['w_sellresistance1m'][0]
            w_sellbv = t['misc']['vals']['w_sellbv'][0]
            w_sellbv2 = t['misc']['vals']['w_sellbv2'][0]
            w_sellpe = t['misc']['vals']['w_sellpe'][0]
            # w_sellconservative = t['misc']['vals']['w_sellconservative'][0]
            w_sellvolmax = t['misc']['vals']['w_sellvolmax'][0]
            w_beardvg2 = t['misc']['vals']['w_beardvg2'][0]

            result[i] = {
                'si_return': t['result']['loss'],
                'weight': t['misc']['vals']

            }

    # dict_filter = json.dumps(result)
    with open("tuning/filter_weight.json", "w") as config_file:
        json.dump(result, config_file)


def parse_param_for_hyperopt_sell_param(trials):
    sell_filter = {
        "Init": "(Volume*Price >10e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (Price > 10000)",
        "_PS1": "Price < 0",
    }

    a = []
    for i, t in enumerate(trials.trials):
        if t['result']['loss'] < -250:
            w1 = t['misc']['vals']['w1'][0]
            w2 = t['misc']['vals']['w2'][0]
            w3 = t['misc']['vals']['w3'][0]
            w4 = t['misc']['vals']['w4'][0]
            w5 = t['misc']['vals']['w5'][0]
            w6 = t['misc']['vals']['w6'][0]
            w7 = t['misc']['vals']['w7'][0]
            # w8 = t['misc']['vals']['w8'][0]

            pattern = "{Init} &" + f"(PB > {w1}*PB_MA5Y + {w2}*PB_SD5Y) & (NP_P0 /NP_P1 < {w3})  &  (Close < {w4}*Res_1Y)  & (Volume  > {w5}*Volume_3M_P50)  & (Close_T1W > {w6}*VAP1M)  & (D_RSI > {w7})"

            sell_filter[f'~Sell{i}'] = pattern
            a.append(pattern)

    dict_filter = json.dumps(sell_filter)
    with open("tuning/filter_string.json", "w") as config_file:
        json.dump(dict_filter, config_file)


def parse_param_for_hyperopt_buy_param(trials):
    # for tuning weights

    # for tuning pattern
    sell_filter = {
        "Init": "(Volume*Price>10e+8) & (time>='2014-01-01') & (time<='2026-01-01')",
        "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
        "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
        "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
        "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
        "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
        "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
        "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
        "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
        "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
        "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
        "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
        "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
        "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
    }

    key_map = {
        'w_ma21': 'MA21',
        'w_ma31': 'MA31',
        'w_ma41': 'MA41',
        'w_s13': 'S13',
        'w_selllowgrowth': 'SellLowGrowth',
        'w_sellresistance1y': 'SellResistance1Y',
        'w_sellresistance1m': 'SellResistance1M',
        'w_sellresistance': 'SellResistance',
        'w_sellbv': 'SellBV',
        'w_sellbv2': 'SellBV2',
        'w_sellpe': 'SellPE',
        'w_sellvolmax': 'SellVolMax',
        'w_beardvg2': 'BearDvg2'
    }

    a = []
    for i, t in enumerate(trials.trials):
        if t['result']['loss'] < -74:
            # w1 = t['misc']['vals']['w1'][0]
            w2 = t['misc']['vals']['w2'][0]
            w3 = t['misc']['vals']['w3'][0]
            w4 = t['misc']['vals']['w4'][0]
            w5 = t['misc']['vals']['w5'][0]
            w6 = t['misc']['vals']['w6'][0]
            w7 = t['misc']['vals']['w7'][0]
            w8 = t['misc']['vals']['w8'][0]
            w9 = t['misc']['vals']['w9'][0]
            w10 = t['misc']['vals']['w10'][0]
            # w11 = t['misc']['vals']['w11'][0]
            # w12 = t['misc']['vals']['w12'][0]
            # w13 = t['misc']['vals']['w13'][0]
            # w14 = t['misc']['vals']['w14'][0]
            # w15 = t['misc']['vals']['w15'][0]
            # w16 = t['misc']['vals']['w16'][0]
            # w17 = t['misc']['vals']['w17'][0]
            # w18 = t['misc']['vals']['w18'][0]

            map = ""
            for key, value in t['misc']['vals'].items():
                if key.startswith('w_') and value[0] > 0:
                    map += f"{key_map[key]}, "
            pattern = "{Init} &" + f"(D_RSI < 0.3)  & (PE < {w2})  & (PE>{w3}) & (ROE_Min3Y > {w4}) & (PB < {w5}*PB_MA5Y - {w6}*PB_SD5Y) & (PCF > {w7}) & (PCF <{w8}) & ((Cash_P0/ (LtDebt_P0+1) > {w9})|(abs(IntCov_P0) > {w10})) & (NP_P0 > 0)"

            sell_filter[f'$Buy{i}'] = map[:-2]
            sell_filter[f'_Buy{i}'] = pattern
            a.append(pattern)
    print(len(a))
    print(trials.best_trial['result'])
    dict_filter = json.dumps(sell_filter)
    with open("tuning/filter_string.json", "w") as config_file:
        json.dump(dict_filter, config_file)


if __name__ == '__main__':
    trials = pickle.load(open("tuning/temp/trials_new_RSILow30_50.pkl", "rb"))
    parse_param_for_hyperopt_buy_param(trials)
