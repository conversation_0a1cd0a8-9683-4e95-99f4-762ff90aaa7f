import logging
import os
import pickle
import re
import sys
import warnings
from datetime import timedelta
from functools import lru_cache

import numpy as np
from hyperopt import hp, fmin, tpe, STATUS_OK
from hyperopt.early_stop import no_progress_loss
from hyperopt.fmin import generate_trials_to_calculate
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool

from core_utils.redis_cache import EvalRedis

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

logging.basicConfig(filename='tuning/monitor_hyo_tuning.log', level=logging.INFO,
                    format='%(asctime)s %(message)s')
warnings.simplefilter(action='ignore')
import pandas as pd
from datetime import datetime
from core_utils.base_eval import TickerEval, PreProcess, Simulation

FPATH = 'ticker_v1a/'
memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host="*************")
# redis_cache = EvalRedis()
HYO_NAME = 'hyo_technical_score_50_neg'
SI_TYPE = 'full_cash_not_fix_slot'


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


@lru_cache(maxsize=10)
def evaluate(tuple_filter, CUTLOSS=0.15):
    print("not cache")
    filters = dict(tuple_filter)

    def eval(ticker):
        try:
            pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
            eval_ticker = TickerEval(ticker, pdxx, filters, cutloss=CUTLOSS, cache_service=redis_cache)
            res = eval_ticker.get_deal()
            return res
        except Exception as error:
            print(f"Error: {ticker}: {error}")
            pass

    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    num_procs = 8
    with Pool(num_procs) as p:
        lres = p.map(eval, list_processed_ticker)

    lres = [res for res in lres if res is not None and not res.empty]
    pd_deal = pd.concat(lres, axis=0).reset_index(drop=True)
    # pd_deal['sell_time'] = pd_deal['sell_time'].dt.strftime('%Y-%m-%d')
    # if pd_deal.shape[0] < 150:
    #     print(pd_deal.shape[0])
    #     raise Exception("Not enough deals")

    df_process = PreProcess()
    pd_deal = df_process.deals(pd_deal)

    return pd_deal


def eval_filter_all_v2(dictFilter, ranking, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    def score_func(df, weight):
        neg = weight[-1]
        weight = weight[:-1]
        conditions = [
            df['D_RSI'] / df['D_RSI_T1W'] > 1,
            df['Close'] / df['VAP1W'] > 1,
            df['D_MFI'] / df['D_MFI_T1W'] > 1,
            df['D_MACD'] / df['D_MACD_T1W'] > 1,
            (df['Close'] / df['LO_3M_T1'] > 0.97) & (df['Close'] / df['LO_3M_T1'] < 1.2),
            df['Volume'] / df['Volume_1M'] > 1,
            df['D_RSI_Max1W'] / df['D_RSI_Max3M'] > 1
        ]
        df['ranking_score'] = sum(cond.astype(float).replace(0, neg) * w for cond, w in zip(conditions, weight))

        return df

    try:
        pd_deal = evaluate(tuple(sorted(dictFilter.items())), CUTLOSS)

        start_date, _ = parse_time(dictFilter)
        simulation = Simulation(start_date=start_date, initial_assets=50e9, max_deals=10,
                                cache_service=memory, num_proc=16)

        # df_combine_deals = pd_deal.merge(df_ranking[['filter', 'ranking_point']], on='filter', how='left')
        df_combine_deals = score_func(pd_deal, ranking)
        si_result = simulation.run_fast(df_deals=df_combine_deals, iterate=50, s_type=SI_TYPE,
                                        r_col='ranking_score', handle='combine')

        si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
                   'si_return_std', 'si_utilization', 'si_win_deal', 'si_win_quarter', 'si_ticker_diversity',
                   'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker']
        si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                          'return_std', 'utilization', 'win_deal', 'win_quarter', 'set_ticker',
                          'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker']
        si_res = {}
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            si_res[si_col] = si_result['Combine'][si_result_col]

        return {
            'si_return': si_res['si_return'],
            'si_std': si_res['si_return_std'],
            'si_deal': si_res['si_deals'],
            'si_utilization': si_res['si_utilization'],
            'si_diversity': si_res['si_ticker_diversity'],
            'si_quarter_ticker_diversity': si_res['si_quarter_ticker_diversity'],
            'si_win_quarter': si_res['si_win_quarter'],
            'si_win_deal': si_res['si_win_deal'],
            'status': STATUS_OK
        }
    except Exception as e:
        print(f"Error: {e}")
        return {}


if __name__ == "__main__":

    import random

    random.seed(0)


    def objective(params):

        Init = "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')"
        filter = {
            "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
            "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
            "$TL3M": "BearDvg2, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M",
            "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
            "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
            "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
            "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
            "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
            "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
            "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
            "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
            "Init": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
            "_BKMA200": f"{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
            "_TrendingGrowth": f"{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
            "_TL3M": f"{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
            "_BuySupport": f"{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
            "_RSILow30": f"{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
            "_UnderBV": f"{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
            "_SuperGrowth": f"{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
            "_SurpriseEarning": f"{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
            "_Conservative": "(Volume*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
            "_BullDvg": f"{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
            "_VolMax1Y": f"{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
            "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
            "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
            "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
            "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
            "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
            "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
            "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
            "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
            "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
            "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
            "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
            "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
            "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
        }

        ranking = [v for k, v in sorted(params.items())]
        ranking_log = {k: v for k, v in sorted(params.items())}

        result = eval_filter_all_v2(filter, ranking)

        si_return = result.get("si_return", -np.inf)
        windeal = result.get("si_win_deal", -np.inf)

        loss = si_return + windeal

        log_data = {
            "si_return": result.get("si_return"),
            "si_std": result.get("si_std"),
            "si_deal": result.get("si_deal"),
            "si_utilization": result.get("si_utilization"),
            "si_diversity": result.get("si_diversity"),
            "si_quarter_ticker_diversity": result.get("si_quarter_ticker_diversity"),
            "si_win_quarter": result.get("si_win_quarter"),
            "si_win_deal": result.get("si_win_deal"),
            "loss": loss
        }
        log_data.update(ranking_log)

        # Đọc dữ liệu cũ nếu file tồn tại
        LOG_FILE = f"tuning/{HYO_NAME}.csv"
        if os.path.exists(LOG_FILE):
            df = pd.read_csv(LOG_FILE)
        else:
            df = pd.DataFrame(columns=list(log_data.keys()))

        # Thêm dữ liệu mới
        df = df._append(log_data, ignore_index=True)

        # Ghi vào file CSV
        df.to_csv(LOG_FILE, index=False)

        print("----------------------------------------------------------------------------------------------")
        print(f"ranking: {ranking}")
        print(
            f'si_deal: {result.get("si_deal")}, si_return: {si_return}, si_win_deal: {windeal}, '
            f'si_win_quarter: {result.get("si_win_quarter")}, si_quarter_ticker_diversity: {result.get("si_quarter_ticker_diversity")}, '
            f'utilization: {result.get("si_utilization")}, si_diversity: {result.get("si_diversity")}, loss:{loss}')
        print(f"\n")
        return {'loss': -loss, 'status': STATUS_OK}


    search_space = {
        'w1': hp.quniform('w1', 0, 1, 0.1),
        'w3': hp.quniform('w3', 0, 1, 0.1),
        'w2': hp.quniform('w2', 0, 1, 0.1),
        'w4': hp.quniform('w4', 0, 1, 0.1),
        'w5': hp.quniform('w5', 0, 1, 0.1),
        'w6': hp.quniform('w6', 0, 1, 0.1),
        'w7': hp.quniform('w7', 0, 1, 0.1),
        'w8': hp.quniform('w8', -1, 0, 0.1),
    }

    init_vals = [{
        'w1': 1,
        'w2': 1,
        'w3': 1,
        'w4': 1,
        'w5': 1,
        'w6': 1,
        'w7': 1,
        'w8': -1,
    }]
    # objective

    trial_name = f"tuning/{HYO_NAME}.pkl"
    if os.path.exists(trial_name):
        trials = pickle.load(open(trial_name, "rb"))
    else:
        trials = generate_trials_to_calculate(init_vals)

    best = fmin(
        fn=objective,  # the objective function to minimize
        space=search_space,  # the search space for hyperparameters
        algo=tpe.suggest,  # the optimization algorithm to use (TPE in this case)
        max_evals=2000,  # number of iterations
        trials=trials,  # store results of each trial
        rstate=np.random.default_rng(42),
        early_stop_fn=no_progress_loss(2000),
        trials_save_file=trial_name
    )
    print("Best parameters:", best)
