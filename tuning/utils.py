import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn import metrics
import seaborn as sns
import pandas as pd
from sklearn.feature_selection import mutual_info_classif, chi2
from sklearn.linear_model import LogisticRegression
from minepy import MINE


def load_data(fname, start, end=2030):
    df = pd.read_csv(fname.replace('.csv', '').replace('*', '') + '.csv', dtype={'time': str, 'ticker': str}) \
        .query(f'time>="{start}" and time<="{end}"').reset_index(drop=True)
    df['ymd'] = pd.to_datetime(df['time'])
    return df


class Metrics:
    def __init__(self, y_gt, y_pred):
        self.y_gt = y_gt.values
        self.y_pred = y_pred.values

    def get_metrics(self):
        return self.balanced_accuracy_score()

    def procressing(self):
        for i in range(len(self.y_gt)):
            if abs(self.y_gt[i] - self.y_pred) == 0.05 * (self.y_gt[i]):
                pass

    def percentile_bins(self, bins=10):
        """
        Split the scores into percentile bins and calculate the proportion of bottoms.
        """
        percentiles = np.percentile(self.y_pred, np.linspace(0, 100, bins + 1))
        bin_labels = np.digitize(self.y_pred, percentiles, right=True)

        bin_labels = np.clip(bin_labels, 1, bins)

        # bin_counts = np.bincount(bin_labels)
        # bottom_counts = [sum(y_gt[bin_labels == i]) for i in range(1, bins + 1)]

        bin_counts = np.bincount(bin_labels, minlength=bins + 1)[1:]
        bottom_counts = np.array([sum(self.y_gt[bin_labels == i]) for i in range(1, bins + 1)])

        # prevent bin_counts from being zero
        bin_counts = np.where(bin_counts == 0, 1, bin_counts)

        return bottom_counts / bin_counts

    def plot_confusion_matrix(self):
        cf_mat = metrics.confusion_matrix(self.y_gt, self.y_pred)
        return cf_mat

    def plot_roc_curve(self, pos_label):
        fpr, tpr, thresholds = metrics.roc_curve(self.y_gt, self.y_pred, pos_label=pos_label)
        plt.plot(fpr, tpr)
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.show()

    def auc_score(self):
        auc_score = metrics.roc_auc_score(self.y_gt, self.y_pred)
        return auc_score

    def balanced_accuracy_score(self):
        return metrics.balanced_accuracy_score(self.y_gt, self.y_pred)

    def precision_score(self):
        return metrics.precision_score(self.y_gt, self.y_pred)

    def recall_score(self):
        return metrics.recall_score(self.y_gt, self.y_pred)

    def f1_score(self):
        return metrics.f1_score(self.y_gt, self.y_pred)

    def precision_recall_fscore_support(self):
        return metrics.precision_recall_fscore_support(self.y_gt, self.y_pred)

    def cohen_kappa_score(self):
        return metrics.cohen_kappa_score(self.y_gt, self.y_pred)

    def accuracy_score(self):
        return metrics.accuracy_score(self.y_gt, self.y_pred)

    def windows_score(self, windows):
        # total = self.y_gt[self.y_gt == 1].shape[0]
        total = self.y_gt.shape[0]
        correct = 0
        for i, y_gt in enumerate(self.y_gt):
            if y_gt == 1:
                y_pred = self.y_pred[i:i + windows]
                if (y_pred == 1).any():
                    correct += 1
            elif y_gt == 0:
                y_pred = self.y_pred[i:i + windows]
                if (y_pred == 0).any():
                    correct += 1

        return (correct / total) if total != 0 else 0

    def score(self):
        total = self.y_gt.shape[0]
        correct = 0
        for y_pre, y_gt in zip(self.y_pred, self.y_gt):
            if y_pre == y_gt:
                correct += 1
        return (correct / total) if total != 0 else 0

    def correct(self):
        correct = 0
        for y_pre, y_gt in zip(self.y_pred, self.y_gt):
            if y_pre == y_gt:
                correct += 1
        return correct


def plot_2_relationship_(df, title='original'):
    minL_row = ['minL1', 'minL2', 'minL3', 'minL4', 'minL5']
    minW_cols = ['minW5D', 'minW10D', 'minW20D']
    results = pd.DataFrame(index=minL_row, columns=minW_cols, dtype=float)
    for minW in minW_cols:
        for w, minL in enumerate(minL_row, 1):
            metric = Metrics(df[minW], df[minL])
            results.loc[minL, minW] = metric.score() * 100

    plt.figure(figsize=(10, 6))
    sns.heatmap(results, annot=True, fmt=".2f", cmap="YlGnBu", cbar_kws={'label': '% minL = minW'})
    plt.title(title)
    plt.xlabel('minW Values')
    plt.ylabel('minL Values')
    plt.show()


def relationship_matrix(df, title='original'):
    minL_row = ['minL1', 'minL2', 'minL3', 'minL4', 'minL5']
    minW_cols = ['minW5D', 'minW10D', 'minW20D']
    results = pd.DataFrame(index=minL_row, columns=minW_cols, dtype=float)
    results_raw = pd.DataFrame(index=minL_row, columns=minW_cols, dtype=float)
    for minL in minL_row:
        for minW in minW_cols:
            metric = Metrics(df[minW], df[minL])
            results.loc[minL, minW] = metric.score() * 100

    plt.figure(figsize=(10, 6))
    sns.heatmap(results, annot=True, fmt=".2f", cmap="YlGnBu", cbar_kws={'label': '% minL = minW'})
    plt.title(title)
    plt.xlabel('minW Values')
    plt.ylabel('minL Values')
    plt.show()

def get_bin_stats(pd_input, cname, cname_value, bins=20):
    pdx = pd_input[[cname, cname_value]].copy()
    pdx[cname] = pdx[cname].astype(float)
    binx = sorted(list(set(np.percentile(pdx[cname].dropna(), np.arange(0, 101, 100 / bins)))))
    pdx[cname] = pd.cut(pdx[cname], bins=binx)
    pdx['count'] = 1
    return pdx.groupby(cname, as_index=False, observed=False).agg({'count': 'sum', cname_value: 'mean'})

def get_bin_stats_2d(pd_input, cname_x, cname_y, cname_value, bins=20):
    pdx = pd_input[[cname_x, cname_y, cname_value]].copy()
    pdx[cname_x] = pdx[cname_x].astype(float)
    pdx[cname_y] = pdx[cname_y].astype(float)
    binx = sorted(list(set(np.percentile(pdx[cname_x].dropna(), np.arange(0, 101, 100 / bins)))))
    biny = sorted(list(set(np.percentile(pdx[cname_y].dropna(), np.arange(0, 101, 100 / bins)))))
    pdx[cname_x] = pd.cut(pdx[cname_x], bins=binx, duplicates='drop', include_lowest=True)
    pdx[cname_y] = pd.cut(pdx[cname_y], bins=biny, duplicates='drop', include_lowest=True)
    pdx = pdx.dropna(subset=[cname_x, cname_y])
    # pdx = pdx.astype({cname_x: 'string', cname_y: 'string'})

    pdx['count'] = 1
    return pdx.groupby([cname_x, cname_y], as_index=False, observed=True).agg({'count': 'sum', cname_value: 'mean'})

def relationship_variable_bin(df, variable, minL_col, minW_col, bins=10):
    # Bin the variable
    df = df[[variable, minL_col, minW_col]].copy()

    # Calculate percentage of minL = minW in each bin
    binx = sorted(list(set(np.percentile(df[variable].dropna(), np.arange(0, 101, 100 / bins)))))

    df['variable_bin'] = pd.cut(df[variable], bins=binx)
    total = df.shape[0]
    # df['count'] = 1
    # df['equal'] = np.where(df[minL_col] == df[minW_col], 1, 0)
    # df = df.groupby('variable_bin', as_index=False).agg({'count': sum, 'equal': sum})
    # df['ratio'] = df['equal'] / df['count']

    bin_counts_mean = df.groupby('variable_bin').apply(
        lambda x: (x[minL_col] == x[minW_col]).mean() * 100).reset_index()
    bin_counts_mean.columns = ['variable_bin', 'percentage_minL_equals_minW']
    bin_counts = df.groupby('variable_bin').apply(lambda x: (x[minL_col] == x[minW_col]).sum()).reset_index()
    bin_counts.columns = ['variable_bin', 'percentage_minL_equals_minW']
    bin_counts['percentage_minL_equals_minW'] = bin_counts['percentage_minL_equals_minW'] / total * 100

    # Plot the result
    ind = np.arange(10)
    width = 0.35
    plt.figure(figsize=(10, 8))
    plt.bar(ind, bin_counts['percentage_minL_equals_minW'], alpha=0.7, width=width)
    plt.bar(ind + width, bin_counts_mean['percentage_minL_equals_minW'],
            alpha=0.7, width=width)
    plt.xlabel(variable)
    plt.ylabel(f'% {minL_col} = {minW_col}')
    plt.title(f'% {minL_col} = {minW_col} across {variable} bins')
    plt.xticks(rotation=20)
    plt.xticks(ind + width / 2, bin_counts['variable_bin'])
    plt.show()


def relationship_scatter_plot(df, variable, minL_col, minW_col):
    df['min_ratio'] = df[minL_col] / df[minW_col]

    df['min_ratio'] = df['min_ratio'].apply(lambda x: x + np.random.normal(0, 0.05) if x == 1 else x)
    df['min_ratio'] = df['min_ratio'].astype(float)

    plt.figure(figsize=(20, 6))
    plt.scatter(df[variable], df['min_ratio'], alpha=0.5)
    plt.xlabel(variable)
    plt.ylabel(f'{minL_col}/{minW_col}')
    plt.title(f'Scatter plot of {variable} vs {minL_col}/{minW_col}')
    plt.show()



class StockPlotter:
    def __init__(self, stock_data):
        """
        Khởi tạo với dữ liệu cổ phiếu, stock_data là một dictionary với key là tên cổ phiếu
        và value là DataFrame chứa dữ liệu của cổ phiếu đó.
        """
        self.stock_data = stock_data

    def plot_stock_price(self, stock_name):
        """
        Vẽ giá đóng cửa của một cổ phiếu.
        """
        df = self.stock_data[stock_name]
        plt.figure(figsize=(14, 7))
        plt.plot(df.index, df['Close'], label='Close Price')
        plt.title(f'{stock_name} - Close Price Over Time')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.grid(True)
        plt.show()

    def plot_technical_indicators(self, stock_name):
        """
        Vẽ các chỉ báo kỹ thuật như SMA và RSI cùng với giá đóng cửa của một cổ phiếu.
        """
        df = self.stock_data[stock_name]
        plt.figure(figsize=(14, 10))

        # Plot Close Price and Moving Averages
        plt.subplot(2, 1, 1)
        plt.plot(df.index, df['Close'], label='Close Price')
        plt.plot(df.index, df['SMA_5'], label='SMA 5', linestyle='--')
        plt.plot(df.index, df['SMA_10'], label='SMA 10', linestyle='--')
        plt.title(f'{stock_name} - Price and Moving Averages')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.grid(True)

        # Plot RSI
        plt.subplot(2, 1, 2)
        plt.plot(df.index, df['RSI'], label='RSI', color='purple')
        plt.axhline(70, color='red', linestyle='--')
        plt.axhline(30, color='green', linestyle='--')
        plt.title(f'{stock_name} - Relative Strength Index (RSI)')
        plt.xlabel('Date')
        plt.ylabel('RSI')
        plt.legend()

        plt.tight_layout()
        plt.show()

    def plot_volume(self, stock_name):
        """
        Vẽ khối lượng giao dịch của một cổ phiếu theo thời gian.
        """
        df = self.stock_data[stock_name]
        plt.figure(figsize=(14, 7))
        plt.bar(df.index, df['Volume'], label='Volume', color='gray')
        plt.title(f'{stock_name} - Trading Volume Over Time')
        plt.xlabel('Date')
        plt.ylabel('Volume')
        plt.legend()
        plt.grid(True)
        plt.show()

    def compare_stocks(self, stocks_to_compare, feature='Close'):
        """
        So sánh một chỉ báo (ví dụ: giá đóng cửa) của nhiều cổ phiếu trên cùng một biểu đồ.
        """
        plt.figure(figsize=(14, 7))
        for stock_name in stocks_to_compare:
            df = self.stock_data[stock_name]
            plt.plot(df.index, df[feature], label=f'{stock_name} {feature}')

        plt.title(f'Comparison of {feature} for Selected Stocks')
        plt.xlabel('Date')
        plt.ylabel(feature)
        plt.legend()
        plt.grid(True)
        plt.show()

    def plot_custom_indicator(self, stock_name, indicator_name):
        """
        Vẽ một chỉ báo tùy chỉnh (custom indicator) của một cổ phiếu.
        """
        df = self.stock_data[stock_name]
        plt.figure(figsize=(14, 7))
        plt.plot(df.index, df[indicator_name], label=indicator_name)
        plt.title(f'{stock_name} - {indicator_name} Over Time')
        plt.xlabel('Date')
        plt.ylabel(indicator_name)
        plt.legend()
        plt.grid(True)
        plt.show()


class FeatureSelector:
    def __init__(self, df, features, target):
        """
        Khởi tạo FeatureSelector với DataFrame, danh sách features và biến mục tiêu.

        :param df: DataFrame chứa dữ liệu.
        :param features: Danh sách tên các cột feature cần tính toán.
        :param target: Tên cột mục tiêu nhị phân (0 hoặc 1).
        """
        self.df = df
        self.features = features
        self.target = target

    def pearson_correlation(self):
        return {feature: self.df[feature].corr(self.df[self.target]) for feature in self.features}

    def spearman_correlation(self):
        return {feature: self.df[feature].corr(self.df[self.target], method='spearman') for feature in self.features}

    def kendall_correlation(self):
        return {feature: self.df[feature].corr(self.df[self.target], method='kendall') for feature in self.features}

    def mutual_information(self):
        mi = mutual_info_classif(self.df[self.features], self.df[self.target])
        # print("mutual_information")
        return dict(zip(self.features, mi))

    def logistic_regression_coefficient(self):
        model = LogisticRegression()
        model.fit(self.df[self.features], self.df[self.target])
        coefficients = model.coef_[0]
        # print("logistic_regression_coefficient")

        return dict(zip(self.features, coefficients))

    def maximal_information_coefficient(self):
        mine = MINE()
        mic_scores = {}
        for feature in self.features:
            mine.compute_score(self.df[feature], self.df[self.target])
            mic_scores[feature] = mine.mic()
            print("maximal_information_coefficient")
        return mic_scores

    def chi_square_test(self):
        chi_scores = chi2(self.df[self.features], self.df[self.target])
        # print("chi_square_test")
        return dict(zip(self.features, chi_scores[0]))

    def aggregate_scores(self):
        """
        Tính toán trung bình cộng của tất cả các scores từ các hàm khác nhau.
        Trả về một DataFrame chứa trung bình cộng của các scores này cho từng feature.
        """
        scores = {
            'Pearson': self.pearson_correlation(),
            'Spearman': self.spearman_correlation(),
            'Kendall': self.kendall_correlation(),
            'Mutual Information': self.mutual_information(),
            'Logistic Regression Coefficient': self.logistic_regression_coefficient(),
            'Maximal Information Coefficient': self.maximal_information_coefficient(),
            # 'Chi-Square': self.chi_square_test()
        }

        # Tạo một DataFrame chứa tất cả các scores
        scores_df = pd.DataFrame(scores)

        # Chuẩn hóa các giá trị để tránh sai lệch do thang đo khác nhau
        normalized_scores_df = (scores_df - scores_df.mean()) / scores_df.std()
        normalized_scores_df_1 = (scores_df - scores_df.min()) / (scores_df.max() - scores_df.min())

        # Tính toán trung bình cộng của tất cả các scores
        scores_df['Average Score'] = normalized_scores_df.mean(axis=1)
        scores_df['Average Score_1'] = normalized_scores_df_1.mean(axis=1)

        # Sắp xếp các feature dựa trên giá trị trung bình cộng giảm dần
        return scores_df.sort_values(by='Average Score', ascending=False), scores

# if __name__ == "__main__":
# Giả sử bạn đã load và xử lý dữ liệu cổ phiếu
# data = load_data('path_to_your_data_folder')
# preprocessed_data = {stock: preprocess_data(df) for stock, df in data.items()}

# # Tạo một đối tượng StockPlotter với dữ liệu đã xử lý
# plotter = StockPlotter(preprocessed_data)
#
# # Vẽ giá cổ phiếu của một cổ phiếu cụ thể
# plotter.plot_stock_price('AAPL')
#
# # Vẽ các chỉ báo kỹ thuật của một cổ phiếu cụ thể
# plotter.plot_technical_indicators('AAPL')
#
# # Vẽ khối lượng giao dịch của một cổ phiếu cụ thể
# plotter.plot_volume('AAPL')
#
# # So sánh giá đóng cửa của nhiều cổ phiếu
# plotter.compare_stocks(['AAPL', 'GOOG', 'AMZN'])
#
# # Vẽ một chỉ báo tùy chỉnh của một cổ phiếu
# plotter.plot_custom_indicator('AAPL', 'MFI')
