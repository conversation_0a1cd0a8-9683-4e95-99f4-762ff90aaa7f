from datetime import datetime, timed<PERSON><PERSON>
from joblib import Memory
import getpass
from core_utils.redis_cache import EvalRedis
from core_utils.constant import REDIS_HOST


CURRENT_USER = getpass.getuser()
print(CURRENT_USER)

user_memory = Memory(location=f"/tmp/joblib_cache/{CURRENT_USER}", verbose=0)
user_memory.clear()

memory = Memory(location='/tmp/joblib_cache', verbose=0)
memory.clear()

user_tuning_memory = Memory(location=f"/tmp/joblib_cache/{CURRENT_USER}_tuning", verbose=0)
user_tuning_memory.clear()

redis_tuning_cache = EvalRedis(host=REDIS_HOST, db=1)
redis_tuning_cache.clear_cache()