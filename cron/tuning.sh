#!/bin/bash


echo "Cron is called."
export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate

source /workspace/kaffa_v2/cron/.env


# Clear all old cache
/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_01.py
/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_02.py
/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_03.py

#SESSION_NAME="tuning"
#
## Start a new tmux session with the specified name, or attach to an existing session
#tmux kill-session -t $SESSION_NAME
#tmux new-session -d -s $SESSION_NAME
#
## Create another window named 'server' and run a server script in it
#tmux new-window -t $SESSION_NAME:1 -n 'tuning' -c ~/my_project
#tmux send-keys -t $SESSION_NAME:1 'cd /workspace/kaffa_v2' C-m
#tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_01.py; tmux wait-for -S step1_done' C-m
#tmux wait-for step1_done
#
#tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_02.py; tmux wait-for -S step2_done' C-m
#tmux wait-for step2_done
#
#tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/python /workspace/kaffa_v2/tuning/hyo_tuning_buy_pattern_03.py; tmux wait-for -S step3_done' C-m
#tmux wait-for step3_done
#
#echo "tuning done done"
## Attach to the session to view all windows
#tmux move-window -s $SESSION_NAME:tuning