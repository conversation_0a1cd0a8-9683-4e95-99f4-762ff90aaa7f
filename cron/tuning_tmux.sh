#!/bin/bash


echo "Cron is called."
export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate

source /workspace/kaffa_v2/cron/.env


SESSION_NAME="tuning"

# Start a new tmux session with the specified name, or attach to an existing session
tmux kill-session -t $SESSION_NAME
tmux new-session -d -s $SESSION_NAME

# Create another window named 'server' and run a server script in it
# Create a second window named 'ram_monitor' and run the RAM monitoring script in it
tmux new-window -t $SESSION_NAME:1 -n 'tuning' -c ~/my_project
tmux send-keys -t $SESSION_NAME:1 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:1 'bash cron/tuning.sh' C-m  # Replace with the actual path to your RAM monitoring script
echo "RUN System Monitor done"

echo "tuning done done"
# Attach to the session to view all windows
tmux move-window -s $SESSION_NAME:tuning