#!/bin/bash
export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate
SESSION_NAME="kaffa_v2"
WINDOWS_NAME='webui'

kill -9 `lsof -t -i:8501`
killall -9 streamlit

sleep 2
echo "Checking if streamlit is running..."
if ! pgrep -x "streamlit" > /dev/null; then
  echo "Streamlit is not running. Starting streamlit in tmux..."
  tmux send-keys -t $SESSION_NAME:$WINDOWS_NAME '/workspace/py310/bin/streamlit run webui/stock_app_v2.py --server.port 8501' C-m
else
  echo "Streamlit is already running."
fi

echo "RUN WEBUI done"