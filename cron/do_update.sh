#!/bin/bash

echo "Cron is called."
source /workspace/py310/bin/activate
export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2


# Clear all old cache
echo "Clearing all cache"
/workspace/py310/bin/python /workspace/kaffa_v2/cron/clear_cache.py

# Check change indicator dictionary file. if change => have new indicator
echo "Checking daily_update"
FILE=/workspace/kaffa_v2/core_utils/dictionary.py
TODAY=$(date +%Y-%m-%d)
#if [ ! -f "$FILE" ]; then
#  echo "File '$FILE' does not exist."
#  exit 1
#fi

MODIFY_DATE=$(stat -c %y "$FILE" | cut -d' ' -f1)

if [ "$MODIFY_DATE" == "$TODAY" ]; then
  arg_daily=0
  echo "  Need to update life-time stock indicator"
else
  arg_daily=1
  echo "  Need to update daily stock indicator"
fi

echo "Updating indicator"
/workspace/py310/bin/python /workspace/kaffa_v2/cron/run.py "${arg_daily}"

echo "Clearing all cache again"
/workspace/py310/bin/python /workspace/kaffa_v2/cron/clear_cache.py

# Build report
set -a
source /workspace/kaffa_v2/cron/.env
set +a

echo "Building report"
# Remove all previous file before starting cron
rm -rf /workspace/kaffa_v2/report/assets/*.html
rm -rf /workspace/kaffa_v2/report/assets/*.pdf

/workspace/py310/bin/python /workspace/kaffa_v2/report/build_report.py
echo "Done"

