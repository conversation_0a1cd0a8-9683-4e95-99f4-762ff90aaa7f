<html>
        <head>
        <meta charset="UTF-8">
        <style>
            body {
                font-family: Arial, sans-serif;
                padding: 20px;
            }
            table.styled-table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 15px;
            }
            table.styled-table th {
                background-color: #48e0c1;
                color: black;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #ccc;
                padding: 6px;
                text-align: center;
            }
            table.styled-table td {
                border: 1px solid #ccc;
                padding: 6px;
                text-align: center;
                font-size: 13px;
            }
            .note {
                font-size: 12px;
                color: #777;
                font-style: italic;
                margin-top: 8px;
                margin-bottom: 20px;
            }
        </style>
        </head>
        <body>
            <h2>📊 Báo cáo tổng hợp dữ liệu</h2>
<p style='font-size:12px; font-style:italic; color:#555;'>Dữ liệu được thu thập và xử lý tự động từ hệ thống SmartStock</p>
<br>
<table class='styled-table' style='margin-bottom:10px;'><tr><td style='font-weight:bold;'>Date</td><td>2025-07-10</td></tr><tr><td style='font-weight:bold;'>Analyst</td><td>HHải</td></tr><tr><td style='font-weight:bold;'>Region</td><td>VN</td></tr></table>
<pre style='background:#f5f5f5; padding:10px; font-size:13px;'>{
  "symbols": [
    "FPT",
    "VIC",
    "VHM"
  ],
  "config": {
    "mode": "aggressive",
    "threshold": 0.8
  }
}</pre>
<hr style='margin: 15px 0;'>
<p>🔥 Các bảng dữ liệu chi tiết bên dưới:</p>
<h4 style='margin-bottom:5px;'>📈 Bảng giao dịch</h4>
<table border="1" class="dataframe styled-table">
  <thead>
    <tr style="text-align: right;">
      <th>Ticker</th>
      <th>Price</th>
      <th>Qty</th>
      <th>Score</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>FPT</td>
      <td><span style='font-weight:bold; font-size:15px;'>1,234,567.89</span></td>
      <td><span style='font-weight:bold; font-size:15px;'>07</span></td>
      <td><span style='font-weight:bold; font-size:15px;'>4.7</span></td>
    </tr>
    <tr>
      <td>VIC</td>
      <td><span style='font-weight:bold; font-size:15px;'>920,000.00</span></td>
      <td><span style='font-weight:bold; font-size:15px;'>23</span></td>
      <td><span style='font-weight:bold; font-size:15px;'>3.2</span></td>
    </tr>
    <tr>
      <td>VHM</td>
      <td>NaN</td>
      <td><span style='font-weight:bold; font-size:15px;'>03</span></td>
      <td><span style='font-weight:bold; font-size:15px;'>2.0</span></td>
    </tr>
  </tbody>
</table>
<p class='note'>Giá trị được tính theo đơn vị VNĐ</p>
<h4 style='margin-bottom:5px;'>Bảng rộng cần scroll</h4>
<div style='overflow-x:auto; padding-bottom: 4px;'><table border="1" class="dataframe styled-table">
  <thead>
    <tr style="text-align: right;">
      <th>Col1</th>
      <th>Col2</th>
      <th>Col3</th>
      <th>Col4</th>
      <th>Col5</th>
      <th>Col6</th>
      <th>Col7</th>
      <th>Col8</th>
      <th>Col9</th>
      <th>Col10</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><span style='font-size:15px; color:#222;'>10.00</span></td>
      <td><span style='font-size:15px; color:#222;'>20.00</span></td>
      <td><span style='font-size:15px; color:#222;'>30.00</span></td>
      <td><span style='font-size:15px; color:#222;'>40.00</span></td>
      <td><span style='font-size:15px; color:#222;'>50.00</span></td>
      <td><span style='font-size:15px; color:#222;'>60.00</span></td>
      <td><span style='font-size:15px; color:#222;'>70.00</span></td>
      <td><span style='font-size:15px; color:#222;'>80.00</span></td>
      <td><span style='font-size:15px; color:#222;'>90.00</span></td>
      <td><span style='font-size:15px; color:#222;'>100.00</span></td>
    </tr>
  </tbody>
</table></div>
<h4 style='margin-bottom:5px;'>Log truncate test</h4>
<table border="1" class="dataframe styled-table">
  <thead>
    <tr style="text-align: right;">
      <th>Log</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>This is a very long text that should be truncated and show tooltip for full content of the cell in the table UI.</td>
    </tr>
    <tr>
      <td>Short one</td>
    </tr>
  </tbody>
</table>
<table border="1" class="dataframe styled-table">
  <thead>
    <tr style="text-align: right;">
      <th>Log</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{
  "event": "SELL",
  "ticker": "VIC",
  "price": 72000,
  "volume": 150000
}</td>
    </tr>
    <tr>
      <td>{
  "event": "BUY",
  "ticker": "FPT",
  "price": 105000,
  "volume": 300000
}</td>
    </tr>
  </tbody>
</table>
<h4 style='margin-bottom:5px;'>List → bảng test</h4>
<table border="1" class="dataframe styled-table">
  <thead>
    <tr style="text-align: right;">
      <th>Ticker</th>
      <th>Price</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>MSN</td>
      <td><span style='font-size:15px; color:#222;'>90,000.00</span></td>
    </tr>
    <tr>
      <td>HPG</td>
      <td><span style='font-size:15px; color:#222;'>27,000.00</span></td>
    </tr>
  </tbody>
</table>
<ul><li>Check lại volume<br>Cập nhật profit chart<br>Alert RSI</li></ul>
        </body>
        </html>