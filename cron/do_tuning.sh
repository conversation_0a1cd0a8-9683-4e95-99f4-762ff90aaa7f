#!/bin/bash


export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate

# Clear all old cache
echo "Clearing all cache"
/workspace/py310/bin/python /workspace/kaffa_v2/cron/clear_cache.py

SESSION_NAME="tuning"

# Start a new tmux session with the specified name, or attach to an existing session
tmux kill-session -t $SESSION_NAME
tmux new-session -d -s $SESSION_NAME

# Create another window named 'server' and run a server script in it
tmux new-window -t $SESSION_NAME:1 -n 'create_task' -c ~/my_project
tmux send-keys -t $SESSION_NAME:1 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/python tuning/buy_pattern/run_tuning.py' C-m
#tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/python tuning/sell_pattern/run_tuning.py' C-m

echo "RUN WEBUI done"

# Create a second window named 'ram_monitor' and run the RAM monitoring script in it
tmux new-window -t $SESSION_NAME:2 -n 'hyperopt' -c ~/my_project
tmux send-keys -t $SESSION_NAME:2 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:2 'hyperopt-mongo-worker --mongo=*************:27017/hyperopt_db' C-m  # Replace with the actual path to your RAM monitoring script
echo "RUN System Monitor done"

# Attach to the session to view all windows
tmux move-window -s $SESSION_NAME:create_task