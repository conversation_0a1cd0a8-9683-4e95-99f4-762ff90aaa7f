import os
import subprocess
import sys
import time
import requests
import psutil
import random

os.environ["SERVICE"] = "MONITOR"
from core_utils.log import logger

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/cron", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# Threshold for RAM usage (in percentage)
RAM_THRESHOLD = 1024 * 1024 * 1024  # Modify this as per your requirement
CHECK_INTERVAL = 10  # In seconds


# Function to monitor RAM usage
def check_ram_available():
    available_ram = psutil.virtual_memory().available
    return available_ram


# Function to kill processes that consume excessive memory
def kill_high_memory_processes():
    for proc in psutil.process_iter(['pid', 'name', 'memory_percent']):
        try:
            memory_usage = proc.info['memory_percent']
            if memory_usage > RAM_THRESHOLD:
                # Kill the process
                os.kill(proc.info['pid'], 9)  # Sending SIGKILL signal
                logger.info(
                    f"Killed process {proc.info['name']} (PID: {proc.info['pid']}) using {memory_usage}% of RAM")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass


# Main service loop
def monitor_ram():
    logger.info("Checking RAM usage...")
    available_ram = check_ram_available()
    if available_ram < RAM_THRESHOLD:
        logger.warning("RAM usage exceeded threshold! Taking action...")
        logger.info("START restart [minitor_ram]")
        subprocess.call("./cron/do_st_restart.sh", shell=True)
        logger.info("DONE")


def monitor_running_process(process_name="streamlit"):
    logger.info("Checking running process...")
    process_exist = False
    for proc in psutil.process_iter(['pid', 'name', 'status']):
        try:
            if process_name in proc.name():
                process_exist = True

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
            logger.warning(e)
            time.sleep(2)

    if not process_exist:
        logger.warning(f"Process {process_name} is not existing. Starting...")
        logger.info("START restart [monitor_running_process]")
        subprocess.call("./cron/do_st_restart.sh", shell=True)
        logger.info("DONE")


def ping_streamlit():
    logger.info("Checking streamlit health...")
    ping_fail = False
    rand = random.randint(1, 5)
    retry_count = 0
    if rand == 1:
        while True:
            try:
                url = "http://localhost:8501/_stcore/health"
                response = requests.get(url=url, )
                if response.status_code != 200:
                    ping_fail = True
                else:
                    if response.text != "ok":
                        ping_fail = True

                break
            except requests.exceptions.ConnectionError as e:
                logger.warning(e)
                time.sleep(2)
                retry_count += 1
                if retry_count == 3:
                    ping_fail = True

    if ping_fail:
        logger.warning(f"Ping healcheck streamlit failed. Starting...")
        logger.info("START restart [monitor_running_process]")
        subprocess.call("./cron/do_st_restart.sh", shell=True)
        logger.info("DONE")


def system_monitor():
    logger.info("START")
    while True:
        time.sleep(CHECK_INTERVAL)
        monitor_ram()
        monitor_running_process(process_name="streamlit")
        ping_streamlit()


if __name__ == "__main__":
    system_monitor()
