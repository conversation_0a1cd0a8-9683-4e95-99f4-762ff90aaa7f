#!/bin/bash


export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate
SESSION_NAME="tuning"

tmux kill-session -t $SESSION_NAME

# Clear all old cache
echo "Clearing all cache"
/workspace/py310/bin/python /workspace/kaffa_v2/cron/clear_cache.py


# Create a second window named 'ram_monitor' and run the RAM monitoring script in it
tmux new-session -d -s $SESSION_NAME

tmux new-window -t $SESSION_NAME:1 -n 'hyperopt' -c ~/my_project
tmux send-keys -t $SESSION_NAME:1 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:1 'hyperopt-mongo-worker --mongo=*************:27017/hyperopt_db --poll-interval=30' C-m  # Replace with the actual path to your RAM monitoring script
echo "RUN System Monitor done"

# Attach to the session to view all windows
tmux move-window -s $SESSION_NAME:create_task