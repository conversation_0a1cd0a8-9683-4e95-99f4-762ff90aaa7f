#!/bin/bash

export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2

source /workspace/py310/bin/activate
SESSION_NAME="kaffa_v2"

# Kill all streamlit processes if they are running
kill -9 `lsof -t -i:8501`
killall -9 streamlit

COUNT=0
while pgrep -x "streamlit" > /dev/null; do
    echo "Waiting for streamlit to terminate..."
    sleep 0.5
    COUNT=$((COUNT+1))
    if [ $COUNT -gt 5 ]; then
        break
    fi
done


# Start a new tmux session with the specified name, or attach to an existing session
tmux kill-session -t $SESSION_NAME
tmux new-session -d -s $SESSION_NAME

# Create another window named 'server' and run a server script in it
tmux new-window -t $SESSION_NAME:1 -n 'webui' -c ~/my_project
tmux send-keys -t $SESSION_NAME:1 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:1 '/workspace/py310/bin/streamlit run webui/stock_app_v2.py --server.port 8501' C-m

echo "RUN WEBUI done"

# Create a second window named 'ram_monitor' and run the RAM monitoring script in it
tmux new-window -t $SESSION_NAME:2 -n 'system_monitor' -c ~/my_project
tmux send-keys -t $SESSION_NAME:2 'cd /workspace/kaffa_v2' C-m
tmux send-keys -t $SESSION_NAME:2 '/workspace/py310/bin/python cron/system_monitor.py' C-m  # Replace with the actual path to your RAM monitoring script
echo "RUN System Monitor done"

# Attach to the session to view all windows
tmux move-window -s $SESSION_NAME:webui