from celery import Celery
import datetime
import sys

# Broker and Backend point to the Docker host IP (assume localhost)
app = Celery(
    'b_jobs',
    broker='redis://localhost:6379/0',  # Replace 'localhost' with the Docker host IP
    backend='redis://localhost:6379/0'
)
now = datetime.datetime.now()
daily = sys.argv[1]
if daily == 1 or daily == '1':
    daily = True
else:
    daily = False

# Trigger a task
app.send_task('tasks.schedule_tasks.pineline', args=[daily]).get()
#app.send_task('tasks.schedule_tasks.update_financial_reports', args=[False, False])
