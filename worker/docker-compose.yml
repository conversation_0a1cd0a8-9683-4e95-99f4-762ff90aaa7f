version: '3'
services:
  redis:
    image: "redis:alpine"
    hostname: "redis"
    container_name: "redis"
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 30s
      retries: 10
    restart: always

  celery_worker:
    image: "python:3.10-slim"
    container_name: "celery_worker"
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A celery_app worker --loglevel=info
    restart: always
#    user: "1000:1000"
    volumes:
      - .:/app
      - ../ticker_v1a:/app/ticker_v1a
      - ../core_utils:/app/core_utils
      - ../explo:/app/explo
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - GCS_CLIENT_ID=""
      - GCS_CLIENT_EMAIL=""
      - GCS_PRIVATE_KEY_ID=""
      - GCS_PRIVATE_KEY=""
      - GCS_BUCKET_NAME=tav2-gs
      - GOOGLE_APPLICATION_CREDENTIALS=core_utils/env/gcskey.json
      - GCSHEET_CREDENTIALS=core_utils/env/gckey.json
      - SERVICE="CELERY"
      - FLOWER_UNAUTHENTICATED_API=true
      - GCS_LOCAL_PATH=gcloud_storage
    depends_on:
      - redis

  celery_beat:
    image: "python:3.10-slim"
    container_name: "celery_beat"
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A celery_app beat --loglevel=info
    restart: always
#    user: "1000:1000"
    volumes:
      - .:/app
      - ../ticker_v1a:/app/ticker_v1a
      - ../core_utils:/app/core_utils
      - ../explo:/app/explo
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - GCS_CLIENT_ID=""
      - GCS_CLIENT_EMAIL=""
      - GCS_PRIVATE_KEY_ID=""
      - GCS_PRIVATE_KEY=""
      - GCS_BUCKET_NAME=tav2-gs
      - GOOGLE_APPLICATION_CREDENTIALS=core_utils/env/gcskey.json
      - GCSHEET_CREDENTIALS=core_utils/env/gckey.json
      - SERVICE="CELERY"
      - FLOWER_UNAUTHENTICATED_API=true
      - GCS_LOCAL_PATH=gcloud_storage
    depends_on:
      - redis

#  streamlit_app:
#    image: "python:3.9-slim"
#    container_name: "streamlit_app"
#    build:
#      context: .
#      dockerfile: Dockerfile
#    command: streamlit run app.py
#    ports:
#      - "8501:8501"
#    volumes:
#      - .:/app
#    depends_on:
#      - celery_worker
#      - redis

  flower:
    image: "mher/flower"
    container_name: "flower"
    ports:
      - "5555:5555"  # Flower UI runs on port 5555
    restart: always
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - GCS_CLIENT_ID=""
      - GCS_CLIENT_EMAIL=""
      - GCS_PRIVATE_KEY_ID=""
      - GCS_PRIVATE_KEY=""
      - FLOWER_UNAUTHENTICATED_API=true
    depends_on:
      - redis
      - celery_worker
