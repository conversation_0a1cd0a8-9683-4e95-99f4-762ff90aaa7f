import datetime
import os
import random
import time
from io import BytesIO

import numpy as np
import pandas as pd
import celery
import requests

from celery_app import app, stock_query, gcs_service, gcsheet_service, memory, redis_cache, logger
from tasks import celery_utils
from explo import talib as ta
from explo import falib as fa
from explo import tdlib as td

fpath = "ticker_v1a/"
fa_path = 'data/fa_latest/'
temp_path = "temp/"
offset = 50
DICT_SHIFT = {'1W': 5, '2W': 10, '1M': 20, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480}


@app.task(bind=True)
def validate_ticker(self, ticker):
    seccond = random.randint(1, 5)
    time.sleep(seccond)
    time_ago = (datetime.datetime.today() - datetime.timedelta(days=10)).strftime('%Y-%m-%d')
    try:
        df = stock_query.get_historical_ticker(ticker, start_date=time_ago)
        # return
        if df.empty:
            return ticker
        return

    except Exception as e:
        print(e)
        if e.args[0] == "Failed to fetch data: 429 - Too Many Requests":
            time.sleep(20)
            raise self.retry(exc=e, countdown=10)
    return ticker


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def validate_volume_ticker_(self, consider_ticker):
    result = []
    three_month_ago = (datetime.datetime.today() - datetime.timedelta(days=120)).strftime('%Y-%m-%d')
    for ticker in consider_ticker:
        try:
            df = stock_query.get_historical_ticker(ticker, start_date=three_month_ago)
            df['Volume_3M'] = df['volume'].rolling(60, min_periods=1).mean()
            if df['Volume_3M'].tail(1).values < 30e3:
                result.append(ticker)
        except Exception as e:
            print(e)
    return result


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def update_stock_list(self, update_skip=False, update_monitor=True):
    logger.info(f"Update stock list")
    ticker_df = stock_query.symbols_by_industries()
    old_ticker_df = celery_utils.get_stock_list()
    consider_ticker = ticker_df['symbol'].unique()

    sell_tickers = gcsheet_service.get_sheet_data(sheet_name="TickerList")['Ticker'].to_list()
    # blacklist_monitor = gcsheet_service.get_sheet_data(sheet_name="TickerBlacklist")['Ticker'].to_list()

    if update_skip:
        list_skip = []
        tasks = celery.group(validate_ticker.s(ticker) for ticker in consider_ticker)
        results = tasks.apply_async()

        for async_result in results.results:  # Access individual AsyncResults
            result = async_result.get(disable_sync_subtasks=False)  # Retrieve the result safely
            list_skip.append(result)
        list_skip = [f for f in list_skip if f is not None]
        # list_skip = validate_volume_ticker_(consider_ticker)
    else:
        list_skip = old_ticker_df[old_ticker_df['is_skip'] == True]['ticker'].unique().tolist()

    if update_monitor:
        monitor = [path.replace('preprocess/adjusted_price/', '').replace('.csv', '') for path in
                   celery_utils.get_list_exist_files(prefix='preprocess/adjusted_price')]
    else:
        monitor = old_ticker_df[old_ticker_df['is_monitor'] == True]['ticker'].unique().tolist()

    # ticker have financial report
    # if ticker doesn't have financial report, it will be skiped
    exist_report = [path.replace('preprocess/financial_report/', '').replace('.csv', '') for path in
                    celery_utils.get_list_exist_files(prefix='preprocess/financial_report')]

    # get list of tickers and indexes
    ticker_df['ticker'] = ticker_df['symbol']
    ticker_df = ticker_df[['ticker', 'organ_name', 'com_type_code', 'icb_code4', 'icb_name4']]
    ticker_df[['is_watchlist', 'is_monitor', 'is_skip']] = False

    list_ticker = [f for f in ticker_df['ticker'].unique() if (len(f) == 3)]
    ticker_df = ticker_df[ticker_df['ticker'].isin(list_ticker)]
    ticker_df.reset_index(drop=True, inplace=True)

    for i in range(0, ticker_df.shape[0]):
        ticker = ticker_df['ticker'].iloc[i]

        if ticker not in exist_report:
            ticker_df.loc[i, 'is_skip'] = True
        if ticker in list_skip:
            ticker_df.loc[i, 'is_skip'] = True
        if ticker in sell_tickers:
            ticker_df.loc[i, 'is_watchlist'] = True
        if ticker in monitor:
            ticker_df.loc[i, 'is_monitor'] = True
    # return ticker_df.to_dict(orient='records')
    # ticker_df.to_csv("ticker_list.csv", index=False)
    gcs_service.upload_from_memory(ticker_df, "rawdata/stock_meta/latest/ticker_list.csv")

    return "Update stock list complete"


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def fill_missing_raw_report(self, ticker):
    df_quarter = stock_query.get_finance_data(ticker, period='quarter')
    df_year = stock_query.get_finance_data(ticker, period='year')
    # df = pd.concat([df_quarter, df_year])
    # df = df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False])
    # df.to_csv("all_report_ADS.csv", index=False)
    reports = []
    for year in df_year['yearReport'].unique():
        if df_quarter[(df_quarter['yearReport'] == year)].empty:
            reports.append(df_year[df_year['yearReport'] == year])

    # for year, group in df_quarter.groupby('yearReport'):
    #     if group.shape[0] == 1 :
    #     reports.append(group
    # pass
    miss_report = pd.concat(reports, ignore_index=True)
    miss_report['lengthReport'] = 4

    df = pd.concat([df_quarter, miss_report]).sort_values(['yearReport', 'lengthReport'],
                                                          ascending=[False, False]).reset_index(drop=True)
    df['release_date'] = None
    for i in range(df.shape[0]):
        year = df['yearReport'].iloc[i]
        length = df['lengthReport'].iloc[i]
        df.loc[i, 'release_date'] = celery_utils.report_date(year, length)
        destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"
        if not gcs_service.file_exists(destination):
            gcs_service.upload_from_memory(df.iloc[i:i + 1], destination)
