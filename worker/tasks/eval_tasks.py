import os
import re
import warnings
from datetime import datetime

import numpy as np

# from pathos.multiprocessing import ProcessingPool as Pool

warnings.simplefilter(action='ignore')
import pandas as pd
from itertools import combinations
from tasks.celery_utils import BaseTask
from celery_app import memory, app, redis_cache
import celery

from core_utils.stockquery import StockQuery
from core_utils.base_eval import TickerEval

FPATH = 'ticker_v1a/'


class TickerEval_Task(TickerEval):
    def __init__(self, stock, dict_filter, cutloss=0.15, cache_service=None, fpath='ticker_v1a'):
        df_all = self.open_df(fpath, stock)
        super().__init__(stock, df_all, dict_filter, cutloss, cache_service)


@app.task()
def eval_deal(ticker, dictFilter, CUTLOSS=0.15):
    eval_ticker = TickerEval_Task(stock=ticker, dict_filter=dictFilter, cutloss=CUTLOSS, cache_service=redis_cache,
                                  fpath=FPATH)
    return eval_ticker.eval_by_deal()


@app.task()
def eval_hit(ticker, dictFilter, CUTLOSS=0.15):
    eval_ticker = TickerEval_Task(stock=ticker, dict_filter=dictFilter, cutloss=CUTLOSS, cache_service=redis_cache,
                                  fpath=FPATH)
    return eval_ticker.eval_by_hit()


class Simulation(BaseTask):
    """
    Simulate trading strategy
    """

    def __init__(self):
        self.initial_assets = 1e8
        self.max_deals = 10
        # init memory cache
        self.get_fist_data_each_years = memory.cache(self.get_fist_data_each_years)

    @staticmethod
    def get_fist_data_each_years(ticker, fpath='ticker_v1a'):
        df_date = pd.read_csv(f'{fpath}/{ticker}.csv')
        df_date = df_date[['time', 'Open_1D']]
        df_date = df_date.copy()
        df_date['time'] = pd.to_datetime(df_date['time'])
        df_date['year'] = df_date['time'].dt.year
        df_date = df_date.groupby('year').first().reset_index()
        return df_date

    def run(self, df_deals, start_date, end_date, seed, si_type="full_cash", report=False):
        """
        Simulate trading strategy for all filter string in df_deals.
        We will use full cash with 10 deals.
        :param df_deals: pd.DataFrame
        :param start_date: start date
        :param end_date: end date
        :param seed: random seed
        :param si_type: type of simulation ["full_cash", "adjust_cash"]
        :return: dictionary of profit

        """
        # df_deals = pd.read_csv('tasks/pd_deal_1.csv')
        if isinstance(df_deals, str):  # Nếu truyền vào dưới dạng JSON
            df_deals = pd.read_json(df_deals)
        elif isinstance(df_deals, list):  # Nếu truyền vào dưới dạng dict
            df_deals = pd.DataFrame(df_deals)

        result = {}

        np.random.seed(seed)
        # shuffle_by_date
        df_list = []
        for date, group in df_deals.groupby('time'):
            df_list.append(group.sample(frac=1, random_state=seed))
        df_deal = pd.concat(df_list, ignore_index=True)

        for b_pattern in df_deal['filter'].unique():
            if si_type == "full_cash":
                result[b_pattern] = self.simulate_full_cash(df_deals[df_deals['filter'] == b_pattern],
                                                            start_date, end_date, self.initial_assets,
                                                            self.max_deals, report=report)
            if si_type == "adjust_cash":
                result[b_pattern] = self.simulate_adjust_cash(df_deals[df_deals['filter'] == b_pattern],
                                                              start_date, end_date, self.initial_assets,
                                                              self.max_deals, report=report)
        return result

    def simulate_full_cash(self, deals, start_date, end_date, initial_assets, max_deals, report=False):
        """
        Simulate trading strategy. We will use full cash with 10 deals
        input: deals, start_date, initial_assets
        deals: pd.DataFrame (ticker, time, sell_time, ticker, profit)
        """
        available_assets = initial_assets
        active_deals = []
        completed_deals = []
        miss_deals = []
        profit = 0
        # Report
        rf = 0.09
        profit_per_year = []
        previous_assets_report = initial_assets
        time_report = pd.to_datetime(start_date).replace(month=1, day=1) + pd.DateOffset(years=1)

        # time_in_market
        start_time = []
        end_time = []

        sell_time_remove = []
        for i, deal in deals.iterrows():
            # Report
            if report:
                while time_report <= pd.to_datetime(deal['time']):
                    current_assets_report = available_assets
                    for d in active_deals:
                        df = self.get_fist_data_each_years(ticker=d['ticker'])
                        df = df.query(f"time >= @time_report").head(1)
                        current_assets_report += (d['investment_amount'] * (df['Open_1D'] / d['Open_1D'])).values[0]

                    profit_per_year.append((current_assets_report - previous_assets_report) / previous_assets_report)
                    previous_assets_report = current_assets_report
                    time_report = time_report + pd.DateOffset(years=1)

            # process complete deal
            keep_deals = []
            # sell_time_remove = []
            for j, active_deal in enumerate(active_deals[:]):
                if active_deal['sell_time'] <= deal['time']:
                    profit += active_deal['profit']
                    # for tax
                    available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.998
                    completed_deals.append(active_deal)
                    sell_time_remove.append(active_deal['sell_time'])
                else:
                    keep_deals.append(active_deal)

            active_deals = keep_deals.copy()

            # end - time_in_market
            if (not active_deals) and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Check the budget for the new deal
            if len(active_deals) < max_deals:
                investment_amount = (available_assets / (max_deals - len(active_deals)))
                deal['investment_amount'] = investment_amount
                # for tax
                available_assets -= investment_amount * 1.001

                # start - time_in_market
                if not active_deals:
                    start_time.append(deal['time'])
                active_deals.append(deal)

            else:
                miss_deals.append(deal)

        # sell_time_remove = []
        for active_deal in (active_deals[:]):
            profit += active_deal['profit']
            available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100))
            completed_deals.append(active_deal)
            sell_time_remove.append(active_deal['sell_time'])

        today = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')

        end_time.append(max(sell_time_remove))

        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(today) - pd.to_datetime(start_date)).days

        utilization = sum([(pd.to_datetime(deal['sell_time']) - pd.to_datetime(deal['time'])).days for deal in
                           completed_deals]) / (total_time * max_deals)

        return {
            'match_deals': len(completed_deals),
            'total_time': total_time,
            'time_in_market': time_in_market,
            'profit': profit / len(completed_deals),
            'cash_profit': ((available_assets / initial_assets) - 1) * 100,
            'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
            'available_assets': available_assets,
            'utilization': utilization,
            'sharpe_ratio': ((np.mean(profit_per_year) - rf) / np.std(profit_per_year)) if report else 0,
            # 'completed_deals': pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame(),
            # 'miss_deals': pd.concat(miss_deals, axis=1).T if miss_deals else pd.DataFrame(),
        }

    def simulate_adjust_cash(self, deals, start_date, end_date, initial_assets, max_deals, report=False):
        """
        Simulate trading strategy. Blance deal cash size every year. cash size equal all cash devide 10 deals
        input: deals, start_date, initial_assets
        deals: pd.DataFrame (ticker, time, sell_time, ticker, profit)
        """
        cash_size = initial_assets / 10.01
        end_size = pd.to_datetime(start_date).replace(month=1, day=1) + pd.DateOffset(years=1)

        available_assets = initial_assets
        active_deals = []
        completed_deals = []
        miss_deals = []
        profit = 0

        # Report
        rf = 0.09
        profit_per_year = []
        previous_assets_report = initial_assets
        time_report = pd.to_datetime(start_date).replace(month=1, day=1) + pd.DateOffset(years=1)

        # time_in_market
        start_time = []
        end_time = []

        sell_time_remove = []
        for i, deal in deals.iterrows():
            # Report
            if report:
                while time_report <= pd.to_datetime(deal['time']):
                    current_assets_report = available_assets
                    for d in active_deals:
                        df = self.get_fist_data_each_years(ticker=d['ticker'])
                        df = df.query(f"time >= @time_report").head(1)
                        current_assets_report += (d['investment_amount'] * (df['Open_1D'] / d['Open_1D'])).values[0]

                    profit_per_year.append((current_assets_report - previous_assets_report) / previous_assets_report)
                    previous_assets_report = current_assets_report
                    time_report = time_report + pd.DateOffset(years=1)

            # process complete deal
            keep_deals = []
            # sell_time_remove = []
            for j, active_deal in enumerate(active_deals[:]):
                if active_deal['sell_time'] <= deal['time']:
                    profit += active_deal['profit']
                    # for tax
                    available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100)) * 0.998
                    completed_deals.append(active_deal)
                    sell_time_remove.append(active_deal['sell_time'])
                else:
                    keep_deals.append(active_deal)

            active_deals = keep_deals.copy()

            # end - time_in_market
            if (not active_deals) and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Update size
            if pd.to_datetime(deal['time']) >= end_size:
                current_assets = available_assets
                for d in active_deals:
                    df = self.get_fist_data_each_years(ticker=d['ticker'])
                    df = df.query(f"time >= @end_size").head(1)
                    current_assets += (d['investment_amount'] * (df['Open_1D'] / d['Open_1D'])).values[0]
                cash_size = current_assets / 10.01

                while end_size < pd.to_datetime(deal['time']):
                    end_size = end_size + pd.DateOffset(years=1)

            # Check the budget for the new deal
            if available_assets > cash_size:
                # investment_amount = (available_assets / (max_deals - len(active_deals)))
                deal['investment_amount'] = cash_size
                # for tax
                available_assets -= cash_size * 1.001

                # start - time_in_market
                if not active_deals:
                    start_time.append(deal['time'])
                active_deals.append(deal)

            else:
                miss_deals.append(deal)

        # sell_time_remove = []
        # check last deals
        for active_deal in (active_deals[:]):
            profit += active_deal['profit']
            available_assets += active_deal['investment_amount'] * (1 + (active_deal['profit'] / 100))
            completed_deals.append(active_deal)
            sell_time_remove.append(active_deal['sell_time'])

        today = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')

        end_time.append(max(sell_time_remove))

        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(today) - pd.to_datetime(start_date)).days

        utilization = sum([(pd.to_datetime(deal['sell_time']) - pd.to_datetime(deal['time'])).days for deal in
                           completed_deals]) / (total_time * max_deals)

        return {
            'match_deals': len(completed_deals),
            'total_time': total_time,
            'time_in_market': time_in_market,
            'profit': profit / len(completed_deals),
            'cash_profit': ((available_assets / initial_assets) - 1) * 100,
            'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
            'available_assets': available_assets,
            'utilization': utilization,
            'sharpe_ratio': ((np.mean(profit_per_year) - rf) / np.std(profit_per_year)) if report else 0,
            # 'completed_deals': pd.concat(completed_deals, axis=1).T if completed_deals else pd.DataFrame(),
            # 'miss_deals': pd.concat(miss_deals, axis=1).T if miss_deals else pd.DataFrame(),
        }


simulate_tasks = app.register_task(Simulation())


def convert_to_dict(serial_data):
    result = {}

    for item in serial_data.items():
        key, value = item
        main_key, sub_key = key.split('.', 1)

        if main_key not in result:
            result[main_key] = {}

        result[main_key][sub_key] = value
    # print(result)
    return result


def parse_time(dict_filter):
    dates = []
    date_pattern = r"\d{4}-\d{2}-\d{2}"

    for key, value in dict_filter.items():
        dates.extend(re.findall(date_pattern, value))

    date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]
    min_date = min(date_objects)
    min_date_str = min_date.strftime("%Y-%m-%d")

    max_date = max(date_objects)
    max_date_str = max_date.strftime("%Y-%m-%d")

    return min_date_str, max_date_str


@app.task()
def abc(all_results):
    df = pd.json_normalize(all_results)
    df_result = pd.Series(index=df.columns)

    for col in df.columns:
        if 'return' in col:
            df_result[col] = df[col].mean()
            df_result[col.replace('return', 'return_std')] = df[col].std()
        df_result[col] = df[col].mean()

    return convert_to_dict(df_result)


@app.task()
def simulate_df(pd_deal, dictFilter):
    start_date, end_date = parse_time(dictFilter)
    df = pd_deal.query("time >= @start_date and time <= @end_date").copy()
    df = df.sort_values('time', ascending=True).reset_index(drop=True)

    update_tasks = []
    for i in range(100):
        update_tasks.append(
            simulate_tasks.s(df_deals=df.to_dict(orient='records'), start_date=start_date, end_date=end_date, seed=i))
    job = celery.group(update_tasks)

    result = celery.chain(job | abc.s()).apply_async()
    return result


def run_simulate(pd_deal, dictFilter):
    start_date, end_date = parse_time(dictFilter)
    df = pd_deal.query("time >= @start_date and time <= @end_date").copy()
    df = df.sort_values('time', ascending=True).reset_index(drop=True)
    df = df.to_dict(orient='records')
    update_tasks = []
    for i in range(100):
        update_tasks.append(
            simulate_tasks.s(df_deals=df, start_date=start_date, end_date=end_date, seed=i))

    job = celery.group(update_tasks)
    async_results = job.apply_async()

    all_results = []
    for async_result in async_results.results:  # Access individual AsyncResults
        result = async_result.get(disable_sync_subtasks=False)  # Retrieve the result safely
        all_results.append(result)

    df = pd.json_normalize(all_results)
    df_result = pd.Series(index=df.columns)

    for col in df.columns:
        if 'return' in col:
            df_result[col] = df[col].mean()
            df_result[col.replace('return', 'return_std')] = df[col].std()
        df_result[col] = df[col].mean()
    return convert_to_dict(df_result)


def find_sell(df_price: pd.DataFrame, list_buy_index: list, list_sell_index: list,
              list_sell_reason: list, cutloss):
    # df_predefine_sell: pd.DataFrame
    """
    Find sell for pair (buy_pattern - sell reason)
    Arguments:
    - df_price: data frame with columns ymd, Close, Open
    - list_buy_index: list of Buy indexes in df_price
    - list_sell_index: list of Sell indexes in df_price
    - list_sell_reason: list of sell reasons
    - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
    Output:
    - list_deal_index: list of first buy indexes for each deal
    - list_deal_buy_price:  list of buying price (open price of T+1)
    - list_deal_sell_index: list of first sell indexes for each deal
    - list_deal_sell_price: list of selling price (open price of T+1)
    - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
    - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
    """

    def sell_decision(idx):
        price = df_price['Open_1D'].iloc[idx]
        list_deal_sell_index.append(idx)
        list_deal_sell_price.append(price)
        list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
        list_deal_market_price.append(
            (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

    # Initialize signal list with "none"
    list_signal = [None] * df_price.shape[0]

    # Mark sell signals with corresponding reasons
    for i, j in zip(list_sell_index, list_sell_reason):
        list_signal[i] = j

    # Mark buy signals
    for i in list_buy_index:
        list_signal[i] = "buy"

    # Initialize output lists
    list_deal_index = []
    list_deal_buy_price = []
    list_deal_sell_index = []
    list_deal_sell_price = []
    list_deal_profit = []
    list_deal_result = []
    list_deal_market_price = []

    current_status = None  # Status can be None or "buy"
    predefine_sell_data = None
    for i in range(min(list_buy_index), df_price.shape[0]):
        # Look for the buy signal
        if current_status is None:
            if list_signal[i] != "buy":
                continue
            # Do not evaluate deal happens today
            if i < df_price.shape[0] - 1:
                price = df_price['Open_1D'].iloc[i]
                list_deal_index.append(i)
                list_deal_buy_price.append(price)
                # predefine_sell_data = df_predefine_sell.loc[i]
                current_status = "buy"

        # Look for the cutloss or sell signal
        elif current_status == "buy":

            if i == df_price.shape[0] - 1:  # Reaching the end
                list_deal_result.append("hold")
                current_status = None
                sell_decision(i)
                continue

            # T + 3
            if i < list_deal_index[-1] + 3:
                continue

            # Cutloss
            close = df_price['Close'].iloc[i]
            if close < list_deal_buy_price[-1] * (1 - cutloss):
                list_deal_result.append("cutloss")
                current_status = None
                sell_decision(i)
                continue

            # # Sell by predefine sell signal
            # if predefine_sell_data['pre_sell_id'] == i:
            #     list_deal_result.append(predefine_sell_data['pre_sell'])
            #     current_status = None
            #     sell_decision(i)
            #     continue

            # Sell by pattern signal
            if list_signal[i] != "buy" and list_signal[i] is not None:
                list_deal_result.append(list_signal[i])
                current_status = None
                sell_decision(i)

    return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
            list_deal_result, list_deal_market_price)


@app.task()
def eval_filter_ticker(ticker, dictFilter, CUTLOSS=0.15):
    """
    Evaluate filter for a ticker
    Input:
        dictFilter: dictionary of filters
        CUTLOSS: cutloss threshold
    Output:
        pdy: dataframe of latest hit
    """
    pd_all = pd.read_csv(f'{FPATH}/{ticker}.csv')

    # Apply sell filters
    s_cols = ['time', 'Close', 'Volume', 'Sell_filter']
    sell_data = []

    for f in dictFilter:
        if f.startswith('~'):
            try:
                pd_Sell_filtered = pd_all.query(f'({dictFilter[f]})').copy()
                pd_Sell_filtered['Sell_filter'] = f[1:]
                sell_data.append(pd_Sell_filtered)
            except Exception as e:
                print(f"{ticker}--{f[1:]}--error: {e}")

    _sell_data = [data for data in sell_data if not data.empty]
    pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True) if _sell_data else sell_data[-1]
    pd_sell = pd_sell[s_cols]

    # Apply buy filters
    b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Volume']
    buy_data = []

    for f in dictFilter:
        if f.startswith('_'):
            try:
                pd_buy_filtered = pd_all.query(f'({dictFilter[f]})').copy()
                pd_buy_filtered['filter'] = f[1:]
                buy_data.append(pd_buy_filtered)
            except Exception as e:
                print(f"{ticker}--{f[1:]}--error: {e}")

    _buy_data = [data for data in buy_data if not data.empty]
    pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True) if _buy_data else buy_data[-1]

    pd_buy['hit'] = 1
    pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])
    pd_buy = pd_buy[b_cols]

    # Evaluate by deal
    # Combination sell pattern
    s_filters = []
    for f in dictFilter:
        if f.startswith('~'):
            s_filters.append(f[1:])
    C_s_filters = []
    for r in range(1, len(s_filters) + 1):
        C_s_filters.extend([list(comb) for comb in combinations(s_filters, r)])

    buy_reasons = list(pd_buy['filter'].unique())

    l_pd_deal = []
    now = pd_all.iloc[-1:][['time', 'Close', 'Volume']].copy()
    now['Sell_filter'] = 'Hold'
    for buy_reason in buy_reasons:
        buy_reason_indexes = list(pd_buy[pd_buy['filter'] == buy_reason].index).copy()

        for C_s_filter in C_s_filters:
            pd_sell_temp = pd_sell.query(f"Sell_filter == {C_s_filter}").copy()
            pd_sell_temp = pd_sell_temp._append(now)
            sell_indexes = list(pd_sell_temp.index)
            sell_reasons = list(pd_sell_temp['Sell_filter'])

            result = find_sell(df_price=pd_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']],
                               list_buy_index=buy_reason_indexes,
                               list_sell_index=sell_indexes, list_sell_reason=sell_reasons, cutloss=CUTLOSS)

            deal_time = pd_all.loc[result[0]]['time'].values
            ticker = pd_all.loc[result[0]]['ticker'].values
            deal_sell_time = pd_all.loc[result[2]]['time'].values
            pd_deal_pa = pd.DataFrame({'ticker': ticker,
                                       'time': deal_time,
                                       'buy_price': result[1],
                                       'sell_price': result[3],
                                       'profit': result[4],
                                       'sell_filter': result[5],
                                       'sell_time': deal_sell_time,
                                       'profit_vni': result[6],
                                       })
            C_s_filter.sort()
            pd_deal_pa['filter'] = f"{buy_reason}_{C_s_filter}"

            l_pd_deal.append(pd_deal_pa)

    deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                 'profit_vni']
    pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

    pd_deal = pd_deal.merge(pd_all[['time', 'Open_1D', 'Close']], on=['time'], how='left')
    return pd_deal.to_dict(orient='records')


# @app.task()
# def eval_all_filter_ticker(dictFilter, CUTLOSS=0.15):
#     list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
#
#     tasks = celery.group(eval_filter_ticker.s(ticker, dictFilter, CUTLOSS) for ticker in list_processed_ticker)
#     results = tasks.apply_async()
#
#     return results


@app.task()
def eval_filter_all_v2(dictFilter, CUTLOSS=0.15):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """
    list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]

    tasks = celery.group(eval_filter_ticker.s(ticker, dictFilter, CUTLOSS) for ticker in list_processed_ticker)
    results = tasks.apply_async()

    lres = []
    for async_result in results.results:  # Access individual AsyncResults
        result = async_result.get(disable_sync_subtasks=False)  # Retrieve the result safely
        lres.append(pd.DataFrame(result))

    # Process dataframe
    pd_deal = pd.concat([res for res in lres if res is not None and not res.empty], axis=0).reset_index(drop=True)

    dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'count_cutloss': 'count', 'n_month': 'sum',
            'count_hold': 'count', 'count_win': 'count', 'count_loss': 'count', 'count_sell': 'count',
            'n_quarter': 'sum', 'sum_profit': 'sum',
            }

    # pd_deal
    pd_deal[['p_hold', 'p_cutloss']] = np.nan
    for idx, result in enumerate(pd_deal['sell_filter'].values):
        if f'p_{result}' not in pd_deal.columns:
            pd_deal[f'p_{result}'] = np.nan
        pd_deal.loc[idx, f'p_{result}'] = pd_deal.loc[idx, 'profit']

    p_sell_columns = [col for col in pd_deal.columns if
                      (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
    pd_deal["p_sell_pattern"] = pd.concat([pd_deal[col].dropna() for col in p_sell_columns])

    pd_deal['p_win'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] > 0, np.nan)
    pd_deal['p_loss'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] <= 0, np.nan)

    pd_deal['count_win'] = pd_deal['p_win'].copy()
    pd_deal['count_loss'] = pd_deal['p_loss'].copy()
    pd_deal['count_hold'] = pd_deal['p_hold'].copy()
    pd_deal['count_cutloss'] = pd_deal['p_cutloss'].copy()

    pd_deal["quarter"] = pd.to_datetime(pd_deal['time']).dt.to_period('Q').astype(str)
    pd_deal['holding_period'] = (pd.to_datetime(pd_deal['sell_time']) - pd.to_datetime(pd_deal['time'])).dt.days
    pd_deal['half_of_year'] = pd_deal['time'].str[:4] + "H" + pd.to_datetime(pd_deal['time']).dt.month.gt(6).add(
        1).astype(str)

    pd_deal['deal'] = 1
    for ticker in pd_deal['ticker'].unique():
        pd_deal.loc[pd_deal['ticker'] == ticker, 'corr'] = pd_deal[pd_deal['ticker'] == ticker]['profit'].corr(
            pd_deal[pd_deal['ticker'] == ticker]['profit_vni'])

    # deals eval
    _pdd = pd_deal.groupby(['filter'], as_index=False) \
        .agg({f: dAgg.get(f, 'mean') for f in list(pd_deal.columns)
              if (f not in ['filter', 'quarter', 'ticker', 'time', 'Close', 'sell_time', 'sell_filter',
                            'half_of_year']) and ("time" not in f)})

    # Simulation deal
    # pd_deal.to_csv('pd_deal_1.csv')
    result = run_simulate(pd_deal, dictFilter)

    si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_utilization']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'utilization']

    for pattern, value in result.items():
        for si_col, si_result_col in zip(si_cols, si_result_cols):
            _pdd.loc[_pdd['filter'] == pattern, si_col] = value[si_result_col]

    pdd = _pdd[['filter', 'deal']].copy()

    pdd['%win_deal'] = (_pdd['count_win'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%loss_deal'] = (_pdd['count_loss'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd['%hold_deal'] = (_pdd['count_hold'].astype(int) / _pdd['deal'].astype(int)) * 100
    pdd["%cutloss_deal"] = (_pdd['count_cutloss'].astype(int) / _pdd['deal'].astype(int)) * 100

    pdd['profit_win'] = _pdd['p_win'].values
    pdd['profit_loss'] = _pdd['p_loss'].values
    pdd['profit_hold'] = _pdd['p_hold'].values
    pdd['profit_cutloss'] = _pdd['p_cutloss'].values
    pdd['holding_period'] = _pdd['holding_period'].values
    pdd['profit_expected'] = _pdd['profit'].values
    pdd['profit_vni'] = _pdd['profit_vni'].values
    pdd['corr_deal_vni'] = _pdd['corr'].values

    pdd['si_total_time'] = _pdd['si_total_time']
    pdd['si_time_in_market'] = _pdd['si_time_in_market']
    pdd['si_deals'] = _pdd['si_deals']
    pdd['si_profit'] = _pdd['si_profit']
    pdd['si_cash_profit'] = _pdd['si_cash_profit']
    pdd['si_return'] = _pdd['si_return']

    pdd = pdd.sort_values(['filter', "si_return"], ascending=[False, False])
    pdd.to_csv('pdd.csv')

# eval_filter_all_v2()
