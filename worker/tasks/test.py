import os
import sys
import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/worker/tasks", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

os.environ["GCS_BUCKET_NAME"] = "tav2-gs"
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "core_utils/env/gcskey.json"
os.environ["GCSHEET_CREDENTIALS"] = "core_utils/env/gckey.json"
os.environ['GCS_LOCAL_PATH'] = 'gcloud_storage'

from handle_raw_data import validate_ticker, update_stock_list
from data_tasks import update_financial_report, process_stock_indicator, preprocess_financial_report, preprocess_index, \
    update_stock_adjust_price, update_stock_price, update_index, process_stock_risk_indicators, process_index_indicator
from schedule_tasks import update_financial_reports, update_indexes, update_risk_indicators

# from eval_tasks import
# validate_ticker('CLH')
# update_financial_reports(True, False)
# update_stock_list(False, True)
# preprocess_index('VNINDEX', True)
# validate_volume_ticker(['HPG', 'MWG', 'MBB'])

now = datetime.datetime.now()
# update_risk_indicators()
# process_stock_risk_indicators(ticker='VIC', period_years=5)


# process_stock_indicator("HPG", now, daily_update=True)
# process_index_indicator(now)
# update_financial_report('HPG', now, True, False)
# update_index("VNINDEX", now, daily_update=True)
# update_stock_adjust_price("VNINDEX", now, daily_update=False)
update_stock_price("HPG", now, daily_update=True)
pass
# preprocess_financial_report('BVS')
