import os
from datetime import timed<PERSON>ta

from celery import Celery
from joblib import Memory
from celery.schedules import crontab
from core_utils.gcsheet import GoogleSheetServices
from core_utils.gcstorage import GoogleCloudStorageService
from core_utils.stockquery import StockQuery
from core_utils.redis_cache import RedisCache
from core_utils.log import get_logger
from core_utils.constant import JOBLIB_CACHE_DIR

logger = get_logger(os.environ.get("SERVICE", "UNKNOWN"))
# Configure Celery to use Redis as a broker
app = Celery(
    'b_jobs',
    broker='redis://localhost:6379/0',  # Redis as broker (0 means database 0)
    backend='redis://localhost:6379/0',  # Redis to store results
    include=['tasks']

)

# Optional: Custom configurations
app.conf.update(
    worker_send_task_events=True,  # Enable events for monitoring (if using tools like Flower)
    worker_max_tasks_per_child=1000,  # Restart worker after handling a number of tasks (prevent memory leaks)
    worker_concurrency=20,  # Adjust based on available CPU cores
    worker_prefetch_multiplier=1,  # Fetch 1 task at a time
    heartbeat_interval=60,  # Send heartbeat every 60 seconds
    result_expires=3600,  # Task result expiration after 1 hour
    broker_connection_retry_on_startup=True
)

app.autodiscover_tasks()

# app.conf.beat_schedule = {
#     'update-daily-finance-report': {
#         'task': 'tasks.schedule_tasks.update_financial_reports',
#         'schedule': crontab(hour='12', minute='0', day_of_week='1-5'),
#         'kwargs': {'daily_update': True, 'overwrite': False}
#     },
#
#     'update-stock-list': {
#         'task': 'tasks.handle_raw_data.update_stock_list',
#         'schedule': crontab(hour='0', minute='0', day_of_month='1,16'),
#         'kwargs': {'update_skip': True, 'update_monitor': True}
#     },
#
#     'update-risk-indicators': {
#         'task': 'tasks.schedule_tasks.update_risk_indicators',
#         'schedule': crontab(hour='5', minute='0', day_of_month='1', month_of_year='1,4,7,10'),
#     }
# }
app.conf.timezone = 'Asia/Ho_Chi_Minh'

# Initialize
# Check path exists
if not os.path.exists(os.environ['GCS_LOCAL_PATH']):
    os.mkdir(os.environ['GCS_LOCAL_PATH'])

# Initialize services
gcs_service = GoogleCloudStorageService(os.environ['GCS_BUCKET_NAME'])
logger.info("Google Cloud Storage initialized")
gcsheet_service = GoogleSheetServices(json_key_file=os.environ['GCSHEET_CREDENTIALS'])
logger.info("Google Sheet initialized")
stock_query = StockQuery(end_date='2026-01-01')
logger.info("Stock Query initialized")

memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
logger.info("Joblib cache initialized")
redis_cache = RedisCache(host='redis', port=6379, db=0)
logger.info("Redis cache initialized")
