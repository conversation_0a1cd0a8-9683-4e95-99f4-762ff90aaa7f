from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
from core_utils.log import logger

class MongoDBClient:
    _instance = None

    def __new__(cls, host='localhost', port=27017, db_name='mydb'):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.client = MongoClient(host, port, serverSelectionTimeoutMS=3000)
            cls._instance.db = cls._instance.client[db_name]
            cls._instance.host = host
            cls._instance.port = port
            cls._instance.db_name = db_name
        return cls._instance

    def check_connection(self):
        try:
            self.client.admin.command('ping')
            logger.info("MongoDB connected OK")
            return True
        except ConnectionFailure:
            logger.error("MongoDB connection failed")
            return False

    def drop_database(self):
        self.client.drop_database(self.db_name)
        logger.info(f"Dropped database {self.db_name}")

    def insert_one_document(self, collection, data):
        col = self.db[collection]
        result = col.insert_one(data)
        logger.info(f"Inserted to {collection}: {result.inserted_id}")
        return result.inserted_id

    def insert_many_documents(self, collection, data_list):
        col = self.db[collection]
        result = col.insert_many(data_list)
        logger.info(f"Inserted {len(result.inserted_ids)} documents to {collection}")
        return result.inserted_ids

    def find_one_document(self, collection, query):
        col = self.db[collection]
        result = col.find_one(query)
        logger.info(f"Find one in {collection} with {query}: {result}")
        return result

    def find_documents(self, collection, query, limit=100):
        col = self.db[collection]
        results = list(col.find(query).limit(limit))
        logger.info(f"Find many in {collection} with {query}, got {len(results)}")
        return results

    def find_all_documents(self, collection):
        col = self.db[collection]
        results = list(col.find({}))
        logger.info(f"Find all in {collection}, got {len(results)}")
        return results

    def update_one_document(self, collection, query, update_data, upsert=False):
        col = self.db[collection]
        result = col.update_one(query, {'$set': update_data}, upsert=upsert)
        logger.info(f"Update one in {collection} with {query}: matched={result.matched_count}, modified={result.modified_count}")
        return result.modified_count

    def update_many_documents(self, collection, query, update_data, upsert=False):
        col = self.db[collection]
        result = col.update_many(query, {'$set': update_data}, upsert=upsert)
        logger.info(f"Update many in {collection} with {query}: matched={result.matched_count}, modified={result.modified_count}")
        return result.modified_count

    def delete_one_document(self, collection, query):
        col = self.db[collection]
        result = col.delete_one(query)
        logger.info(f"Deleted one in {collection} with {query}: deleted={result.deleted_count}")
        return result.deleted_count

    def delete_many_documents(self, collection, query):
        col = self.db[collection]
        result = col.delete_many(query)
        logger.info(f"Deleted many in {collection} with {query}: deleted={result.deleted_count}")
        return result.deleted_count

    def list_collections(self):
        collections = self.db.list_collection_names()
        logger.info(f"Collections in db: {collections}")
        return collections

    def insert_or_update(self, collection, query, update_data, upsert=True):
        col = self.db[collection]
        result = col.update_one(query, {'$set': update_data}, upsert=upsert)
        logger.info(f"Upserted in {collection} with {query}: matched={result.matched_count}, modified={result.modified_count}")
        return result.modified_count
