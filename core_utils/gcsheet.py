import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd

FAILED_LOAD_API_KEY = "Failed to load GC API key."


class GCException(Exception):
    def __init__(self, message: str):
        super().__init__(message)

class GoogleSheetServices:
    def __init__(self, sheet_name='TA2023', json_key_file="utils/env/gckey.json"):
        self._json_key_file = json_key_file
        self._sheet_name = sheet_name
        self._scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']

    def get_google_sheet(self):
        if self._json_key_file is None:
            raise GCException(FAILED_LOAD_API_KEY)

        credentials = ServiceAccountCredentials.from_json_keyfile_name(self._json_key_file, self._scope)
        gc = gspread.authorize(credentials)
        return gc.open(self._sheet_name)

    def get_sheet_data(self, sheet_name="TickerList"):
        sheet = self.get_google_sheet()
        worksheet = sheet.worksheet(sheet_name)
        df = pd.DataFrame(worksheet.get_all_records())
        return df


# gsheet = GoogleSheetServices('TA2023', json_key_file="gckey.json")
# data = gsheet.get_sheet_data()

