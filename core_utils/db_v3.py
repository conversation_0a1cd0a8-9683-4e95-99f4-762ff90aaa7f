import pymongo
from datetime import datetime
import pandas as pd

# ====== CONFIGURATION ======
MONGO_URL = "mongodb://localhost:27017/"
DB_NAME = "your_db"
COL_META = "feature_meta"
COL_DATA = "feature_data"

# ====== 1. CONNECT TO MONGODB ======
client = pymongo.MongoClient(MONGO_URL)
db = client[DB_NAME]
col_meta = db[COL_META]
col_data = db[COL_DATA]

# ====== 2. FEATURE META MANAGER ======
class FeatureMetaManager:
    """
    Class for managing metadata of features/scores/indicators in MongoDB.
    Stores meta info (name, type, description, status, timestamps) in a dedicated collection.
    """

    def __init__(self, col):
        """
        Args:
            col: MongoDB collection for storing feature metadata
        """
        self.col = col

    def add_feature(self, name, typ, desc='', active=True):
        """
        Add or update metadata for a feature/score.

        Args:
            name (str): Feature/score field name
            typ (str): Type of feature, e.g. "score", "indicator", "info"
            desc (str): Description of the feature/score
            active (bool): If feature is active/visible
        """
        now = datetime.utcnow()
        doc = {
            "name": name,
            "type": typ,
            "desc": desc,
            "created_at": now,
            "updated_at": now,
            "active": active
        }
        self.col.update_one({"name": name}, {"$set": doc}, upsert=True)

    def update_feature(self, name, **kwargs):
        """
        Update fields of a feature/score metadata.

        Args:
            name (str): Feature/score field name
            **kwargs: Fields to update (e.g. desc="New description")
        """
        kwargs["updated_at"] = datetime.utcnow()
        self.col.update_one({"name": name}, {"$set": kwargs})

    def get_active_features(self, typ=None):
        """
        Get a list of all active features.

        Args:
            typ (str, optional): Filter by type ("score", "indicator", etc.)

        Returns:
            list of dict: Metadata of active features
        """
        query = {"active": True}
        if typ:
            query["type"] = typ
        return list(self.col.find(query))

    def get_all_features(self):
        """
        Get all features/fields metadata, including inactive ones.

        Returns:
            list of dict: All features metadata
        """
        return list(self.col.find({}))

# ====== 3. FEATURE DATA MANAGER ======
class FeatureDBManager:
    """
    Class for managing feature/score/indicator data in MongoDB.
    Supports insert/update (single or batch), and flexible queries.
    """

    def __init__(self, col):
        """
        Args:
            col: MongoDB collection for storing feature/score data
        """
        self.col = col
        # Create indexes for efficient query
        self.col.create_index([("ymd", 1)])
        self.col.create_index([("ticker", 1)])

    def insert_or_update(self, ymd, ticker, feature_dict):
        """
        Insert or update (upsert) feature values for a single ticker-date.

        Args:
            ymd (str): Date in format "YYYY-MM-DD" or "YYYYMMDD"
            ticker (str): Stock ticker code
            feature_dict (dict): Dictionary of {feature/score name: value}
        """
        self.col.update_one(
            {"ymd": ymd, "ticker": ticker},
            {"$set": feature_dict},
            upsert=True
        )

    def batch_update(self, df: pd.DataFrame):
        """
        Batch insert or update feature values from a DataFrame.

        Args:
            df (pd.DataFrame): Must include columns 'ymd' and 'ticker', others are features/scores.
        """
        for _, row in df.iterrows():
            ymd = row['ymd']
            ticker = row['ticker']
            features = row.drop(['ymd', 'ticker']).to_dict()
            self.insert_or_update(ymd, ticker, features)

    def get_data(self, ymd=None, ticker=None, features=None):
        """
        Query data by date, ticker, and/or selected features.

        Args:
            ymd (str or list, optional): Date(s) to filter
            ticker (str or list, optional): Ticker(s) to filter
            features (list, optional): List of feature/score fields to include

        Returns:
            pd.DataFrame: Resulting data
        """
        query = {}
        if ymd:
            if isinstance(ymd, (list, set)):
                query["ymd"] = {"$in": list(ymd)}
            else:
                query["ymd"] = ymd
        if ticker:
            if isinstance(ticker, (list, set)):
                query["ticker"] = {"$in": list(ticker)}
            else:
                query["ticker"] = ticker
        proj = None
        if features:
            proj = {f: 1 for f in features}
            proj["ymd"] = 1
            proj["ticker"] = 1
        cursor = self.col.find(query, proj)
        return pd.DataFrame(list(cursor))

# ====== 4. USAGE EXAMPLE ======
if __name__ == "__main__":
    # Initialize managers
    meta_mgr = FeatureMetaManager(col_meta)
    db_mgr = FeatureDBManager(col_data)

    # 1. Add a new score field to metadata
    meta_mgr.add_feature(
        name="score_superai",
        typ="score",
        desc="Super AI model score",
        active=True
    )

    # 2. Insert/update value for a single ticker and date
    ymd = "2024-07-27"
    ticker = "AAA"
    score_val = 1.234
    db_mgr.insert_or_update(ymd, ticker, {"score_superai": score_val})

    # 3. Batch update for multiple tickers/dates from DataFrame
    df = pd.DataFrame([
        {"ymd": "2024-07-27", "ticker": "AAA", "score_superai": 1.234},
        {"ymd": "2024-07-27", "ticker": "BBB", "score_superai": 2.345},
        {"ymd": "2024-07-28", "ticker": "AAA", "score_superai": 0.888},
    ])
    db_mgr.batch_update(df)

    # 4. Query all data
    full_data = db_mgr.get_data()
    print("All data:\n", full_data.head())

    # 5. Query by ticker or date
    data_aaa = db_mgr.get_data(ticker="AAA")
    print("Data AAA:\n", data_aaa)

    # 6. Show metadata
    meta = meta_mgr.get_all_features()
    print("Meta info:\n", pd.DataFrame(meta))







class Portfolio:
    def __init__(self, investment_amount: float, num_slot: int):
        self.cash = investment_amount
        self.holdings: Dict[str, List[dict]] = {}  # ticker: list of lots
        self.transactions: List[dict] = []
        self.daily_stats: List[dict] = []
        self.num_slot = num_slot

    def update_nav_and_stats(self, ymd: str, df_today: pd.DataFrame):
        """
        Calculate NAV and cash for the current day and append to daily_stats.
        """
        nav = self.cash
        for ticker, lots in self.holdings.items():
            price_row = df_today[df_today['ticker'] == ticker]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            for lot in lots:
                nav += lot['quantity'] * price
        self.daily_stats.append({'ymd': ymd, 'nav': nav, 'cash': self.cash})

    def review_portfolio(self, df_today: pd.DataFrame, cut_loss: float) -> List[dict]:
        """
        Review the portfolio and return a list of sell orders (cutloss, score drop, etc.).
        """
        to_sell = []
        for ticker, lots in self.holdings.items():
            price_row = df_today[df_today['ticker'] == ticker]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            score = price_row.iloc[0]['score']
            for lot in lots:
                # Cut loss condition
                if price < lot['buy_price'] * (1 - cut_loss):
                    to_sell.append({'ticker': ticker, 'quantity': lot['quantity'], 'reason': 'cutloss',
                                    'buy_price': lot['buy_price']})
                # Score drop condition
                elif score < 0.5:
                    to_sell.append({'ticker': ticker, 'quantity': lot['quantity'], 'reason': 'score_drop',
                                    'buy_price': lot['buy_price']})
        return to_sell

    def execute_sell(self, sell_order: dict, df_today: pd.DataFrame, fee: float, ratio_deal_volume: float):
        """
        Execute a sell order, update portfolio, cash, and transactions. cash_ref is a single-element list for reference passing.
        """
        ticker = sell_order['ticker']
        price_row = df_today[df_today['ticker'] == ticker]
        if price_row.empty:
            return
        price = price_row.iloc[0]['price']
        volume_p50_1m = price_row.iloc[0]['volume_p50_1m']
        max_sell = volume_p50_1m * ratio_deal_volume
        quantity = min(sell_order['quantity'], max_sell)
        lots = self.holdings.get(ticker, [])
        remain = quantity
        new_lots = []
        for lot in lots:
            if remain <= 0:
                new_lots.append(lot)
                continue
            if lot['quantity'] <= remain:
                sell_qty = lot['quantity']
                remain -= sell_qty
            else:
                sell_qty = remain
                lot['quantity'] -= sell_qty
                new_lots.append(lot)
                remain = 0
            self.transactions.append({
                'ymd': price_row.iloc[0]['ymd'],
                'ticker': ticker,
                'action': 'sell',
                'price': price,
                'quantity': sell_qty,
                'fee': sell_qty * price * fee,
                'reason': sell_order['reason'],
            })
            self.cash += sell_qty * price * (1 - fee)
        if new_lots:
            self.holdings[ticker] = new_lots
        else:
            self.holdings.pop(ticker, None)

    def select_buy_candidates(self, df_today: pd.DataFrame) -> List[dict]:
        """
        Select buy candidates, prioritize by score, and check slot constraints.
        """
        slot_used = 0
        for lots in self.holdings.values():
            for lot in lots:
                slot_used += lot['slot']
        df_buy = df_today[df_today['score'] >= 1].copy()
        if df_buy.empty:
            return []
        df_buy = df_buy.sort_values('score', ascending=False)
        buy_orders = []
        for _, row in df_buy.iterrows():
            score = row['score']
            if score >= 3:
                slot = 3
            elif score >= 2:
                slot = 2
            else:
                slot = 1
            if slot_used + slot > self.num_slot:
                continue
            buy_orders.append({
                'ticker': row['ticker'],
                'price': row['price'],
                'score': score,
                'slot': slot,
                'volume_p50_1m': row['volume_p50_1m'],
                'volume': row['volume'],
            })
            slot_used += slot
        return buy_orders

    def execute_buy(self, buy_order: dict, df_today: pd.DataFrame, fee: float, ratio_deal_volume: float):
        """
        Execute a buy order, check slot, cash, liquidity, fee, and update portfolio, cash, and transactions.
        """
        ticker = buy_order['ticker']
        price = buy_order['price']
        slot = buy_order['slot']
        volume_p50_1m = buy_order['volume_p50_1m']
        max_buy = volume_p50_1m * ratio_deal_volume
        nav = self.calc_nav(df_today)
        max_cash = nav / self.num_slot
        quantity = min(max_buy, int(min(self.cash, max_cash) // (price * (1 + fee))))
        if quantity <= 0:
            return
        cost = quantity * price * (1 + fee)
        if cost > self.cash:
            quantity = int(self.cash // (price * (1 + fee)))
            cost = quantity * price * (1 + fee)
        if quantity <= 0:
            return
        lot = {'buy_price': price, 'quantity': quantity, 'buy_date': None, 'slot': slot,
               'score_at_buy': buy_order['score']}
        if ticker in self.holdings:
            self.holdings[ticker].append(lot)
        else:
            self.holdings[ticker] = [lot]
        self.cash -= cost
        self.transactions.append({
            'ymd': None,  # will be filled after loop
            'ticker': ticker,
            'action': 'buy',
            'price': price,
            'quantity': quantity,
            'fee': quantity * price * fee,
            'reason': 'score_buy',
        })

    def calc_nav(self, df_today: pd.DataFrame) -> float:
        nav = self.cash
        for ticker, lots in self.holdings.items():
            price_row = df_today[df_today['ticker'] == ticker]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            for lot in lots:
                nav += lot['quantity'] * price
        return nav

    def calc_return(self, investment_amount: float) -> Dict[str, Any]:
        """
        Calculate total return and annualized return from daily NAV statistics.
        """
        if not self.daily_stats:
            return {'total_return': 0, 'annualized_return': 0}
        nav_start = self.daily_stats[0]['nav']
        nav_end = self.daily_stats[-1]['nav']
        days = len(self.daily_stats)
        total_return = (nav_end - nav_start) / investment_amount
        annualized_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        return {'total_return': total_return, 'annualized_return': annualized_return}


def simulation_v2_(
        data: pd.DataFrame,
        investment_amount: float,
        cut_loss: float,
        num_slot: int,
        ratio_deal_volume: float,
        review_frequency: str,
        fee: float = 0.0015
) -> Tuple[pd.DataFrame, pd.DataFrame, dict]:
    data = data.sort_values('ymd')
    trading_days = sorted(data['ymd'].unique())
    review_days = get_review_days(trading_days, review_frequency)
    pf = Portfolio(investment_amount, num_slot)

    for ymd in trading_days:
        # 0. Get today's data
        df_today = data[data['ymd'] == ymd].copy()

        pf.update_nav_and_stats(ymd=ymd, df_today=df_today)
        if ymd in review_days:
            # 1. Review portfolio
            to_sell = pf.review_portfolio(df_today, cut_loss)
            # 2. Execute sell orders
            for sell_order in to_sell:
                pf.execute_sell(sell_order, df_today, fee, ratio_deal_volume)
            # 3. Execute buy orders
            buy_candidates = pf.select_buy_candidates(df_today)
            for buy_order in buy_candidates:
                pf.execute_buy(buy_order, df_today, fee, ratio_deal_volume)

    # Gán lại ymd cho các transaction mua (giữ lại logic cũ)
    buy_idx = 0
    for ymd in trading_days:
        for t in pf.transactions:
            if t['action'] == 'buy' and t['ymd'] is None:
                t['ymd'] = ymd
                buy_idx += 1
    si_return = pf.calc_return(investment_amount)
    return pd.DataFrame(pf.transactions), pd.DataFrame(pf.daily_stats), si_return