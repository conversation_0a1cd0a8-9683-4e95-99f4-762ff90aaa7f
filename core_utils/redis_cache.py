import hashlib
import io
import json
import os
from functools import wraps

import pandas as pd
import redis
from redis.exceptions import ConnectionError

from core_utils.constant import CURRENT_USER

os.environ["SERVICE"] = f"CACHE_{CURRENT_USER}"
from core_utils.log import logger


class RedisCache:
    _instance = None  # Singleton instance

    def __new__(cls, host='localhost', port=6379, db=0, max_connections=10, expiry=3600):
        if cls._instance is None:
            # Create a new instance only if one doesn't already exist
            cls._instance = super(RedisCache, cls).__new__(cls)
            # Initialize Redis connection pool
            cls._instance.pool = redis.ConnectionPool(
                host=host, port=port, db=db, max_connections=max_connections, decode_responses=True
            )
            # Set up Redis connection using the connection pool
            cls._instance.redis_conn = redis.StrictRedis(connection_pool=cls._instance.pool)
        logger.info(cls._instance)
        return cls._instance

    def __init__(self, host='localhost', port=6379, db=0, max_memory='1000mb', eviction_policy='allkeys-lru',
                 expiry=3600):
        """
        Initializes the Redis Cache with configuration for cache, expiry, and policies.

        :param host: Redis server address
        :param port: Redis port
        :param db: Redis Database (default is 0)
        :param max_memory: Maximum cache size in Redis (e.g., '100mb')
        :param eviction_policy: Eviction policy when cache overloads ('allkeys-lru', 'volatile-lru', ...)
        :param expiry: Default expiration time for each cache key (seconds)
        """
        # Set instance variables
        self.host = host
        self.port = port
        self.db = db
        self.max_memory = max_memory
        self.eviction_policy = eviction_policy
        self.expiry = expiry

        # Apply configuration only if this is the first initialization
        if not hasattr(self, "_configured"):
            self._configure_redis()
            self._configured = True  # Avoid reconfiguring on subsequent instantiations

    def _configure_redis(self):
        """
        Configure Redis based on settings for max_memory and eviction_policy.
        """
        try:
            self.redis_conn.config_set('maxmemory', self.max_memory)
            self.redis_conn.config_set('maxmemory-policy', self.eviction_policy)
            logger.info("Redis cache configured with max_memory: %s and eviction_policy: %s", self.max_memory,
                        self.eviction_policy)
        except ConnectionError as e:
            logger.error("Redis connection error during configuration: %s", e)

    @staticmethod
    def generate_cache_key(unique_string):
        """
        Generate a unique cache key based on ticker and filter.
        """
        # filter_str = json.dumps(filter, sort_keys=True)
        # unique_string = f"{prefix}:{filter_str}"
        cache_key = hashlib.md5(unique_string.encode()).hexdigest()
        return cache_key

    def set_cache(self, key, value, expiry=None):
        """
        Set data in cache with a specified key and value.

        :param key: Cache key
        :param value: Value to cache
        :param expiry: Optional expiry time for the key (default is RedisCache's expiry)
        """
        try:
            expiry_time = expiry if expiry else self.expiry
            self.redis_conn.setex(name=key, time=expiry_time, value=value)
            logger.info("Cache set: %s = %s (expiry in %d seconds)", key, value, expiry_time)
        except ConnectionError as e:
            logger.error("Redis connection error during set_cache: %s", e)

    def get_cache(self, key):
        """
        Retrieve data from cache by key.

        :param key: Cache key
        :return: Cached value if found, None if not found or expired.
        """
        try:
            value = self.redis_conn.get(key)
            if value is None:
                logger.info("Cache miss for key: %s", key)
            else:
                logger.info("Cache hit for key: %s", key)
            return value
        except ConnectionError as e:
            # logger.error("Redis connection error during get_cache: %s", e)
            return None

    def set_prefix_cache(self, prefix, key, value, expiry=None):
        """
        Cache the sell signal result with unique key based on ticker and filter.
        Additionally, store this key in a list for ticker for easy deletion.
        Only one cache per prefix:ticker is allowed.
        """
        try:
            if isinstance(value, pd.DataFrame):
                value = value.to_json(orient='split')
            else:
                value = json.dumps(value)

            expiry_time = expiry if expiry else self.expiry
            self.redis_conn.setex(name=f"{prefix}:{key}", time=expiry_time, value=value)
            # logger.info("Cache set: %s = %s (expiry in %d seconds)", key, value, expiry_time)

        except ConnectionError as e:
            # logger.error("Redis connection error during set_cache: %s", e)

            pass

    def get_prefix_cache(self, prefix, key):
        """
        Retrieve the cached sell signal result based on ticker and filter.
        """
        try:
            cache_key = f"{prefix}:{key}"
            cached_value = self.redis_conn.get(cache_key)

            if cached_value is not None:
                if isinstance(cached_value, bytes):
                    cached_value = cached_value.decode("utf-8")
                # logger.info("Cache hit for key: %s", key)

                try:
                    data = json.loads(cached_value)
                    if isinstance(data, dict) and all(k in data for k in ["columns", "index", "data"]):
                        # Try loading as a DataFrame JSON format
                        return pd.read_json(io.StringIO(cached_value), orient='split')

                    return data

                except json.JSONDecodeError:
                    # Nếu không phải JSON hợp lệ, log lỗi và trả về None
                    print(f"ERROR: Invalid JSON format in cache for key {cache_key}")
                    return None

            # logger.info("Cache miss for key: %s", key)
            return None
        except ConnectionError as e:
            logger.error("Redis connection error during get_cache: %s", e)
            return None

    def get_with_expiry_update(self, prefix, key):
        """
               Retrieve the cached sell signal result based on ticker and filter.
               """
        try:
            cache_key = f"{prefix}:{key}"
            cached_value = self.redis_conn.getex(cache_key, ex=self.expiry)

            if cached_value is not None:
                # Update expiry
                if isinstance(cached_value, bytes):
                    cached_value = cached_value.decode("utf-8")
                # logger.info("Cache hit for key: %s", key)

                try:
                    data = json.loads(cached_value)
                    if isinstance(data, dict) and all(k in data for k in ["columns", "index", "data"]):
                        # Try loading as a DataFrame JSON format
                        return pd.read_json(io.StringIO(cached_value), orient='split')

                    return data

                except json.JSONDecodeError:
                    # Nếu không phải JSON hợp lệ, log lỗi và trả về None
                    print(f"ERROR: Invalid JSON format in cache for key {cache_key}")
                    return None

            # logger.info("Cache miss for key: %s", key)
            return None
        except ConnectionError as e:
            logger.error("Redis connection error during get_cache: %s", e)
            return None

    def delete_cache(self, key):
        """
        Delete data from cache by key.

        :param key: Cache key
        """
        try:
            self.redis_conn.delete(key)
            logger.info("Cache deleted for key: %s", key)
        except ConnectionError as e:
            logger.error("Redis connection error during delete_cache: %s", e)

    def clear_cache(self):
        """
        Clear all cache in Redis.
        """
        try:
            self.redis_conn.flushdb()
            logger.info("All cache cleared")
        except ConnectionError as e:
            logger.error("Redis connection error during clear_cache: %s", e)

    def check_cache_status(self):
        """
        Check the current status of the cache.

        :return: Returns information about memory usage and number of keys in cache.
        """
        try:
            memory_used = self.redis_conn.info('memory').get('used_memory_human')
            num_keys = self.redis_conn.info('db0').get('keys', 0)
            logger.info("Cache status - Memory used: %s, Number of keys: %d", memory_used, num_keys)
            return {'memory_used': memory_used, 'num_keys': num_keys}
        except ConnectionError as e:
            logger.error("Redis connection error during check_cache_status: %s", e)
            return None

    def cache_exists(self, key):
        """
        Check if a key exists in cache.

        :param key: Cache key
        :return: True if key exists, False if not.
        """
        try:
            exists = self.redis_conn.exists(key)
            # logger.info("Cache exists check for key %s: %s", key, "Yes" if exists else "No")
            return exists
        except ConnectionError as e:
            # logger.error("Redis connection error during cache_exists: %s", e)
            return False

    def handle_cache_overload(self):
        """
        Monitor for cache overload and take necessary actions.
        """
        status = self.check_cache_status()
        if status:
            memory_used = status['memory_used']
            # Example of overload handling: check if memory usage exceeds a set threshold.
            if 'mb' in memory_used and float(memory_used.replace('mb', '')) > 90.0:
                # logger.warning("Cache memory usage is over 90MB, clearing old keys...")
                self.clear_cache()  # Alternatively, delete least-used keys (LRU)

    def clean_expired_keys(self):
        """
        Automatically clear expired keys from Redis.
        """
        try:
            self.redis_conn.expire()
            logger.info("Expired keys have been cleared from cache")
        except ConnectionError as e:
            logger.error("Redis connection error during clean_expired_keys: %s", e)

    def cache_method(self, expiry=None):
        """Decorator to cache the result of a method with an optional expiry parameter."""

        def decorator(method):
            @wraps(method)
            def wrapper(*args, **kwargs):
                # Generate a unique cache key based on method name, arguments, and keyword arguments
                # key_base = f"{self.__class__.__name__}:{method.__name__}:{args}:{kwargs}"
                method_name = method.__name__
                key_base = f"{args}:{kwargs}"

                cache_key = self.generate_cache_key(key_base)
                cached_result = self.get_prefix_cache(prefix=method_name, key=cache_key)
                # Attempt to retrieve from cache
                if cached_result is not None:
                    return cached_result

                # Call the method and cache its result
                result = method(*args, **kwargs)
                self.set_prefix_cache(prefix=method_name, key=cache_key, value=result, expiry=expiry)
                return result

            return wrapper

        return decorator


class EvalRedis(RedisCache):
    def __init__(self, host='localhost', port=6379, db=0, max_memory='3000mb', eviction_policy='allkeys-lru',
                 expiry=1800):
        """
        Initializes the Redis Cache with configuration for cache, expiry, and policies.

        :param host: Redis server address
        :param port: Redis port
        :param db: Redis Database (default is 0)
        :param max_memory: Maximum cache size in Redis (e.g., '100mb')
        :param eviction_policy: Eviction policy when cache overloads ('allkeys-lru', 'volatile-lru', ...)
        :param expiry: Default expiration time for each cache key (seconds)
        """
        super().__init__(host=host, port=port, db=db, max_memory=max_memory, eviction_policy=eviction_policy,
                         expiry=expiry)

    def manage_prefix_unique_cache(self, prefix, r_name, key):
        """
        prefix and ticker are used to generate unique cache key
        Make sure to only save one cache for a prefix:ticker
        arg:
            prefix: str like a classname function
            r_name: str like a name representing like a ticker
            key: str hash key
        """
        # Delete key from ticker-specific list
        keys = self.redis_conn.smembers(f"{prefix}:{r_name}")
        if keys:
            for k in keys:
                self.redis_conn.delete(k)
            # Clear the set storing keys for this ticker
            self.redis_conn.delete(f"{prefix}:{r_name}")

        # Save key to ticker-specific list
        self.redis_conn.sadd(f"{prefix}:{r_name}", f"{prefix}:{key}", )
