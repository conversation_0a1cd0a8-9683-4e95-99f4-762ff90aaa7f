import time

import numpy as np
import pandas as pd

SELL_THRESHOLD = 0.5
BUY_THRESHOLD = 1
DELTA_DROP = 2
LOWEST_CASH = 10e6
max_slot = 0


class Portfolio:
    def __init__(self, initial_amount, num_slot):
        self.cash = initial_amount
        self.num_slot = num_slot
        self.holdings = pd.DataFrame(
            columns=['ticker', 'quantity', 'price_buy', 'investment_amount', 'ymd_buy', 'fee', 'slot', 'initial_score',
                     'current_score'])
        self.transactions = []
        self.completed_deals = []
        self.daily_stats = []

    def update_holding(self, ticker, quantity, price_buy, ymd_buy, fee, initial_score, investment_amount):
        print(
            f"[update_holding] ticker={ticker} quantity={quantity} price_buy={price_buy} ymd_buy={ymd_buy} fee={fee} initial_score={initial_score}")
        idx = self.holdings[self.holdings['ticker'] == ticker].index

        if len(idx) > 0:
            # already exists => accumulate quantity, update average buy price, slot
            old_row = self.holdings.loc[idx[0]]
            new_qty = old_row['quantity'] + quantity
            new_price_buy = (old_row['price_buy'] * old_row['quantity'] + price_buy * quantity) / new_qty
            new_slot = old_row['slot'] + 1
            new_invest = old_row['investment_amount'] + investment_amount
            new_fee = old_row['fee'] + fee
            print(f"  Already have {ticker}, update quantity: {old_row['quantity']} + {quantity} = {new_qty}")
            self.holdings.loc[
                idx[0], ['quantity', 'price_buy', 'ymd_buy', 'fee', 'slot', 'initial_score', 'investment_amount']] = \
                [new_qty, new_price_buy, ymd_buy, new_fee, new_slot, initial_score, new_invest]
        else:
            print(f"  Add new ticker: {ticker}")
            self.holdings = pd.concat([self.holdings, pd.DataFrame([{
                'ticker': ticker,
                'quantity': quantity,
                'price_buy': price_buy,
                'investment_amount': investment_amount,
                'ymd_buy': ymd_buy,
                'fee': fee,
                'slot': 1,
                'initial_score': initial_score,
                'current_score': initial_score,
            }])], ignore_index=True)

    def remove_holding(self, ticker, quantity, price_sell, ymd_sell, fee, impactloss=0):
        idx = self.holdings[self.holdings['ticker'] == ticker].index
        if len(idx) > 0:
            row = self.holdings.loc[idx[0]]
            sell_qty = min(quantity, row['quantity'])
            remain_qty = row['quantity'] - sell_qty
            print(
                f"[remove_holding] ticker={ticker} sell {sell_qty} quantities, (price={price_sell}, fee={fee}), remaining {remain_qty}")
            self.holdings.loc[idx[0], 'quantity'] = remain_qty
            # Save completed_deal if fully sold
            if remain_qty > 0:
                ratio = sell_qty / row['quantity']
                self.holdings.loc[idx[0], 'quantity'] = remain_qty
                self.holdings.loc[idx[0], 'investment_amount'] = row['investment_amount'] * (1 - ratio)
                self.holdings.loc[idx[0], 'fee'] = row['fee'] * (1 - ratio)
                print(f"  Partially sold {ticker}, keep holding {remain_qty}")

            else:
                exec_adj_sell = price_sell  # đã là adj sau delta
                adj_buy = row['price_buy']
                invest = row['investment_amount']
                buy_fee = row['fee']
                sell_fee = fee
                proceeds = invest * (exec_adj_sell / adj_buy)
                profit_amount = proceeds - invest - buy_fee - sell_fee
                roi = profit_amount / invest if invest > 0 else np.nan

                deal = {
                    'ticker': ticker,
                    'ymd_buy': row['ymd_buy'],
                    'ymd_sell': ymd_sell,
                    'price_buy': adj_buy,
                    'price_sell': exec_adj_sell,
                    'investment_amount': invest,
                    'quantity': sell_qty,
                    'buy_fee': buy_fee,
                    'sell_fee': sell_fee,
                    'profit_amount': profit_amount,
                    # 'profit': (price_sell - row['price_buy']) / row['price_buy'] * 100
                    'profit': roi
                }
                self.completed_deals.append(deal)
                print(f"  Fully sold {ticker}, recorded completed_deal: {deal}")
                self.holdings = self.holdings.drop(idx[0]).reset_index(drop=True)

    def record_transaction(self, ymd, ticker, action, price, quantity, fee, cash_amount, impactloss=0):
        self.transactions.append({
            'ymd': ymd, 'ticker': ticker, 'action': action, 'price': price, 'quantity': quantity, 'fee': fee,
            'gross_cash': cash_amount
        })

    def update_nav(self, ymd, adj_price_dict):
        holdings_value = 0
        for idx, row in self.holdings.iterrows():
            ticker = row['ticker']
            adj_price = adj_price_dict.get(ticker, row['price_buy'])
            holdings_value += (adj_price / row['price_buy']) * row['investment_amount']
        nav = self.cash + holdings_value
        self.daily_stats.append({'ymd': ymd, 'nav': nav, 'cash': self.cash})

    def update_score(self, score_dict):
        """
        Update score for all holdings based on score_dict.
        score_dict: dictionary of {ticker: score}

        Using for debug
        """
        for idx, row in self.holdings.iterrows():
            ticker = row['ticker']
            score = score_dict.get(ticker, row['current_score'])
            self.holdings.loc[idx, 'current_score'] = score

    def get_holding_lowest_score(self, score_dict, score_threshold=0.5, ymd=None):
        if self.holdings.empty:
            return self.holdings.iloc[0:0]

        lowest_score = float('inf')
        lowest_ticker = None

        for idx, row in self.holdings.iterrows():
            if (pd.to_datetime(ymd) - pd.to_datetime(row['ymd_buy'])).days < 3:
                continue

            ticker = row['ticker']
            score = score_dict.get(ticker, row.get('current_score', float('inf')))
            if score < lowest_score:
                lowest_score = score
                lowest_ticker = ticker

        if lowest_ticker is None or lowest_score > score_threshold:
            return self.holdings.iloc[0:0]

        result = self.holdings[self.holdings['ticker'] == lowest_ticker]

        if result.empty:
            return self.holdings.iloc[0:0]

        return result.iloc[[0]]

    def get_num_slot_used(self):
        return self.holdings['slot'].sum()

    def get_df(self, attr):
        val = getattr(self, attr, None)
        if val is None:
            raise ValueError(f"Attribute '{attr}' not found.")
        return pd.DataFrame(val)


class SimulationV2:
    def __init__(self, data, initial_amount, cut_loss, num_slot, ratio_deal_volume, review_frequency,
                 fee_rate=0.001):
        self.data = data
        self.cut_loss = cut_loss
        self.num_slot = num_slot
        self.ratio_deal_volume = ratio_deal_volume
        self.review_frequency = review_frequency
        self.portfolio = Portfolio(initial_amount, num_slot)
        self.fee_rate = fee_rate
        self.last_review_ymd = None
        self.ymd_list = sorted(data['time'].unique())

    def _get_slot_from_score(self, score):
        if score >= 3:
            return 3
        elif score >= 2:
            return 2
        elif score >= 1:
            return 1
        return 0

    def _is_review_day(self, ymd):
        if self.review_frequency == 'weekly':
            return pd.to_datetime(ymd).weekday() == 4  # Friday
        elif self.review_frequency == 'monthly':
            ymd_dt = pd.to_datetime(ymd)
            next_day = ymd_dt + pd.Timedelta(days=1)
            return next_day.month != ymd_dt.month  # Last day of month
        elif self.review_frequency == 'quarterly':
            ymd_dt = pd.to_datetime(ymd)
            next_day = ymd_dt + pd.Timedelta(days=1)
            return (next_day.month - 1) // 3 != (ymd_dt.month - 1) // 3  # Last day of quarter
        return False

    def _sell_rule(self, hold_quantity, hold_buy_price, hold_investment, volume, price, adj_price, ymd_sell, ymd_buy):
        def calculate_delta_price(v_sell, v_prev, k=0.1, p_latest=1):
            if np.isnan(v_sell) or np.isnan(v_prev) or v_prev == 0 or v_sell == 0:
                return 0.1
            return min(0.1, p_latest * (1 - np.exp(-k * (v_sell / v_prev))))

        if pd.to_datetime(ymd_sell) - pd.to_datetime(ymd_buy) < pd.Timedelta(days=3):
            return None, None, None, None

        delta = calculate_delta_price(hold_quantity, volume)

        # if active_deal['Low_sell'] == active_deal['High_sell'] == active_deal['Open_sell'] == active_deal[
        #     'Close_sell'] == active_deal['Close_T1_sell']:
        #     delta = max(delta, 0.2)
        exec_adj_price = adj_price * (1 - delta)
        fee = price * (1 - delta) * hold_quantity * self.fee_rate
        proceeds = hold_investment * (exec_adj_price / hold_buy_price)

        return exec_adj_price, hold_quantity, fee, proceeds

    def _buy_rule(self, volume_p50_1m, price, adj_price):
        volume_limit = volume_p50_1m * self.ratio_deal_volume
        # value_limit = (self.portfolio.daily_stats[-1]['nav'] / self.num_slot
        #                if self.portfolio.daily_stats else self.portfolio.cash / (self.num_slot))

        slots_used = self.portfolio.get_num_slot_used()
        if slots_used <= self.num_slot:
            value_limit = (self.portfolio.daily_stats[-1]['nav'] / self.num_slot
                           if self.portfolio.daily_stats else self.portfolio.cash / self.num_slot)
        else:
            value_limit = self.portfolio.cash

        max_quantity = min(int(volume_limit), int(value_limit // price))

        if max_quantity > 0:
            total_cost = max_quantity * price * (1 + self.fee_rate)
            if total_cost > self.portfolio.cash:
                max_quantity = int(self.portfolio.cash // (price * (1 + self.fee_rate)))
            if max_quantity > 0:
                fee = price * max_quantity * self.fee_rate
                investment_amount = price * max_quantity

                return adj_price, max_quantity, fee, investment_amount

        return None, None, None, None

    def run(self):
        max_slot = 0
        for ymd in self.ymd_list:
            print(f"[RUN] Processing day {ymd}")
            day_data = self.data[self.data['time'] == ymd]
            price_dict = dict(zip(day_data['ticker'], day_data['price']))
            adj_price_dict = dict(zip(day_data['ticker'], day_data['close']))
            score_dict = dict(zip(day_data['ticker'], day_data['score']))
            volume_dict = dict(zip(day_data['ticker'], day_data['volume']))
            volume_p50_dict = dict(zip(day_data['ticker'], day_data['volume_p50_1m']))
            # 1. REVIEW PORTFOLIO
            if self._is_review_day(ymd):
                print('Review day: ', ymd)
                # Sell stocks poorly (score drops sharply and below BUY_THRESHOLD)
                low_score_tickers = []
                # Iterate over a copy of index to avoid bugs when removing
                for idx in list(self.portfolio.holdings.index):
                    row = self.portfolio.holdings.loc[idx]
                    score = score_dict.get(row['ticker'], row['current_score'])
                    if row['initial_score'] - score >= DELTA_DROP and score < BUY_THRESHOLD:
                        low_score_tickers.append(row['ticker'])

                for ticker in low_score_tickers:
                    row = self.portfolio.holdings[self.portfolio.holdings['ticker'] == ticker].iloc[0]

                    price_sell, quantity, fee, proceeds = self._sell_rule(
                        hold_quantity=row['quantity'],
                        hold_buy_price=row['price_buy'],
                        hold_investment=row['investment_amount'],
                        volume=volume_dict.get(ticker, 0),
                        price=price_dict.get(ticker, row['price_buy']),
                        adj_price=adj_price_dict.get(ticker, row['price_buy']),
                        ymd_sell=ymd,
                        ymd_buy=row['ymd_buy']
                    )
                    if not proceeds:
                        continue

                    self.portfolio.remove_holding(ticker, quantity, price_sell, ymd, fee)
                    self.portfolio.cash += proceeds - fee
                    self.portfolio.record_transaction(ymd, ticker, 'sell', price_sell, quantity, fee, proceeds)
                    assert self.portfolio.cash >= 0, f"Cash < 0 at {ymd} after sell {ticker}"

                # If there is a very good stock (score 3+), rotate portfolio to get slot
                high_score = day_data[day_data['score'] >= 3]
                for idx, row in high_score.iterrows():
                    if row['ticker'] not in self.portfolio.holdings['ticker'].values:
                        # Find lowest score stock to sell and get slot
                        holdings_low = self.portfolio.get_holding_lowest_score(score_dict, score_threshold=1, ymd=ymd)
                        if not holdings_low.empty:
                            low_row = holdings_low.iloc[0]
                            price_sell, quantity, fee, proceeds = self._sell_rule(
                                hold_quantity=low_row['quantity'],
                                hold_buy_price=low_row['price_buy'],
                                hold_investment=low_row['investment_amount'],
                                volume=volume_dict.get(low_row['ticker'], 0),
                                price=price_dict.get(low_row['ticker'], low_row['price_buy']),
                                adj_price=adj_price_dict.get(low_row['ticker'], low_row['price_buy']),
                                ymd_sell=ymd,
                                ymd_buy=low_row['ymd_buy']
                            )
                            if not proceeds:
                                continue

                            self.portfolio.remove_holding(low_row['ticker'], quantity, price_sell, ymd, fee)
                            self.portfolio.cash += proceeds - fee
                            self.portfolio.record_transaction(ymd, low_row['ticker'], 'sell', price_sell, quantity, fee,
                                                              proceeds)

                            assert self.portfolio.cash >= 0, f"Cash < 0 at {ymd} after rotation"

                # Rebalance (score up, down a lot), further logic can be customized later

            # 2. SELL: cutloss or score < SELL_THRESHOLD
            sold_tickers = []
            for _, row in self.portfolio.holdings.copy().iterrows():
                ticker = row['ticker']
                score = score_dict.get(ticker, row['current_score'])
                adj_price = adj_price_dict.get(ticker, row['price_buy'])
                # If score < SELL_THRESHOLD or price falls below cutloss
                cut_price = row['price_buy'] * (1 - self.cut_loss)
                if score < SELL_THRESHOLD or adj_price <= cut_price:
                    print(
                        f'sell {ticker} at {adj_price}, score < SELL_THRESHOLD: {score < SELL_THRESHOLD} or cutloss:{adj_price <= cut_price}')

                    price_sell, quantity, fee, proceeds = self._sell_rule(
                        hold_quantity=row['quantity'],
                        hold_buy_price=row['price_buy'],
                        hold_investment=row['investment_amount'],
                        volume=volume_dict.get(ticker, 0),
                        price=price_dict.get(ticker, row['price_buy']),
                        adj_price=adj_price,
                        ymd_sell=ymd,
                        ymd_buy=row['ymd_buy']
                    )
                    if not proceeds:
                        continue

                    self.portfolio.remove_holding(ticker, quantity, price_sell, ymd, fee)
                    self.portfolio.cash += proceeds - fee
                    self.portfolio.record_transaction(ymd, ticker, 'sell', price_sell, quantity, fee, proceeds)
                    sold_tickers.append(ticker)
                    assert self.portfolio.cash >= 0, f"Cash < 0 at {ymd} after cutloss {ticker}"

            # 3. BUY by slot & volume limit
            day_data_buy = day_data[day_data['score'] >= BUY_THRESHOLD].sort_values(by='score', ascending=False)
            # slots_used = self.portfolio.get_num_slot_used()
            # slot_left = self.num_slot - slots_used
            for idx, row in day_data_buy.iterrows():
                # if slot_left <= 0 or self.portfolio.cash <= LOWEST_CASH:
                if self.portfolio.cash <= LOWEST_CASH:
                    break

                ticker = row['ticker']
                score = row['score']
                slot_need = self._get_slot_from_score(score)
                if slot_need == 0 or (ticker in sold_tickers):
                    continue

                # Check if already holding this ticker, buy more if not enough slot
                if ticker in self.portfolio.holdings['ticker'].values:
                    current_slot = self.portfolio.holdings[self.portfolio.holdings['ticker'] == ticker]['slot'].sum()
                    if current_slot >= slot_need:
                        continue

                price_buy, max_quantity, fee, investment_amount = self._buy_rule(
                    volume_p50_1m=volume_p50_dict.get(ticker, 0),
                    price=price_dict.get(ticker, 0),
                    adj_price=adj_price_dict.get(ticker, 0))

                if not investment_amount:
                    continue

                print('buy: ', ticker)
                self.portfolio.update_holding(ticker, max_quantity, price_buy, ymd, fee, score, investment_amount)
                self.portfolio.cash -= investment_amount + fee
                self.portfolio.record_transaction(ymd, ticker, 'buy', price_buy, max_quantity, fee, investment_amount)

                # slot_left -= 1

            # Update NAV, daily_stats
            self.portfolio.update_nav(ymd, adj_price_dict)
            self.portfolio.update_score(score_dict)

            print(f"NAV: {self.portfolio.daily_stats[-1]['nav']}")
            print(f"  Portfolio cash at end of day: {self.portfolio.cash}, holdings:")
            print(self.portfolio.holdings)
            max_slot = max(max_slot, self.portfolio.get_num_slot_used())
            print(f"max_slot: {max_slot}")
            print('__________________________________')

    def get_output(self):
        return {
            'transactions': self.portfolio.get_df('transactions'),
            'completed_deals': self.portfolio.get_df('completed_deals'),
            'daily_stats': self.portfolio.get_df('daily_stats')
        }


# file = 'test_weight_score_5_block.csv'
# file = 'test_weight_score_5.csv'
# file = 'test_weight_score_no_clip.csv'
# file = 'test_weight_score_noclip_block.csv'
file = 'test_weight_score_deal_block_9m_10.csv'
#
df = pd.read_csv(file)
# df['time'] = pd.to_datetime(df['time'], format='%m/%d/%Y')
df = df[df['time'] >= '2014-01-01']
df = df[df['close'] != 0].copy()
df = df.dropna()

# import random
# df['score'] = [float(random.randint(-3,3)) for _ in range(len(df))]


now = time.time()
a = SimulationV2(df, 50e9, 0.15, 25, 0.1, 'quarterly', 0.0015)
a.run()

print(time.time() - now)
print(file)
