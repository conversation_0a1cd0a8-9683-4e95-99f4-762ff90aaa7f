from typing import List, Dict, <PERSON><PERSON>, Any

import pandas as pd




import pandas as pd
from typing import List, Dict, Tuple, Any

class Portfolio:
    def __init__(self, investment_amount: float, num_slot: int, cut_loss: float, ratio_deal_volume: float, fee: float):
        self.cash = investment_amount
        self.num_slot = num_slot
        self.cut_loss = cut_loss
        self.ratio_deal_volume = ratio_deal_volume
        self.fee = fee
        self.holdings = pd.DataFrame(columns=[
            'ticker', 'buy_price', 'quantity', 'buy_date', 'slot', 'score_at_buy', 'fee', 'history'
        ])
        self.transactions: List[dict] = []
        self.daily_stats: List[dict] = []

    def get_current_slots(self) -> int:
        return self.holdings['slot'].sum() if not self.holdings.empty else 0

    def get_max_buy_quantity(self, ticker: str, price: float, volume_p50_1m: float, nav: float) -> int:
        max_by_nav = nav / self.num_slot // (price * (1 + self.fee))
        max_by_liquidity = volume_p50_1m * self.ratio_deal_volume
        max_by_cash = self.cash // (price * (1 + self.fee))
        return int(min(max_by_nav, max_by_liquidity, max_by_cash))

    def get_max_sell_quantity(self, ticker: str, volume_p50_1m: float) -> int:
        max_by_liquidity = volume_p50_1m * self.ratio_deal_volume
        holding_qty = self.holdings[self.holdings['ticker'] == ticker]['quantity'].sum()
        return int(min(holding_qty, max_by_liquidity))

    def update_nav_and_stats(self, ymd: str, df_today: pd.DataFrame):
        nav = self.cash
        for _, row in self.holdings.iterrows():
            price_row = df_today[df_today['ticker'] == row['ticker']]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            nav += row['quantity'] * price
        self.daily_stats.append({'ymd': ymd, 'nav': nav, 'cash': self.cash})

    def sell(self, df_today: pd.DataFrame):
        """
        Sell holdings if score < 0.5 or price drops below cutloss threshold.
        """
        to_remove = []
        for idx, row in self.holdings.iterrows():
            price_row = df_today[df_today['ticker'] == row['ticker']]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            score = price_row.iloc[0]['score']
            volume_p50_1m = price_row.iloc[0]['volume_p50_1m']
            sell_reason = None
            if price < row['buy_price'] * (1 - self.cut_loss):
                sell_reason = 'cutloss'
            elif score < 0.5:
                sell_reason = 'score_drop'
            if sell_reason:
                max_qty = self.get_max_sell_quantity(row['ticker'], volume_p50_1m)
                sell_qty = min(row['quantity'], max_qty)
                if sell_qty <= 0:
                    continue
                self.transactions.append({
                    'ymd': price_row.iloc[0]['ymd'],
                    'ticker': row['ticker'],
                    'action': 'sell',
                    'price': price,
                    'quantity': sell_qty,
                    'fee': sell_qty * price * self.fee,
                    'reason': sell_reason,
                })
                self.cash += sell_qty * price * (1 - self.fee)
                self.holdings.at[idx, 'quantity'] -= sell_qty
                if self.holdings.at[idx, 'quantity'] <= 0:
                    to_remove.append(idx)
        self.holdings.drop(index=to_remove, inplace=True)
        self.holdings.reset_index(drop=True, inplace=True)

    def buy(self, df_today: pd.DataFrame):
        """
        Buy stocks with score >= 1, prioritizing higher scores, respecting slot, NAV/slot, and liquidity constraints.
        """
        slot_used = self.get_current_slots()
        df_buy = df_today[df_today['score'] >= 1].copy()
        if df_buy.empty:
            return
        df_buy = df_buy.sort_values('score', ascending=False)
        for _, row in df_buy.iterrows():
            score = row['score']
            if score >= 3:
                slot = 3
            elif score >= 2:
                slot = 2
            else:
                slot = 1
            if slot_used + slot > self.num_slot:
                continue
            max_qty = self.get_max_buy_quantity(row['ticker'], row['price'], row['volume_p50_1m'], self.get_nav(df_today))
            if max_qty <= 0:
                continue
            # Only buy if not already holding this ticker with enough slot
            current_slot = self.holdings[self.holdings['ticker'] == row['ticker']]['slot'].sum() if not self.holdings.empty else 0
            if current_slot + slot > self.num_slot:
                continue
            # Buy as much as possible within constraints
            buy_qty = max_qty
            cost = buy_qty * row['price'] * (1 + self.fee)
            if cost > self.cash or buy_qty <= 0:
                continue
            self.holdings = pd.concat([
                self.holdings,
                pd.DataFrame([{
                    'ticker': row['ticker'],
                    'buy_price': row['price'],
                    'quantity': buy_qty,
                    'buy_date': row['ymd'],
                    'slot': slot,
                    'score_at_buy': score,
                    'fee': buy_qty * row['price'] * self.fee,
                    'history': '',
                }])
            ], ignore_index=True)
            self.cash -= cost
            self.transactions.append({
                'ymd': row['ymd'],
                'ticker': row['ticker'],
                'action': 'buy',
                'price': row['price'],
                'quantity': buy_qty,
                'fee': buy_qty * row['price'] * self.fee,
                'reason': 'score_buy',
            })
            slot_used += slot
            if slot_used >= self.num_slot:
                break

    def review_portfolio(self, df_today: pd.DataFrame):
        """
        Advanced review logic for rebalance/rotation on review days.
        Example: sell low-score holdings to buy high-score (score >= 3) stocks.
        """
        # Find high-score candidates
        high_score = df_today[df_today['score'] >= 3]
        if high_score.empty or self.holdings.empty:
            return
        # Sell lowest score holdings if not enough slot for high-score
        slot_used = self.get_current_slots()
        for _, high_row in high_score.iterrows():
            slot_needed = 3
            if slot_used + slot_needed > self.num_slot:
                # Sell lowest score holding(s) to make room
                low_score_holdings = self.holdings.sort_values('score_at_buy', ascending=True)
                for idx, low_row in low_score_holdings.iterrows():
                    price_row = df_today[df_today['ticker'] == low_row['ticker']]
                    if price_row.empty:
                        continue
                    price = price_row.iloc[0]['price']
                    volume_p50_1m = price_row.iloc[0]['volume_p50_1m']
                    max_qty = self.get_max_sell_quantity(low_row['ticker'], volume_p50_1m)
                    sell_qty = min(low_row['quantity'], max_qty)
                    if sell_qty <= 0:
                        continue
                    self.transactions.append({
                        'ymd': price_row.iloc[0]['ymd'],
                        'ticker': low_row['ticker'],
                        'action': 'sell',
                        'price': price,
                        'quantity': sell_qty,
                        'fee': sell_qty * price * self.fee,
                        'reason': 'rotation',
                    })
                    self.cash += sell_qty * price * (1 - self.fee)
                    self.holdings.at[idx, 'quantity'] -= sell_qty
                    slot_used -= low_row['slot']
                    if self.holdings.at[idx, 'quantity'] <= 0:
                        self.holdings.drop(idx, inplace=True)
                    if slot_used + slot_needed <= self.num_slot:
                        break
                self.holdings.reset_index(drop=True, inplace=True)

    def get_nav(self, df_today: pd.DataFrame) -> float:
        nav = self.cash
        for _, row in self.holdings.iterrows():
            price_row = df_today[df_today['ticker'] == row['ticker']]
            if price_row.empty:
                continue
            price = price_row.iloc[0]['price']
            nav += row['quantity'] * price
        return nav

    def calc_return(self, investment_amount: float) -> Dict[str, Any]:
        if not self.daily_stats:
            return {'total_return': 0, 'annualized_return': 0}
        nav_start = self.daily_stats[0]['nav']
        nav_end = self.daily_stats[-1]['nav']
        days = len(self.daily_stats)
        total_return = (nav_end - nav_start) / investment_amount
        annualized_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        return {'total_return': total_return, 'annualized_return': annualized_return}


def simulation_v2(
    data: pd.DataFrame,
    investment_amount: float,
    cut_loss: float,
    num_slot: int,
    ratio_deal_volume: float,
    review_frequency: str,
    fee: float = 0.0015
) -> Tuple[pd.DataFrame, pd.DataFrame, dict]:
    data = data.sort_values('ymd')
    trading_days = sorted(data['ymd'].unique())
    review_days = get_review_days(trading_days, review_frequency)
    pf = Portfolio(investment_amount, num_slot, cut_loss, ratio_deal_volume, fee)

    for ymd in trading_days:
        df_today = data[data['ymd'] == ymd].copy()
        # On review days, run advanced review logic (rebalance/rotation)
        if ymd in review_days:
            pf.review_portfolio(df_today)
        # Always check for sell (score < 0.5, cutloss)
        pf.sell(df_today)
        # Always check for buy (score >= 1, slot/NAV/volume/fee constraints)
        pf.buy(df_today)
        # Update NAV, cash, daily_stats
        pf.update_nav_and_stats(ymd=ymd, df_today=df_today)

    si_return = pf.calc_return(investment_amount)
    return pd.DataFrame(pf.transactions), pd.DataFrame(pf.daily_stats), si_return

def get_review_days(trading_days: List[str], review_frequency: str) -> List[str]:
    """
    Return the list of review days based on the specified frequency: 'weekly', 'monthly', 'quarterly'.
    """
    dates = pd.to_datetime(trading_days)
    if review_frequency == 'weekly':
        return dates.groupby(dates.to_period('W')).max().dt.strftime('%Y-%m-%d').tolist()
    elif review_frequency == 'monthly':
        return dates.groupby(dates.to_period('M')).max().dt.strftime('%Y-%m-%d').tolist()
    elif review_frequency == 'quarterly':
        return dates.groupby(dates.to_period('Q')).max().dt.strftime('%Y-%m-%d').tolist()
    else:
        return trading_days
