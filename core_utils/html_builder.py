import pandas as pd
import json
import re
from functools import partial

class HTMLBlockBuilder:
    """
    Utility class for building HTML email/report blocks dynamically from various data types.
    Supports text, tables, dictionaries, lists, and pandas DataFrames.
    """

    def __init__(self):
        """Initialize a new empty HTML block builder."""
        self.blocks = []

    def add_text(self, text, style='normal'):
        """
        Add a text block with optional styling.

        Args:
            text (str): The content to render.
            style (str): One of 'normal', 'headline', 'subheadline', 'note',
                         'section_title', 'bullet', 'code'.
        """
        if style == 'headline':
            html = f"<h2>{text}</h2>"
        elif style == 'subheadline':
            html = f"<h4>{text}</h4>"
        elif style == 'note':
            html = f"<p style='font-size:12px; font-style:italic; color:#555;'>{text}</p>"
        elif style == 'section_title':
            html = f"<h3 style='margin-top:20px;'>{text}</h3>"
        elif style == 'bullet':
            html = f"<ul><li>{text}</li></ul>"
        elif style == 'code':
            html = f"<pre style='background:#f5f5f5; padding:10px; font-size:13px; font-family:monospace;'>{text}</pre>"
        else:
            html = f"<p>{text}</p>"
        self.blocks.append(html)

    def add_line_break(self, type="br", count=1):
        """
        Add a line break or horizontal rule.

        Args:
            type (str): "br" for <br>, anything else for <hr>.
            count (int): Number of times to repeat the tag.
        """
        tag = "<br>" if type == "br" else "<hr style='margin: 15px 0;'>"
        self.blocks.append(tag * count)

    def add_dict(self, data, as_table=True):
        """
        Add a dictionary to the HTML body, rendered as a table or pretty-printed JSON.

        Args:
            data (dict): Dictionary to render.
            as_table (bool): If True, render as key-value table. Otherwise render as JSON block.
        """
        if as_table:
            rows = ''.join(
                f"<tr><td style='font-weight:bold;'>{k}</td><td>{json.dumps(v) if isinstance(v, (dict, list)) else v}</td></tr>"
                for k, v in data.items()
            )
            html = f"<table class='styled-table' style='margin-bottom:10px;'>{rows}</table>"
        else:
            pretty_json = json.dumps(data, indent=2, ensure_ascii=False)
            html = f"<pre style='background:#f5f5f5; padding:10px; font-size:13px;'>{pretty_json}</pre>"
        self.blocks.append(html)

    def add_dataframe(self, df: pd.DataFrame, title=None, note=None, formatters=None, table_class="styled-table", column_format_map=None):
        """
        Add a pandas DataFrame as a styled HTML table with optional title, note, and formatting.

        Args:
            df (pd.DataFrame): The DataFrame to render.
            title (str): Optional table heading.
            note (str): Optional note below the table.
            formatters (dict): Per-column formatting functions.
            table_class (str): CSS class for the table.
            column_format_map (dict): Mapping of column names to format string, e.g., { "Price": ",.0f" }.
        """
        if formatters is None:
            if column_format_map:
                def make_formatter(fmt):
                    def f(val):
                        try:
                            cleaned = str(val).replace(",", "").strip()
                            num = float(cleaned) if '.' in cleaned or 'e' in cleaned.lower() else int(cleaned)
                            formatted = format(num, fmt)
                            return f"<span style='font-weight:bold; font-size:15px;'>{formatted}</span>"
                        except:
                            return str(val)
                    return f

                formatters = {
                    col: make_formatter(fmt)
                    for col, fmt in column_format_map.items()
                    if col in df.columns
                }
            else:
                formatters = self.auto_format_numeric(df)

        table_html = df.to_html(
            escape=False,
            index=False,
            border=1,
            classes=table_class,
            formatters=formatters
        )

        parts = []

        table_wrapper = table_html
        if df.shape[1] > 8:
            table_wrapper = f"<div style='overflow-x:auto; padding-bottom: 4px;'>{table_html}</div>"

        if title:
            parts.append(f"<h4 style='margin-bottom:5px;'>{title}</h4>")
        parts.append(table_wrapper)
        if note:
            parts.append(f"<p class='note'>{note}</p>")
        self.blocks.append("\n".join(parts))

    def add_data(self, data, type_hint=None, **kwargs):
        """
        Add any supported type: string, DataFrame, dict, list.
        Dispatches to appropriate internal add_* method.

        Args:
            data (Any): The data to render (string, dict, list, DataFrame).
            type_hint (str): Optional type hint: 'json', 'headline', etc.
            **kwargs: Forwarded to add_dataframe().
        """
        if isinstance(data, str):
            self.add_text(data, style=type_hint or "normal")
        elif isinstance(data, pd.DataFrame):
            self.add_dataframe(data, **kwargs)
        elif isinstance(data, dict):
            self.add_dict(data, as_table=(type_hint != "json"))
        elif isinstance(data, list):
            if all(isinstance(x, dict) for x in data):
                df = pd.DataFrame(data)
                self.add_dataframe(df, **kwargs)
            else:
                self.add_text("<br>".join(map(str, data)), style="bullet")
        else:
            self.add_text(str(data), style="normal")

    def render(self, with_wrapper=True):
        """
        Render the entire HTML document.

        Args:
            with_wrapper (bool): If True, wrap in full <html> template. Otherwise return only body.

        Returns:
            str: The HTML string.
        """
        body = "\n".join(self.blocks)
        if not with_wrapper:
            return body
        return f"""
        <html>
        <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, sans-serif;
                padding: 20px;
            }}
            table.styled-table {{
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 15px;
            }}
            table.styled-table th {{
                background-color: #48e0c1;
                color: black;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #ccc;
                padding: 6px;
                text-align: center;
            }}
            table.styled-table td {{
                border: 1px solid #ccc;
                padding: 6px;
                text-align: center;
                font-size: 13px;
            }}
            .note {{
                font-size: 12px;
                color: #777;
                font-style: italic;
                margin-top: 8px;
                margin-bottom: 20px;
            }}
        </style>
        </head>
        <body>
            {body}
        </body>
        </html>
        """.strip()

    def auto_format_numeric(self, df: pd.DataFrame):
        """
        Automatically detect numeric columns and apply formatting.

        Args:
            df (pd.DataFrame): Input DataFrame to scan.

        Returns:
            dict: Mapping of column names to formatters.
        """
        def formatter(val, fmt=",.2f"):
            try:
                val_str = str(val).strip()

                if isinstance(val, (int, float)) and pd.notnull(val):
                    return f"<span style='font-size:15px; color:#222;'>{val:{fmt}}</span>"

                match = re.match(r"^([\d,\.eE+-]+)\s*(\w+)?$", val_str)
                if match:
                    raw_num, unit = match.groups()
                    num = float(raw_num.replace(",", ""))
                    formatted = f"{num:{fmt}}"
                    return f"<span style='font-size:15px; color:#222;'>{formatted}{' ' + unit if unit else ''}</span>"

                return str(val)
            except:
                return str(val)

        return {
            col: partial(formatter, fmt=",.2f")
            for col in df.columns
        }



if __name__ == "__main__":
    #####################################
    # Sử dụng add data wrapper

    builder = HTMLBlockBuilder()

    # Headline & note
    builder.add_data("📊 Báo cáo tổng hợp dữ liệu", type_hint="headline")
    builder.add_data("Dữ liệu được thu thập và xử lý tự động từ hệ thống SmartStock", type_hint="note")

    builder.add_line_break("br")

    # Dict → bảng
    builder.add_data({"Date": "2025-07-10", "Analyst": "HHải", "Region": "VN"}, type_hint="table")

    # Dict → JSON
    builder.add_data({"symbols": ["FPT", "VIC", "VHM"], "config": {"mode": "aggressive", "threshold": 0.8}}, type_hint="json")

    builder.add_line_break("hr")

    # Normal text
    builder.add_data("🔥 Các bảng dữ liệu chi tiết bên dưới:")

    # DataFrame thường, có format số, note
    df1 = pd.DataFrame([
        {"Ticker": "FPT", "Price": 1234567.891, "Qty": 7, "Score": 4.7},
        {"Ticker": "VIC", "Price": 920000, "Qty": 23, "Score": 3.2},
        {"Ticker": "VHM", "Price": None, "Qty": 3, "Score": 2}
    ])
    builder.add_data(df1, title="📈 Bảng giao dịch", note="Giá trị được tính theo đơn vị VNĐ", column_format_map={
        "Price": ",.2f",
        "Qty": "02d",
        "Score": ".1f"
    })

    # DataFrame với 10 cột (trigger scroll ngang)
    df2 = pd.DataFrame([
        {f"Col{i}": i * 10 for i in range(1, 11)}
    ])
    builder.add_data(df2, title="Bảng rộng cần scroll")

    # Cell dài + truncate
    df3 = pd.DataFrame([
        {"Log": "This is a very long text that should be truncated and show tooltip for full content of the cell in the table UI."},
        {"Log": "Short one"}
    ])
    builder.add_data(df3, title="Log truncate test")

    # JSON-log dạng 1 cột toàn text dài
    df4 = pd.DataFrame([
        {"Log": json.dumps({"event": "SELL", "ticker": "VIC", "price": 72000, "volume": 150000}, indent=2)},
        {"Log": json.dumps({"event": "BUY", "ticker": "FPT", "price": 105000, "volume": 300000}, indent=2)}
    ])
    builder.add_data(df4)

    # List → convert thành bảng
    builder.add_data([
        {"Ticker": "MSN", "Price": 90000},
        {"Ticker": "HPG", "Price": 27000}
    ], title="List → bảng test")

    # List text → bullet
    builder.add_data(["Check lại volume", "Cập nhật profit chart", "Alert RSI"], type_hint="bullet")

    html = builder.render()

    with open("test.html", "w", encoding="utf-8") as f:
        f.write(html)


    #####################################################
    builder = HTMLBlockBuilder()

    # =============================
    # 1. Headline và mô tả tổng quan
    # =============================
    builder.add_text("📈 Báo cáo phân tích tuần", style="headline")
    builder.add_text("Tổng hợp dữ liệu giao dịch nổi bật trong tuần", style="note")
    builder.add_line_break("br")

    # =============================
    # 2. Cấu hình hệ thống
    # =============================
    builder.add_text("🔧 Cấu hình hệ thống", style="section_title")

    config = {
        "engine": "SmartStock AI",
        "analyst": "HHải",
        "version": "v2.0.1",
        "threshold": 0.8,
        "tags": ["strategy", "auto", "quant"]
    }
    builder.add_dict(config, as_table=True)
    builder.add_dict(config, as_table=False)  # hiển thị dạng JSON thô

    builder.add_line_break("hr")

    # =============================
    # 3. Dữ liệu bảng + formatters thủ công
    # =============================
    builder.add_text("📊 Giao dịch nổi bật", style="section_title")

    df = pd.DataFrame([
        {"Ticker": "FPT", "Price": "1234567.821", "Volume": "9200000", "Score": 4.7},
        {"Ticker": "VIC", "Price": "870000", "Volume": "N/A", "Score": 3.2},
        {"Ticker": "HPG", "Price": "21500.0", "Volume": 10500000, "Score": None}
    ])

    # formatter thủ công cho từng cột
    formatters = {
        "Price": lambda
            v: f"<span style='color:#0c0; font-weight:bold;'>{float(str(v).replace(',', '')):,.0f}</span>" if str(
            v).replace(',', '').replace('.', '').isdigit() else v,
        "Score": lambda v: f"<b>{v:.1f}</b>" if isinstance(v, (int, float)) else v
    }

    builder.add_dataframe(df, title="Top giao dịch", note="Giá trị theo VNĐ", formatters=formatters)

    # không có formatter, auto format numeric
    builder.add_dataframe(df, title="Top giao dịch", note="Giá trị theo VNĐ")

    # =============================
    # 4. Bảng nhiều cột để test scroll ngang
    # =============================
    df_wide = pd.DataFrame([
        {f"Col{i}": i * 100 for i in range(1, 12)}
    ])
    builder.add_text("🧾 Bảng nhiều cột (test scroll)", style="section_title")
    builder.add_dataframe(df_wide)


    # =============================
    # 6. JSON log → 1 cột toàn JSON, hiển thị dạng code
    # =============================
    json_logs = pd.DataFrame([
        {"Log": json.dumps({"event": "SELL", "ticker": "MWG", "price": 102000, "volume": 150000})},
        # {"Log": json.dumps({"event": "BUY", "ticker": "VHM", "price": 87000, "volume": 300000})}
    ])
    builder.add_text("📄 Log JSON dạng text", style="section_title")
    builder.add_dataframe(json_logs)

    # =============================
    # 7. List dict → render thành bảng
    # =============================
    builder.add_text("🧮 Danh sách theo dõi", style="section_title")

    watchlist = [
        {"Ticker": "MSN", "Price": 90000},
        {"Ticker": "VRE", "Price": 27000}
    ]
    builder.add_dataframe(pd.DataFrame(watchlist), title="Watchlist", column_format_map={"Price": ",.0f"})

    # =============================
    # 8. List text → bullet
    # =============================
    builder.add_text("📝 Việc cần kiểm tra:", style="section_title")
    tasks = ["Kiểm tra volume bất thường", "Kiểm tra MA breakout", "Cập nhật RSI"]
    builder.add_text("<br>".join(tasks), style="bullet")

    # =============================
    # Render ra file HTML
    # =============================
    html = builder.render()

    with open("report_variant.html", "w", encoding="utf-8") as f:
        f.write(html)


