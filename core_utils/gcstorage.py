import pandas as pd
from google.cloud import storage
from google.cloud.storage import transfer_manager
from pathlib import Path
from oauth2client.service_account import ServiceAccountCredentials
import os
import io
os.environ["SERVICE"] = "GCS"
from core_utils.log import logger


class GoogleCloudStorageService:
    def __init__(self, bucket_name):
        """
        Initialization function to connect to Google Cloud Storage.
        - bucket_name: The name of the bucket in Google Cloud Storage that you want to interact with.
        """
        self.bucket_name = bucket_name
        # first way
        # credentials = ServiceAccountCredentials.from_json_keyfile_dict(
        #     {
        #         'type': 'service_account',
        #         'client_id': os.environ['GCS_CLIENT_ID'],
        #         'client_email': os.environ['GCS_CLIENT_EMAIL'],
        #         'private_key_id': os.environ['GCS_PRIVATE_KEY_ID'],
        #         'private_key': os.environ['GCS_PRIVATE_KEY'],
        #     }
        # )
        # self.client = storage.Client(credentials=credentials,
        #                              project='myproject')  # Initialize the client to connect to Google Cloud Storage

        # seccond way
        self.client = storage.Client.from_service_account_json(os.environ['GOOGLE_APPLICATION_CREDENTIALS'])
        self.bucket = self.client.bucket(bucket_name)

    def upload_from_file(self, source_file_path, destination_blob_name):
        """
        Upload a file from the local machine to Google Cloud Storage.
        - source_file_path: Path to the file to be uploaded from the local system.
        - destination_blob_name: The name of the file when stored in the bucket.
        """
        blob = self.bucket.blob(destination_blob_name)
        blob.upload_from_filename(source_file_path)
        logger.info(f"File {source_file_path} uploaded to {destination_blob_name}.")

    def update_from_directory(self, source_directory_path, workers=4):
        """
        Upload multiple files from the local machine to Google Cloud Storage in parallel.
        - source_directory_path: Path to the directory to be uploaded from the local system.
        - workers: The number of concurrent workers to use for uploading files.
        """
        # Generate a list of paths (in string form) relative to the `directory`.
        # This can be done in a single list comprehension, but is expanded into

        # First, recursively get all files in `directory` as Path objects.
        directory_as_path_obj = Path(source_directory_path)
        paths = directory_as_path_obj.rglob("*")

        # Filter so the list only includes files, not directories themselves.
        file_paths = [path for path in paths if path.is_file()]

        # These paths are relative to the current working directory. Next, make them
        # relative to `directory`
        relative_paths = [path.relative_to(source_directory_path) for path in file_paths]

        # Finally, convert them all to strings.
        string_paths = [str(path) for path in relative_paths]
        logger.info("Found {} files.".format(len(string_paths)))

        # Start the upload.
        results = transfer_manager.upload_many_from_filenames(
            self.bucket, string_paths, source_directory=source_directory_path, max_workers=workers
        )

        for name, result in zip(string_paths, results):
            # The results list is either `None` or an exception for each filename in
            # the input list, in order.

            if isinstance(result, Exception):
                logger.info("Failed to upload {} due to exception: {}".format(name, result))
            else:
                logger.info("Uploaded {} to {}.".format(name, self.bucket.name))

    def upload_from_memory(self, contents, destination_blob_name):
        """Uploads a file to the bucket."""
        # The contents to upload to the file
        if isinstance(contents, pd.DataFrame):
            csv_buffer = io.StringIO()
            contents.to_csv(csv_buffer, index=False)
            contents = csv_buffer.getvalue()

        blob = self.bucket.blob(destination_blob_name)
        blob.upload_from_string(contents, content_type="text/csv")

        logger.info(f"Uploaded contents to {destination_blob_name}.")

    def download_file(self, blob_name, destination_file_path):
        """
        Download a file from Google Cloud Storage to the local machine.
        - blob_name: The name of the file in the bucket.
        - destination_file_path: The path to save the file on the local system.
        """
        blob = self.bucket.blob(blob_name)
        blob.download_to_filename(destination_file_path)
        logger.info(f"File {blob_name} downloaded to {destination_file_path}.")

    def download_files(self, blob_names, destination_file_path, workers=4):
        """
        Download multiple files from Google Cloud Storage to the local machine in parallel.
        - blob_names: A list of the names of the files in the bucket.
        - destination_file_path: The path to save the files on the local system.
        """
        # import concurrent.futures
        # with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
        #     futures = [executor.submit(self.download_file, blob_name, destination_file_path) for blob_name in blob_names]
        #     for future in concurrent.futures.as_completed(futures):
        #         future.result()

        results = transfer_manager.download_many_to_path(
            self.bucket, blob_names, destination_directory=destination_file_path, max_workers=workers
        )

        for name, result in zip(blob_names, results):
            # The results list is either `None` or an exception for each blob in
            # the input list, in order.

            if isinstance(result, Exception):
                logger.info("Failed to download {} due to exception: {}".format(name, result))
            else:
                logger.info("Downloaded {} to {}.".format(name, destination_file_path + name))

    def download_file_to_memory(self, blob_name):
        """Downloads a blob into memory."""

        # Construct a client side representation of a blob.
        # Note `Bucket.blob` differs from `Bucket.get_blob` as it doesn't retrieve
        # any content from Google Cloud Storage. As we don't need additional data,
        # using `Bucket.blob` is preferred here.
        blob = self.bucket.blob(blob_name)
        contents = blob.download_as_bytes()

        logger.info(f"Downloaded file {blob_name} with size {len(contents)/1000} Kbytes.")
        return contents

    def delete_file(self, blob_name):
        """
        Delete a file from Google Cloud Storage.
        - blob_name: The name of the file in the bucket.
        """
        blob = self.bucket.blob(blob_name)
        if blob.exists():
            blob.delete()
        logger.info(f"File {blob_name} deleted from bucket.")

    def get_file_url(self, blob_name):
        """
        Get the public URL of a file.
        - blob_name: The name of the file in the bucket.
        """
        blob = self.bucket.blob(blob_name)
        url = blob.public_url
        logger.info(f"Public URL for {blob_name}: {url}")
        return url

    def list_files(self, prefix=None, delimiter=None):
        """
        List all files in the bucket.
        """
        blobs = self.bucket.list_blobs(prefix=prefix, delimiter=delimiter)
        all_files = []
        # logger.info("Files in bucket:")
        for blob in blobs:
            all_files.append(blob.name)

        return all_files

    def list_files_by_ticker(self, ticker, prefix=None, delimiter=None):
        blobs = self.bucket.list_blobs(prefix=prefix, delimiter=delimiter)
        all_files = []
        # logger.info("Files in bucket:")
        for blob in blobs:
            all_files.append(blob.name)

        all_files = [x for x in all_files if ticker in x]
        return all_files

    def file_exists(self, blob_name):
        """
        Check if a file exists in the bucket.
        - blob_name: The name of the file in the bucket.
        """
        blob = self.bucket.blob(blob_name)
        return blob.exists()


# Example usage
if __name__ == "__main__":
    # Initialize the service with your bucket name
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "core_utils/env/gcskey.json"
    bucket_name = "tav2-gs"
    # gcs_service = GoogleCloudStorageService(bucket_name)
    #
    # ticker = "HPG"
    # path = "temp/"
    #
    # fa_pdraw = gcs_service.download_file_to_memory(f"preprocess/financial_report/{ticker}.csv")
    # pdraw = gcs_service.download_file_to_memory(f"preprocess/price/{ticker}.csv")
    # pd_adj = gcs_service.download_file_to_memory(f"preprocess/adjust_price/{ticker}.csv")
    #
    # gcs_service.download_files(f"preprocess/adjust_price/{ticker}.csv", path)
