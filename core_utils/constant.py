"""
Constants used throughout the project.
This module contains all the constant values that are used across different modules.
"""
import getpass
CURRENT_USER = getpass.getuser()

# Project Constants
RANKING = 'ranking'
MAX_DECREASE = 0.2
SIMULATE_FULL_CASH = "full_cash"
SIMULATE_FULL_CASH_NOT_FIX = "full_cash_not_fix_slot"
SIMULATE_ALLOCATE = "adjust_cash"
FPATH = 'ticker_v1a/'

NUM_PROCESS = 1
# API Constants
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"
API_TITLE = "TA-API"
API_DESCRIPTION = "Kaffa API for data processing and analysis"

# Joblib cache settings
JOBLIB_CACHE_DIR: str = f"/tmp/joblib_cache/{CURRENT_USER}"

# Server Constants
REDIS_HOST = "localhost"
REDIS_PORT = "6379"

MONGO_HOST = "localhost"
MONGO_PORT = "27017"

# File Paths
DATA_DIR = "data"
REPORT_DIR = "report"
LOG_DIR = "logs"

# Time Constants
DATE_FORMAT = "%Y-%m-%d"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
TIMEZONE = "Asia/Ho_Chi_Minh"

# Status Constants
STATUS_SUCCESS = "success"
STATUS_ERROR = "error"
STATUS_PENDING = "pending"
STATUS_COMPLETED = "completed"
STATUS_FAILED = "failed"

# HTTP Status Codes
HTTP_200_OK = 200
HTTP_201_CREATED = 201
HTTP_400_BAD_REQUEST = 400
HTTP_401_UNAUTHORIZED = 401
HTTP_403_FORBIDDEN = 403
HTTP_404_NOT_FOUND = 404
HTTP_500_INTERNAL_SERVER_ERROR = 500

# Cache Constants
CACHE_TTL = 3600  # 1 hour in seconds
CACHE_PREFIX = "kaffa:"

# Logging Constants
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Security Constants
JWT_SECRET_KEY = "your-secret-key"
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Worker Constants
WORKER_CONCURRENCY = 4
WORKER_MAX_TASKS = 1000
WORKER_TIMEOUT = 3600  # 1 hour in seconds

# File Types
ALLOWED_EXTENSIONS = {".csv", ".json", ".xlsx", ".xls"}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
