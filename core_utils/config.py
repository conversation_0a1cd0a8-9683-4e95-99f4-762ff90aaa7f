from pydantic_settings import BaseSettings

from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    # MongoDB Configuration
    MONGO_URL: str = os.getenv("MONGO_URL")
    MONGO_DB_NAME: str = os.getenv("MONGO_DB_NAME")

    # API Configuration
    API_V1_PREFIX: str = os.getenv("API_V1_PREFIX")
    PROJECT_NAME: str = os.getenv("PROJECT_NAME")
    DEBUG: bool = os.getenv("DEBUG", "True").lower() == "true"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT")
    API_KEY: str = os.getenv("API_KEY")

    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")

    class Config:
        case_sensitive = True
        env_file = "core_utils/env/.env"


settings = Settings()
