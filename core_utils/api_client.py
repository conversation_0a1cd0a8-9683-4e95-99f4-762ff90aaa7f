import requests
from datetime import date
from typing import List, Optional
from requests.exceptions import RequestException
import logging


class APIClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {"X-API-Key": api_key,
                        "Content-Type": "application/json"}
        self.logger = logging.getLogger(__name__)

    def _request(self, method: str, endpoint: str, **kwargs):
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.request(method, url, headers=self.headers, **kwargs)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            self.logger.error(f"API request failed: {e}")
            return None

    def get_recommends(self, target_date: str, signal_type: Optional[str] = None) -> List[dict]:
        params = {"target_date": target_date, "signal_type": signal_type}
        return self._request("GET", f"/recommends", params=params)

    def get_recommend_by_ticker(self, ticker: str, target_date: Optional[str] = None) -> List[dict]:
        params = {"target_date": target_date if target_date else None}
        return self._request("GET", f"/recommends/{ticker}", params=params)

    def insert_recommend(self, recommend_data: dict) -> bool:
        return self._request("POST", f"/recommends", json=recommend_data)

    def insert_recommends(self, recommend_data_list: List[dict]) -> bool:
        return self._request("POST", f"/recommends", json=recommend_data_list)

    def get_patterns(self) -> List[dict]:
        return self._request("GET", f"/patterns")

    def get_pattern(self, pattern: str) -> dict:
        return self._request("GET", f"/patterns/{pattern}")

    def update_pattern(self, pattern: str, data: dict) -> bool:
        return self._request("POST", f"/patterns/{pattern}", json=data)

    def insert_pattern(self, pattern_data: dict) -> bool:
        return self._request("POST", f"/patterns", json=pattern_data)

    def get_riskrating_by_ticker(self, ticker: str, quarter: Optional[str] = None) -> dict:
        params = {"quarter": quarter if quarter else None}
        return self._request("GET", f"/riskrating/{ticker}", params=params)

    def get_riskrating(self) -> List[dict]:
        return self._request("GET", f"/riskrating")

    def insert_risk_rating(self, riskrating_data: dict) -> bool:
        return self._request("POST", f"/riskrating", json=riskrating_data)

    def insert_risk_ratings(self, riskrating_data_list: List[dict]) -> bool:
        return self._request("POST", f"/riskrating", json=riskrating_data_list)
