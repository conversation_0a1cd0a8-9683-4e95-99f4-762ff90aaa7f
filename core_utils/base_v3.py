import json
import os
import traceback
from datetime import timedelta
from functools import wraps
import pandas as pd
from pathos.multiprocessing import ProcessingPool as Pool

import numpy as np
import pandas as pd
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR
from core_utils.redis_cache import EvalRedis
from core_utils.base_eval import BaseEval
from core_utils.mongodb import MongoDBClient

memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
memory.reduce_size(bytes_limit=5e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis()
mongo_service = MongoDBClient(host='localhost', port=27017, db_name='score')


class WeightEval(BaseEval):
    def __init__(self, stock, data_frame, dict_filter, weight, threshold_buy, threshold_sell, cutloss=0.15, lookback=5,
                 k_exp=0.0, cache_service=None):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         cache_service=cache_service)

        self.weight = self.init_weight(weight, dict_filter, threshold_sell, threshold_buy)
        self.threshold_buy = threshold_buy
        self.threshold_sell = threshold_sell
        self.lookback = lookback
        self.exp = k_exp
        self.score_df = self.find_score_base_on_weight()

    @staticmethod
    def init_weight(w, dict_filter, threshold_sell, threshold_buy):
        if isinstance(w, dict) and w != {}:
            w['Hold'] = threshold_sell
        else:
            w = {'Hold': threshold_sell}
            for k, _ in dict_filter.items():
                if k.startswith('~'):
                    w[k[1:]] = threshold_sell
                if k.startswith('_'):
                    w[k[1:]] = threshold_buy
        return w

    def _block_score(self, df, block_len: int = 30) -> pd.DataFrame:
        df = df.copy()

        df_sell_filtered = self.df_sell[self.df_sell["Sell_filter"].str.endswith("~", na=False)].sort_values("time")
        df_sell_filtered['month'] = df_sell_filtered['time'].map(lambda x: x[:7])
        df_sell_filtered = df_sell_filtered.drop_duplicates(subset=['month'], keep='first')

        # buy_idx = df_buy.index
        # buy_times = df_buy["time"]

        for _, sell_row in df_sell_filtered.iterrows():
            sell_time = sell_row["time"]
            end_time = (pd.to_datetime(sell_row["time"]) + pd.Timedelta(days=block_len)).strftime('%Y-%m-%d')

            mask = (df["time"] >= sell_time) & (df["time"] < end_time)
            df.loc[mask, 'score'] = self.threshold_sell

        return df

    def find_score_base_on_weight(self):
        """
        Input:
        weights: Weight patterns dictionary from hyperopt
        dict_filter: Filter pattern dictionary

        Output:
        si_rerturn

        """
        dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'Price': 'last', 'Open_1D': 'last',
                'Volume': 'last', 'score': 'last', 'n_month': 'sum', 'idx': 'last'}
        d_agg = {}

        df_b = self.get_buy_signals()
        df_s = self.get_sell_signals()

        dfs = [df for df in [df_b, df_s] if not df.empty]
        df = pd.concat(dfs, axis=0).sort_values('time', ascending=True)

        for col in ['filter', 'Sell_filter']:
            if col not in df.columns:
                df[col] = None

        df = df.reset_index(drop=True)
        df['idx'] = df.index

        df['score'] = 0
        for i in range(df.shape[0]):
            now_idx = df['idx'].iloc[i]
            lookback_idx = now_idx - 10
            slice = df.query('idx >= @lookback_idx & idx <= @now_idx')
            filters = slice['filter'].unique().tolist()
            sell_filters = slice['Sell_filter'].unique().tolist()
            add = [self.weight.get(f, 0.0) for f in filters]
            add = min(sum(add), 5)
            # add = sum(add)
            sub = [self.weight.get(f, 0.0) for f in sell_filters]
            sub = max(sum(sub), -5)
            # sub = sum(sub)

            # df.loc[df.index[i], 'score'] = add + sub
            df.at[i, 'score'] = add + sub

        # df['score'] = np.zeros(len(df))
        # unique_idx = np.unique(df['idx'].values)  # Lấy các index duy nhất
        # idx_arr = df['idx'].values
        # filter_arr = df[['filter', 'Sell_filter']].to_numpy()
        # score_dict = {}  # Dictionary để cache kết quả
        #
        # for now_idx in unique_idx:
        #     lookback_mask = (idx_arr >= now_idx - self.lookback) & (idx_arr <= now_idx)  # Lọc trong NumPy
        #     slice_idx = idx_arr[lookback_mask]
        #
        #     days_ago = now_idx - slice_idx
        #     decay_factor = np.exp(-self.exp * days_ago)  # Tính decay factor
        #
        #     slice_filters = filter_arr[lookback_mask].flatten()
        #
        #     weights = np.zeros_like(slice_filters, dtype=float)
        #     unique_filters = {}
        #
        #     for i in range(len(slice_filters) - 1, -1, -1):
        #         f = slice_filters[i]
        #         if pd.notna(f) and f not in unique_filters:
        #             unique_filters[f] = True
        #             weights[i] = self.weight.get(f, 0)
        #
        #     score = np.sum(weights * decay_factor.repeat(2))
        #     score_dict[now_idx] = score
        #
        # df['score'] = np.vectorize(score_dict.get)(df['idx'])

        for f in list(df.columns):
            if (f not in ['filter', 'ticker', 'time', 'quarter', 'month', 'week', 'sell_time', 'Sell_filter',
                          'sell_filter', 'buy_time', 'buy_filter', 'half_of_year', 'pre_sell',
                          'pre_sell_profit', 'pre_sell_id', 'FILTER']) and ("time" not in f):
                d_agg[f] = dAgg.get(f, 'mean')
        df = df.groupby(['ticker', 'time', 'score'], as_index=False).agg(d_agg)
        df = df.set_index('idx')
        #
        # handler for hold
        df['score_status'] = 'score'
        if df.iloc[-1]['score'] >= self.threshold_sell:
            df.at[df.index[-1], 'score'] = self.threshold_sell

        return df

    def get_df(self):
        # data: time, ticker, score, volume, price, volume_p50_1m.
        df_all = self.df_all[['time', 'ticker', 'Close', 'Price', 'Volume', 'Volume_1M_P50']].copy()

        df_all = df_all.merge(self.score_df[['time', 'score']], on=['time'], how='left')
        df_all = self._block_score(df_all, block_len=30)

        df_all['score'] = df_all['score'].ffill()
        df_all['score'] = df_all['score'].fillna(0.0)

        return df_all


class ScoreProvider:
    """
    Base class for a score provider (DL/ML/Heuristic...).
    Each provider must implement the get_score method.
    """

    def __init__(self, name, active=True, weight=1.0):
        self.name = name
        self.active = active
        self.weight = weight

    def calc_score(self) -> pd.DataFrame:
        """
        Return DataFrame with columns ['time', 'ticker', 'score'] (score column can be renamed).
        """
        raise NotImplementedError


class WeightScoreEval(ScoreProvider):
    def __init__(self, name=None, active=True, weight=1.0,
                 dict_filter=None, threshold_buy=1.0, threshold_sell=1.5,
                 cutloss=0.15, lookback=5, k_exp=0.0, cache_service=None):
        if name is None:
            name = self.__class__.__name__
        super().__init__(name=name, active=active, weight=weight)
        self.dict_filter = dict_filter or {}
        self.threshold_buy = threshold_buy
        self.threshold_sell = threshold_sell
        self.cutloss = cutloss
        self.lookback = lookback
        self.exp = k_exp
        self.cache_service = cache_service

    def _score_single_ticker(self, ticker) -> pd.DataFrame:
        pd_ticker = pd.read_csv(f'{FPATH}/{ticker}.csv')
        pd_ticker = pd_ticker.query("time >= '2014-01-01'")
        evaluator = WeightEval(
            stock=ticker,
            data_frame=pd_ticker.copy(),
            dict_filter=self.dict_filter,
            weight={},
            threshold_buy=self.threshold_buy,
            threshold_sell=self.threshold_sell,
            cutloss=self.cutloss,
            lookback=self.lookback,
            k_exp=self.exp,
            cache_service=self.cache_service
        )
        df = evaluator.get_df()
        # return df[['time', 'ticker', 'score']].rename(columns={'time': 'time'})
        return df

    def _safe_score_wrapper(self, ticker):
        try:
            return self._score_single_ticker(ticker)
        except Exception as e:
            print(f"[ERROR] Ticker {ticker} failed: {e}")
            print(traceback.format_exc())
            return None

    def calc_score(self) -> pd.DataFrame:
        list_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
        print(len(list_ticker))
        with Pool(20) as p:
            results = p.map(self._safe_score_wrapper, list_ticker)

        # Lọc bỏ None hoặc df rỗng
        results = [r for r in results if r is not None and not r.empty]

        if not results:
            print("Không có kết quả hợp lệ.")
            return pd.DataFrame()

        return pd.concat(results, ignore_index=True)


# 3. Score aggregator
class ScoreManager:
    """
    Manages all score providers: load, compute, aggregate, save to DB.
    """

    def __init__(self, db_mgr, providers: list):
        self.db_mgr = db_mgr
        self.collection = 'ScoreManager'
        self.meta_collection = 'ScoreMeta'
        self.providers = [p for p in providers if p.active]

    def _get_score(self, ymd=None, ticker=None, provider_name=None, limit=0):
        """
        Query data by date, ticker, and/or selected features.

        Args:
            ymd (str or list, optional): Date(s) to filter
            ticker (str or list, optional): Ticker(s) to filter
            features (list, optional): List of feature/score fields to include

        Returns:
            pd.DataFrame: Resulting data
        """
        query = {}
        if ymd:
            if isinstance(ymd, (list, set)):
                query["ymd"] = {"$in": list(ymd)}
            else:
                query["ymd"] = ymd
        if ticker:
            if isinstance(ticker, (list, set)):
                query["ticker"] = {"$in": list(ticker)}
            else:
                query["ticker"] = ticker
        # proj = None
        # if features:
        #     proj = {f: 1 for f in features}
        #     proj["ymd"] = 1
        #     proj["ticker"] = 1
        # cursor = self.db_mgr.find(query, proj)
        cursor = self.db_mgr.find_documents(self.collection, query, limit=limit)
        cursor = pd.DataFrame(list(cursor))
        if provider_name:
            cursor = cursor[['time', 'ticker', provider_name]]
        return cursor

    def _compute_and_save_score(self, provider: ScoreProvider):
        if provider not in self.db_mgr.find_documents(self.meta_collection, {"name": provider.name}):
            score_df = provider.calc_score()
            for _, row in score_df.iterrows():
                features = row.drop(['time', 'ticker']).to_dict()
                query = {"time": row['time'], "ticker": row['ticker']}
                self.db_mgr.insert_or_update(self.collection, query, features)

            self.db_mgr.insert_one_document(self.meta_collection, {"name": provider.name, "active": True})

        return True

    def _compute_and_save_all_scores(self):
        for provider in self.providers:
            self._compute_and_save_score(provider)

    # def load_score_or_calc_and_save(self, data, provider: ScoreProvider):
    #     # Lấy score từ db, nếu chưa có thì tự động tính toán rồi lưu vào db
    #     ymids = data['time'].unique()
    #     tickers = data['ticker'].unique()
    #     score_df = self.db_mgr.get_score(provider.name, ymids, tickers)
    #     if score_df is not None and not score_df.empty:
    #         # Có đủ rows không?
    #         # Merge để tránh thiếu, bổ sung nếu thiếu
    #         merged = data[['time', 'ticker']].merge(score_df, on=['time', 'ticker'], how='left')
    #         missing_mask = merged[provider.name].isna()
    #         if missing_mask.sum() > 0:
    #             missing_data = merged.loc[missing_mask, ['time', 'ticker']]
    #             to_calc = data.merge(missing_data, on=['time', 'ticker'])
    #             calc_score = provider.calc_score(to_calc)
    #             self._save_score(calc_score, provider)
    #             merged.update(calc_score)
    #         return merged
    #     else:
    #         score_df = provider.calc_score(data)
    #         self._save_score(score_df, provider)
    #         merged = data[['time', 'ticker']].merge(score_df, on=['time', 'ticker'], how='left')
    #         return merged

    def load_scores(self, data, score_names):
        result = data.copy()
        for name in score_names:
            score_df = self.db_mgr.get_score(name, data['time'].unique(), data['ticker'].unique())
            if score_df is not None and not score_df.empty:
                result = result.merge(score_df, on=['time', 'ticker'], how='left')
            else:
                result[name] = np.nan
        return result

    def aggregate_scores(self, data: pd.DataFrame, score_names, method='weighted_sum', weights=None,
                         new_col='score_agg'):
        """
        Aggregate scores from specified columns, support different rules.
        """
        if method == 'weighted_sum':
            weights = np.ones(len(score_names)) if weights is None else np.array(weights)
            scores = data[score_names].values * weights
            data[new_col] = scores.sum(axis=1) / (weights.sum() + 1e-9)
        elif method == 'average':
            data[new_col] = data[score_names].mean(axis=1)
        elif method == 'max':
            data[new_col] = data[score_names].max(axis=1)
        elif method == 'min':
            data[new_col] = data[score_names].min(axis=1)
        # Add more custom methods here
        return data


if __name__ == "__main__":
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/core_utils", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)

    FPATH = 'ticker_v1a'
    # ticker = "HPG"
    # pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
    Init = "(Volume_1M_P50*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01') "

    d_filters = {
        "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
        "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TL3M": "BearDvg2, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M",
        "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
        "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
        "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
        "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
        "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
        "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
        "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
        "Init": "(Volume_1M_P50*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
        "_BKMA200": f"{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
        "_TrendingGrowth": f"{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
        "_TL3M": f"{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
        "_BuySupport": f"{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
        "_RSILow30": f"{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
        "_UnderBV": f"{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
        "_SuperGrowth": f"{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
        "_SurpriseEarning": f"{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
        "_Conservative": "(Volume_1M_P50*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
        "_BullDvg": f"{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
        "_VolMax1Y": f"{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
        "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
        "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
        "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
        "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
        "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
        "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
        "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
        "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
        "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
        "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
        "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
        "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
        "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)",
        "~BearDvgVNI1~": "(time>='2014-01-01') & (time<='2026-01-01') & (VNINDEX_RSI_Max1W/VNINDEX_RSI > 1.044)  & (VNINDEX_RSI_Max3M > 0.74) & (VNINDEX_RSI_Max1W < 0.72) & (VNINDEX_RSI_Max1W>0.61) & (VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close > 1.028) & (VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD>1.11) & (VNINDEX_MACDdiff < 0)  & ( Close/VNINDEX_RSI_Max3M_Close > 0.96) & (VNINDEX_RSI_MinT3 > 0.43) & (VNINDEX_CMF < 0.13)",
        "~BearDvgVNI2~": "(time>='2014-01-01') & (time<='2026-01-01') & (VNINDEX_RSI_Max1W/VNINDEX_RSI > 1.016)  & (VNINDEX_RSI_Max3M > 0.77) & (VNINDEX_RSI_Max1W < 0.79) & (VNINDEX_RSI_Max1W>0.6) & (VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close > 1.008) & (VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD>1.1) & (VNINDEX_MACDdiff < 0)  & ( Close/VNINDEX_RSI_Max3M_Close > 0.97) & (VNINDEX_RSI_MinT3 > 0.5) & (VNINDEX_CMF < 0.15)"
    }

    # eval_ticker = WeightEval(ticker, pdxx, d_filters, cutloss=0.15, weight={},
    #                          lookback=20, k_exp=0.1, threshold_buy=1,
    #                          threshold_sell=-1, cache_service=redis_cache)
    # res_1 = eval_ticker.get_df()
    res = WeightScoreEval(dict_filter=d_filters, threshold_buy=1, threshold_sell=-1, cutloss=0.15, lookback=15,
                          k_exp=0.1, cache_service=redis_cache).calc_score()
    res.to_csv('test_weight_score.csv', index=False)
    pass

    # # Assume db_mgr and meta_mgr are defined from previous code.
    # # Load base data from DB (can filter by time/ticker)
    # raw_data = db_mgr.get_data()  # Full data or filtered dataEm đang
    #
    # # 1. Define providers
    # providers = [
    #     DLScoreProvider(),
    #     MLScoreProvider(),
    #     # Add other providers here...
    # ]
    #
    # # 2. Init score manager
    # score_mgr = ScoreManager(db_mgr=db_mgr, meta_mgr=meta_mgr, providers=providers)
    #
    # # 3. Compute and save all scores
    # score_mgr.compute_and_save_all_scores(raw_data)
    #
    # # 4. Load updated data from DB (with all scores)
    # scored_data = db_mgr.get_data()
    #
    # # 5. Aggregate scores (weighted sum, avg, max, ...)
    # scored_data = score_mgr.aggregate_scores(
    #     scored_data,
    #     score_names=["score_dl", "score_ml"],   # List score fields to aggregate
    #     method='weighted_sum',
    #     weights=[2.0, 1.0],                    # Optional: weights for each score
    #     new_col='score_ensemble'
    # )
    #
    # # 6. Save aggregated score to DB
    # db_mgr.batch_update(scored_data[['time', 'ticker', 'score_ensemble']])
