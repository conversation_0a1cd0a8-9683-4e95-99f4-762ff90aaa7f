import pandas as pd
import json
import re
from functools import partial

def format_html(list_elements, formatting_dict=None):
    """
    Convert a list of elements to HTML format.
    
    Args:
        list_elements (list): List of elements to format (DataFrames, dicts, or other objects)
        formatting_dict (dict): Dictionary mapping column names to format strings for DataFrames
                              e.g., {"Price": ",.2f", "Qty": "02d"}
    
    Returns:
        str: Complete HTML document
    """
    if formatting_dict is None:
        formatting_dict = {}
    
    html_blocks = []
    
    for element in list_elements:
        if isinstance(element, pd.DataFrame):
            html_blocks.append(_format_dataframe(element, formatting_dict))
        elif isinstance(element, dict):
            html_blocks.append(_format_dict(element))
        else:
            html_blocks.append(f"<p>{str(element)}</p>")
    
    body = "\n".join(html_blocks)
    
    return f"""
    <html>
    <head>
    <meta charset="UTF-8">
    <style>
        body {{
            font-family: Arial, sans-serif;
            padding: 20px;
        }}
        table.styled-table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 15px;
        }}
        table.styled-table th {{
            background-color: #48e0c1;
            color: black;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #ccc;
            padding: 6px;
            text-align: center;
        }}
        table.styled-table td {{
            border: 1px solid #ccc;
            padding: 6px;
            text-align: center;
            font-size: 13px;
        }}
        .note {{
            font-size: 12px;
            color: #777;
            font-style: italic;
            margin-top: 8px;
            margin-bottom: 20px;
        }}
    </style>
    </head>
    <body>
        {body}
    </body>
    </html>
    """.strip()

def _format_dataframe(df, formatting_dict):
    """Format a pandas DataFrame as HTML table with optional column formatting."""
    
    # Create formatters based on formatting_dict
    formatters = {}
    for col, fmt in formatting_dict.items():
        if col in df.columns:
            def make_formatter(format_str):
                def formatter(val):
                    try:
                        cleaned = str(val).replace(",", "").strip()
                        num = float(cleaned) if '.' in cleaned or 'e' in cleaned.lower() else int(cleaned)
                        formatted = format(num, format_str)
                        return f"<span style='font-weight:bold; font-size:15px;'>{formatted}</span>"
                    except:
                        return str(val)
                return formatter
            formatters[col] = make_formatter(fmt)
    
    # Auto-format numeric columns if no specific formatting provided
    if not formatters:
        formatters = _auto_format_numeric(df)
    
    table_html = df.to_html(
        escape=False,
        index=False,
        border=1,
        classes="styled-table",
        formatters=formatters
    )
    
    # Add horizontal scroll for wide tables
    if df.shape[1] > 8:
        table_html = f"<div style='overflow-x:auto; padding-bottom: 4px;'>{table_html}</div>"
    
    return table_html

def _format_dict(data):
    """Format a dictionary as pretty-printed JSON with number formatting."""
    
    def format_numbers(obj):
        """Recursively format numbers in the dictionary."""
        if isinstance(obj, dict):
            return {k: format_numbers(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [format_numbers(item) for item in obj]
        elif isinstance(obj, (int, float)):
            if isinstance(obj, int):
                return f"{obj:,}"
            else:
                return f"{obj:,.2f}"
        else:
            return obj
    
    formatted_data = format_numbers(data)
    pretty_json = json.dumps(formatted_data, indent=2, ensure_ascii=False)
    return f"<pre style='background:#f5f5f5; padding:10px; font-size:13px;'>{pretty_json}</pre>"

def _auto_format_numeric(df):
    """Automatically detect numeric columns and apply formatting."""
    
    def formatter(val, fmt=",.2f"):
        try:
            val_str = str(val).strip()
            
            if isinstance(val, (int, float)) and pd.notnull(val):
                return f"<span style='font-size:15px; color:#222;'>{val:{fmt}}</span>"
            
            match = re.match(r"^([\d,\.eE+-]+)\s*(\w+)?$", val_str)
            if match:
                raw_num, unit = match.groups()
                num = float(raw_num.replace(",", ""))
                formatted = f"{num:{fmt}}"
                return f"<span style='font-size:15px; color:#222;'>{formatted}{' ' + unit if unit else ''}</span>"
            
            return str(val)
        except:
            return str(val)
    
    return {
        col: partial(formatter, fmt=",.2f")
        for col in df.columns
    }

if __name__ == "__main__":
    # Example usage
    
    # Create sample data
    df1 = pd.DataFrame([
        {"Ticker": "FPT", "Price": 1234567.891, "Qty": 7, "Score": 4.7},
        {"Ticker": "VIC", "Price": 920000, "Qty": 23, "Score": 3.2},
        {"Ticker": "VHM", "Price": None, "Qty": 3, "Score": 2}
    ])
    
    df2 = pd.DataFrame([
        {"Ticker": "MSN", "Price": 90000, "Volume": 1500000},
        {"Ticker": "HPG", "Price": 27000, "Volume": 2500000}
    ])
    
    config_dict = {
        "engine": "SmartStock AI",
        "analyst": "HHải",
        "version": "v2.0.1",
        "threshold": 0.8,
        "tags": ["strategy", "auto", "quant"]
    }
    
    trade_data = {
        "symbols": ["FPT", "VIC", "VHM"],
        "config": {"mode": "aggressive", "threshold": 0.8},
        "prices": {"FPT": 1234567.89, "VIC": 920000, "VHM": 87000}
    }
    
    # List of elements to format
    elements = [
        "📊 Báo cáo tổng hợp dữ liệu",
        "Dữ liệu được thu thập và xử lý tự động từ hệ thống SmartStock",
        df1,
        config_dict,
        trade_data,
        df2
    ]
    
    # Formatting dictionary for DataFrame columns
    formatting = {
        "Price": ",.2f",
        "Qty": "02d",
        "Score": ".1f",
        "Volume": ",.0f"
    }
    
    # Generate HTML
    html = format_html(elements, formatting)
    
    # Save to file
    with open("test.html", "w", encoding="utf-8") as f:
        f.write(html)
    
    print("HTML file generated successfully!")


