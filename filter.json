{"INC": "(VN30_T1W<0.95) & (T1W>1) & (Close*Volume>1000e+6) & (time>='2017-01-01') & (time<='2025-01-01')", "Init": "(Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')", "InitT3": "(((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))", "MA3": "(PE<15)  &  (Close/MA200<1)  &  (Close_T1/MA200_T1>1)", "PKDM": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.5)  & (M_CMB_Step>0)  &  (M_CMB_LEN>=2)  &  (M_CMB_LAG==1)  & (ROE5Y>0.1) & (PE<30) & (W_CMB<0) & (Close > MA10)", "PKDW8": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.5) & (W_CMB_Step>0)  &  (W_CMB_LEN>=2)  &  (W_CMB_LAG==1) & (ROE5Y>=0.1)&(PE<30) & (W_CMB<0)& (Close >MA10) & (PB <0.6)", "S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)", "Sell1": "(C_L3M>=1.2)&(Close > 3*MA200) &  (D_CMB_Step<0) & (D_CMB_LEN>=5) & (D_CMB_LAG>0)& (D_CMB_LAG<=3) ", "Sell2": "(C_L3M>=1.2)&(Close_T1W  > 3*MA200)&(Close < STrend_S)", "Sell3": "(C_L3M>=1.2)&(Close > 3*MA200)&((Close < vol3M_Low) | (Close < VAP1M))", "Sell4": "(C_L3M>=1.2)&(Close < 3*MA200) & (Close >= 2*MA200)& (W_CMB_Step<0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3) ", "T2P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7)  & (C_H1M<0.9)  &  (W_CMB_Step>0)  &  (W_CMB_LEN>=2)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=3)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=2)  &  (M_CMB_LAG >=1)  &  (M_CMB_LAG <=4) & (ROE5Y>=0.1)&(PE<30) & (PB <0.6)", "T2P5X": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (C_H2Y<0.7) & (C_H3M<0.8)  & (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=4)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >0)  &  (M_CMB_LAG <=4)", "T3P4": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R>0)&(PE<10)&(C_H2Y<0.7) & (C_H2Y>0.5)", "T3P5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_R<0)&(PE<5)&(C_H2Y<0.6)&(C_H2Y>0.3)", "T3P6": "((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)", "T3P6V1": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)", "TL3M": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & (HI_3M_T1/LO_3M_T1<1.3) & (Volume > Volume_3M_P90)& (ROE5Y>0.1) & (PE<20) & (PB < 1.5) & (FSCORE > 4) & (NP_P0 > 1.1*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)", "TL3Mv1": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01'))  & (HI_3M_T1/LO_3M_T1<1.34) & (Volume > 1.18*Volume_3M_P90)& (ROE5Y>0.13) & (PE<20.5) & (PB < 1.21) & (FSCORE > 4) & (NP_P0 > 1.36*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)", "VolMax": "(Close < Volume_Max5Y_Low) & (ROE5Y>0.1)&(PE<20)& (NP_P0 > 1.2*NP_P4) & (PE >0) & (PCF>0)& (Price*Volume>1000e+6) ", "_BKMA200": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>293) & (MA50/MA200>0.86) & (MA10/MA200<1.37) & (ROE5Y >0.09) & (PE <20) & (NP_P0 > 1.21*NP_P1) & (ID_Current - ID_C_XMA200 <60)", "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)", "~Sell5": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01'))&(C_L2Y>2)&(M_CMB_Step<0) & (M_CMB_LEN>=1) & (M_CMB_LAG>0)& (M_CMB_LAG<=1) & (M_CMB_LEN<=8)&(Close < VAP1M) ", "~SellTest": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01'))&(C_L2Y>2)&(Close >2*BVPS)&(NP_R < -0.5)&(Close < VAP1M)", "BKMA200": "((Volume*Price>1e+9) & (time>='2017-01-01') & (time<='2025-01-01')) & ((ID_LO_3Y-ID_HI_3Y)>293) & (MA50/MA200>0.86) & (MA10/MA200<1.37) & (ROE5Y >0.09) & (PE <20) & (NP_P0 > 1.21*NP_P1) & (ID_Current - ID_C_XMA200 <60)"}