INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (hyperopt.mongoexp): Error while unpickling.
INFO (hyperopt.mongoexp): job exception: No module named 'hyo_tuning_manager'
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76d527c7a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f576dc4e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c7f82d8e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72e13105e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70d55de79ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x799f49cca020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c820fb4e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f40f2502020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72389811e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x759b37d41ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f4c7a051ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bf14d489ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76e7f2d42020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x797ff9002020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75039df12020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70b01f53a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72e1cadedff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75e849c5e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a2d2c14a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ee625d0e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x704e599f2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x775f8a772020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72cd354fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d433a002020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x732d18716020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ac9b67e2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x759622345ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a3c8e0fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ef138f6e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7af5391fe020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x724068e5e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73e15d232020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a9a6b432020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750fc6a7dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70f7cc58e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75b047b52020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fb7fca32020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fc516b42020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71ba5f236020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ac84ed7a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cbdb5886020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e6461ef2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7eaefa7f2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71dd3737a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fd0d001e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74fef441e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ef607c89ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73580890a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72d9a85fdff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750175826020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x727f65e7e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a3c2ea5e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79cf39002020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x749e54c12020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71dc69d81ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f8305382020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73a8bcd8dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b67e2fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cad8b372020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77461f779ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d40b1e76020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7425f9e66020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7db26d982020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fa2ee75e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fae6bf0e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75fe8a48a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f5593b75ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d85c6e72020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71dcba57a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7926aff1a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x702581a8e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x786ac7dfe020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71eafd62a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b6eb6b52020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7097a977a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c69aa7e6020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7941f2882020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79fd1d25a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dafb235a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76398a58e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76006424dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x782dd25f6020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d8783446020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7af9ac022020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e82a1342020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7433d438a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74c3262fdff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78b2e7582020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72617f9fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fb05c43a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7da02655a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7918aaefe020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77f97d9f2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a854bf85ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78c126f7e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79103fe0a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x775ff0a4a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x741986ee6020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79c162776020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78013fc82020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x739354f16020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74393fb82020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7df4e3116020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x795078b8e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79d7d277a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b1d7dd7e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b664a155ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72054657a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7af50338a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cbb83b7e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d9c3edf2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x719d2ad76020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70e016a1e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7918daa8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x764182076020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x780aef87e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x711388a7a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c580781e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x775355b92020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7707ebf8e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x788a0e08e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c32ab766020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dd57737a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7be3fad8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79f56b68a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77e860026020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79a729705ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f194d886020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x732517c66020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x795efca1e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7de02b1e6020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cde97776020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73e7cf34a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 15, shutting down.
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7765a8816020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d29cc7e2020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x712fd648e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d7d3d91a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fcacfa76020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71be84d0a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e045d819ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x78cb5bf8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7cf1b1786020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x748e9e3fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e0b5ff66020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79b02948dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x76eb23586020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x730c11d92020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7bb312116020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74e848a6dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72a214822020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x734c261f1ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7404e9d82020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a85b6d46020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ecf31086020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7077a5286020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x75ba22a86020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d813b826020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d141277a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e9f83c86020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ca8199fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7fd624792020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ee642e72020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750ce4c7a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x750c351fa020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c5b11a0a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x79488e282020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e9c94a2a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7228b068e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73ad42b8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7c14e3482020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x709b3a986020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x728c3ad82020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7894d608a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7531d2ee1ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7ff497d92020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x773555c82020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7adaf1686020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7199cb07e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7a0012302020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f0e15d16020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73e1b211dff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x74ae93c52020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x70b9b4df6020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x755a9c292020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7f804402a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x799929b8e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x795ecbe8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x737fffc8a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x768db407e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x71523d662020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x73abbba7a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x732979be1ff0>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7dd0a847a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b500053e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x72090302a020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7e2e01676020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x735334a3e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d627c27e020>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7b1c31827e50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x77c5a5f17e50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (numexpr.utils): NumExpr defaulting to 8 threads.
INFO (CACHE): <core_utils.redis_cache.EvalRedis object at 0x7d962719fe50>
INFO (CACHE): Redis cache configured with max_memory: 3000mb and eviction_policy: allkeys-lru
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): job exception: 2
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
INFO (hyperopt.mongoexp): Caught signal 2, shutting down.
