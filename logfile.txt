Commits on Aug 25, 2025
refactor build_report.py to improve data handling and error management


refactor filtering logic in build_report.py for improved performance and accuracy


Commits on Aug 20, 2025
add overheating alerts for PE and Buffett Indicator to index report


Commits on Aug 14, 2025
simulation_v2 not buy if sell in same day


add overheating alert to index report and improve filtering logic


add overheating alert to index report and improve filtering logic


Commits on Aug 13, 2025
hotfix Price


add script bigquery.py


add script bigquery.py


Commits on Aug 12, 2025
add index report to email html


Commits on Aug 11, 2025
refactor func name



update max(['Risk_Rating','D_Risk_Rating'])



fix Price and add timeout to celery task


Commits on Aug 9, 2025
refactor portfolio management: enhance investment tracking and update sell/buy logic



Commits on Aug 8, 2025
init simualation_v2


add overheating block option to buy signals and update related parameters


Commits on Aug 7, 2025
hot fix webui


hot fix webui


add traceback error to webui


add timeout and handle retry call request


update latest vnindex f0r plot service


Add df_market parameter and implement blocking logic for buy signals


Add df_market parameter and implement blocking logic for buy signals


Commits on Aug 6, 2025
fix render default value in webui


Add force_sell Backtest


Commits on Aug 4, 2025
fix error when query sell, buy pattern


fix error when query sell, buy pattern


temp fix find_score_base_on_weight

add mongodb client

feat: update data handling methods to use model_dump for consistency and add quarter filtering in risk retrieval

feat: update data handling methods to use model_dump for consistency and add quarter filtering in risk retrieval

Commits on Aug 3, 2025
feat: add risk rating data initialization and refactor related functions

feat: implement risk rating endpoints and refactor recommendation service

fix: add risk metrics descriptions and update variable names for clarity
