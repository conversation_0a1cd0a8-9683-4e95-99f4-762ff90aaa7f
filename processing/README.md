# Stock Evaluator Service

Service đánh giá cổ phiếu sử dụng FastAPI và Celery để xử lý song song các file CSV.

## Cấu trúc thư mục

```
processing/
├── api/                    # FastAPI app
├── celery_app/            # Celery tasks
├── core/                  # Core logic
├── shared/               # Shared utilities
└── ui/                   # Streamlit UI
```

## Cài đặt và Chạy với Docker

1. C<PERSON>u hình NFS:
- Cập nhật thông tin NFS server trong `docker-compose.yml`:
```yaml
volumes:
  nfs_mount:
    driver: local
    driver_opts:
      type: nfs
      o: addr=nfs_server,rw  # Thay nfs_server bằng địa chỉ NFS server
      device: ":/path/to/nfs/share"  # Thay đổi path tới NFS share
```

2. Build và chạy containers:
```bash
docker-compose up --build
```

3. Truy cập các service:
- Streamlit UI: http://localhost:8501
- FastAPI docs: http://localhost:8000/docs
- Redis: localhost:6379

## Cấu trúc Docker

Service được chia thành 4 containers:

1. **Redis**:
- Message broker cho Celery
- Lưu trữ kết quả task

2. **API** (FastAPI):
- REST API endpoints
- WebSocket server
- Port: 8000

3. **Worker** (Celery):
- Xử lý các task đánh giá
- Scale tự động (2 replicas)
- Kết nối với Redis và NFS

4. **UI** (Streamlit):
- Giao diện người dùng
- Port: 8501
- Kết nối với API qua WebSocket

## Volume Mounts

- `.:/app`: Mount code vào containers
- `nfs_mount:/mnt/shared`: Mount NFS share cho lưu trữ dữ liệu
- `redis_data:/data`: Persist Redis data

## Networks

- `app_network`: Bridge network cho internal communication
- Các service có thể giao tiếp qua tên service (e.g. `redis://redis:6379`)

## Scaling

- Worker có thể scale bằng cách tăng số replicas:
```bash
docker-compose up --scale worker=4
```

## Monitoring

- Redis data được persist trong volume
- Logs có thể xem qua:
```bash
docker-compose logs -f [service_name]
```

## Troubleshooting

1. NFS mount issues:
```bash
# Kiểm tra NFS server
showmount -e nfs_server

# Kiểm tra mount trong container
docker-compose exec api ls -l /mnt/shared
```

2. Redis connection:
```bash
# Kiểm tra Redis
docker-compose exec redis redis-cli ping
```

3. Worker scaling:
```bash
# Kiểm tra worker status
docker-compose exec api celery -A processing.celery_app.celery_app status
```

## Cài đặt

1. Tạo môi trường ảo:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
.\venv\Scripts\activate  # Windows
```

2. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

3. Cấu hình môi trường:
- Tạo file `.env` với các biến môi trường:
```
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
NFS_MOUNT_PATH=/path/to/nfs/mount
```

## Chạy service

1. Khởi động Redis:
```bash
redis-server
```

2. Khởi động Celery worker:
```bash
celery -A processing.celery_app.celery_app worker --loglevel=info
```

3. Khởi động FastAPI server:
```bash
uvicorn processing.api.main:app --reload
```

4. Khởi động Streamlit UI:
```bash
streamlit run processing/ui/app.py
```

## API Endpoints

- POST `/jobs/start`: Bắt đầu job đánh giá
- GET `/jobs/{job_id}/status`: Lấy trạng thái job
- GET `/jobs/{job_id}/results`: Lấy kết quả job
- WebSocket `/ws/{job_id}`: Theo dõi tiến trình job

## Sử dụng

1. Mở Streamlit UI tại http://localhost:8501
2. Upload các file CSV cần đánh giá
3. Chọn loại đánh giá và tham số
4. Nhấn "Start Evaluation" để bắt đầu
5. Theo dõi tiến trình và tải kết quả khi hoàn thành 