from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
from processing.celery_app.tasks import start_job
from processing.shared.storage import NFSStorage
from processing.api.websockets import WebSocketManager
from celery.result import AsyncResult
from processing.celery_app.celery_app import celery_app
from processing.core.cache import redis_cache, joblib_cache
from processing.core.config import settings

router = APIRouter(prefix="/jobs", tags=["jobs"])
storage = NFSStorage()
ws_managerws_manager = WebSocketManager()

@router.get("/health")
async def health_check():
    """Kiểm tra trạng thái hoạt động của service"""
    return {"status": "healthy"}

@router.post("/start")
@redis_cache(expire=settings.REDIS_CACHE_EXPIRE)
@joblib_cache(expire=settings.JOBLIB_CACHE_EXPIRE)
# async def start_evaluation(files: List[str], eval_type: str = "all", params: Dict[str, Any] = None):
async def start_evaluation(dict_filter: dict, eval_type: str = "all", params: Dict[str, Any] = None):
    """Start a new evaluation job with caching"""
    try:
        # Start job
        job_id = start_job.delay(dict_filter, eval_type, params)
        
        # Initialize job status
        storage.update_job_status(job_id, {
            "status": "PENDING",
            # "total_files": len(files),
            "processed_files": 0,
            "eval_type": eval_type,
            "params": params
        })
        
        return {"job_id": job_id, "status": "started"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{job_id}/status")
@redis_cache(expire=settings.REDIS_CACHE_EXPIRE)
@joblib_cache(expire=settings.JOBLIB_CACHE_EXPIRE)
async def get_job_status(job_id: str):
    """Get job status with caching"""
    try:
        status = storage.get_job_status(job_id)
        if not status:
            raise HTTPException(status_code=404, detail="Job not found")
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{job_id}/results")
@redis_cache(expire=settings.REDIS_CACHE_EXPIRE)
@joblib_cache(expire=settings.JOBLIB_CACHE_EXPIRE)
async def get_job_results(job_id: str):
    """Get job results with caching"""
    try:
        status = storage.get_job_status(job_id)
        if not status:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if status["status"] != "COMPLETED":
            raise HTTPException(status_code=400, detail="Job not completed")
            
        results_path = storage._get_job_path(job_id) / "results.parquet"
        if not results_path.exists():
            raise HTTPException(status_code=404, detail="Results not found")
            
        return {"results_path": str(results_path)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{job_id}/cancel")
async def cancel_job(job_id: str):
    """Cancel a running job"""
    try:
        # Get job status
        status = storage.get_job_status(job_id)
        if not status:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if status["status"] in ["COMPLETED", "FAILED", "CANCELLED"]:
            raise HTTPException(status_code=400, detail=f"Job already {status['status']}")
            
        # Revoke all tasks in the job
        result = AsyncResult(job_id, app=celery_app)
        if result.parent:
            # If it's a chord, revoke the chord header
            celery_app.control.revoke(result.parent.id, terminate=True)
            
        # Update job status
        storage.update_job_status(job_id, {
            **status,
            "status": "CANCELLED"
        })
        
        # Notify clients
        await ws_manager.broadcast(job_id, {
            "status": "CANCELLED",
            "message": "Job cancelled by user"
        })
        
        return {"status": "cancelled"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))