import asyncio
import json

from celery.result import AsyncR<PERSON>ult
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware

from processing.api.routes import jobs
from processing.api.websockets import WebSocketManager
from processing.celery_app.celery_app import celery_app

app = FastAPI(title="Stock Evaluator API")

# CORS middleware
app.add_middleware(  # type: ignore
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(jobs.router)

# WebSocket manager
ws_manager = WebSocketManager()

active_tasks = {}


@app.websocket("/ws/{job_id}")
async def websocket_endpoint(websocket: WebSocket, job_id: str):
    await ws_manager.connect(websocket, job_id)
    try:
        while True:
            # Keep connection alive
            await asyncio.sleep(1)
    except Exception as e:
        await ws_manager.disconnect(websocket, job_id)


@app.websocket("/ws/status/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """
    WebSocket endpoint để theo dõi trạng thái task realtime
    """
    await websocket.accept()
    session_id = id(websocket)
    active_tasks[session_id] = task_id  # Lưu task_id
    try:
        while True:
            task = AsyncResult(task_id, app=celery_app)
            # progress = redis_client.get(f"progress:{task_id}")
            data = await websocket.receive_text()
            request = json.loads(data)

            status_data = {
                "task_id": task_id,
                "status": task.status,
                # "progress": progress
            }
            if task.status == "SUCCESS":
                status_data["result"] = task.get()
                await websocket.send_json(status_data)
                break

            if request.get("action") == "cancel":
                celery.control.revoke(active_tasks[session_id], terminate=True)
                del active_tasks[session_id]
                break

            await websocket.send_json(status_data)
            await asyncio.sleep(1)

    # except Exception as e:
    except WebSocketDisconnect:
        await websocket.close()
        if session_id in active_tasks:
            celery.control.revoke(active_tasks[session_id], terminate=True)  # Hủy task khi mất kết nối
            del active_tasks[session_id]
        print("Client đã ngắt kết nối")
    except Exception as e:
        print(f"Lỗi khác: {e}")
