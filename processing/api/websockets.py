from fastapi import WebSocket
from typing import Dict, List, Set
import json
import asyncio
from processing.shared.storage import NFSStorage
from processing.core.config import settings

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.storage = NFSStorage()
        self.progress_tasks: Dict[str, asyncio.Task] = {}

    async def connect(self, websocket: WebSocket, job_id: str):
        await websocket.accept()
        if job_id not in self.active_connections:
            self.active_connections[job_id] = []
            # Start progress monitoring task
            self.progress_tasks[job_id] = asyncio.create_task(
                self.monitor_progress(job_id)
            )
        self.active_connections[job_id].append(websocket)

    async def disconnect(self, websocket: WebSocket, job_id: str):
        if job_id in self.active_connections:
            self.active_connections[job_id].remove(websocket)
            if not self.active_connections[job_id]:
                del self.active_connections[job_id]
                # Stop progress monitoring task
                if job_id in self.progress_tasks:
                    self.progress_tasks[job_id].cancel()
                    del self.progress_tasks[job_id]

    async def broadcast(self, job_id: str, message: dict):
        if job_id in self.active_connections:
            dead_connections = []
            for connection in self.active_connections[job_id]:
                try:
                    await connection.send_json(message)
                except Exception:
                    dead_connections.append(connection)
            
            # Remove dead connections
            for dead in dead_connections:
                await self.disconnect(dead, job_id)

    async def monitor_progress(self, job_id: str):
        """Monitor job progress and broadcast updates"""
        try:
            while True:
                # Get job status
                status = self.storage.get_job_status(job_id)
                if not status:
                    break

                # Calculate progress
                if status["status"] == "PENDING":
                    progress = 0
                elif status["status"] in ["COMPLETED", "FAILED", "CANCELLED"]:
                    progress = 1
                else:
                    total = status.get("total_files", 0)
                    processed = status.get("processed_files", 0)
                    progress = processed / total if total > 0 else 0

                # Prepare message
                message = {
                    "status": status["status"],
                    "progress": progress,
                    "processed_files": status.get("processed_files", 0),
                    "total_files": status.get("total_files", 0),
                    "message": self._get_status_message(status)
                }

                # Broadcast update
                await self.broadcast(job_id, message)

                # Check if job is finished
                if status["status"] in ["COMPLETED", "FAILED", "CANCELLED"]:
                    break

                # Wait before next update
                await asyncio.sleep(1)

        except asyncio.CancelledError:
            pass
        except Exception as e:
            # Broadcast error
            await self.broadcast(job_id, {
                "status": "ERROR",
                "message": str(e)
            })

    def _get_status_message(self, status: dict) -> str:
        """Get human-readable status message"""
        if status["status"] == "PENDING":
            return "Job is pending"
        elif status["status"] == "PROGRESS":
            return f"Processing {status.get('processed_files', 0)}/{status.get('total_files', 0)} files"
        elif status["status"] == "COMPLETED":
            return "Job completed successfully"
        elif status["status"] == "FAILED":
            return f"Job failed: {status.get('error', 'Unknown error')}"
        elif status["status"] == "CANCELLED":
            return "Job was cancelled"
        else:
            return "Unknown status" 