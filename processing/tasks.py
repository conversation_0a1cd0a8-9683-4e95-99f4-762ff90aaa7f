from celery import Celery
import pandas as pd
import logging
from typing import List, Dict
import os

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Khởi tạo Celery với Redis
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
celery_app = Celery('tasks', broker=REDIS_URL, backend=REDIS_URL)


@celery_app.task(bind=True, max_retries=3)
def process_data(self, data: Dict) -> Dict:
    """
    Task chính để xử lý dữ liệu
    """
    try:
        # Chia nhỏ dữ liệu thành các subtask nếu cần
        if len(data.get("items", [])) > 1000:
            chunks = [data["items"][i:i + 1000] for i in range(0, len(data["items"]), 1000)]
            subtask_results = []

            for chunk in chunks:
                result = process_subtask.delay(chunk)
                subtask_results.append(result)

            # Đ<PERSON><PERSON> và gộp kết quả từ các subtask
            results = [task.get() for task in subtask_results]
            return combine_results(results)

        # X<PERSON> lý trực tiếp nếu dữ liệu nhỏ
        return process_subtask(data["items"])

    except Exception as e:
        logger.error(f"Error in process_data: {str(e)}")
        self.retry(countdown=5, exc=e)


@celery_app.task
def process_subtask(items: List) -> Dict:
    """
    Xử lý một phần nhỏ của dữ liệu
    """
    try:
        # Xử lý dữ liệu ở đây
        df = pd.DataFrame(items)
        # Thêm logic xử lý dữ liệu tùy theo yêu cầu

        return df.to_dict(orient='records')
    except Exception as e:
        logger.error(f"Error in process_subtask: {str(e)}")
        return []


def combine_results(results: List[Dict]) -> Dict:
    """
    Gộp kết quả từ các subtask
    """
    try:
        combined_df = pd.concat([pd.DataFrame(r) for r in results])
        return combined_df.to_dict(orient='records')
    except Exception as e:
        logger.error(f"Error in combine_results: {str(e)}")
        return []


from celery import Celery
import pandas as pd
import redis

app = Celery('tasks')
app.config_from_object('celeryconfig')

redis_client = redis.Redis(host='redis', port=6379, db=0)


@app.task(bind=True, max_retries=3)
def process_subtask(self, data_chunk):
    try:
        # Process data chunk
        df = pd.DataFrame(data_chunk)
        # Your processing logic here
        return df.to_json()
    except Exception as exc:
        self.retry(exc=exc, countdown=60)


@app.task(bind=True)
def process_data(self, data):
    # Split data into chunks
    chunks = [data[i:i + 1000] for i in range(0, len(data), 1000)]

    subtasks = []
    for chunk in chunks:
        subtask = process_subtask.delay(chunk)
        subtasks.append(subtask)

    # Monitor progress
    total_tasks = len(subtasks)
    while not all(task.ready() for task in subtasks):
        completed = sum(task.ready() for task in subtasks)
        progress = (completed / total_tasks) * 100
        redis_client.set(f"progress:{self.request.id}", progress)

    # Combine results
    results = [pd.read_json(task.result) for task in subtasks if task.successful()]
    final_df = pd.concat(results)

    # Save to parquet
    final_df.to_parquet(f"result_{self.request.id}.parquet")
    return f"result_{self.request.id}.parquet"
