version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network

  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - NFS_MOUNT_PATH=/mnt/shared
      - JOBLIB_CACHE_DIR=/tmp/joblib_cache
    volumes:
      - .:/app
      - nfs_mount:/mnt/shared
      - joblib_cache:/tmp/joblib_cache
    depends_on:
      - redis
    networks:
      - app_network

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - NFS_MOUNT_PATH=/mnt/shared
      - JOBLIB_CACHE_DIR=/tmp/joblib_cache
    volumes:
      - .:/app
      - nfs_mount:/mnt/shared
      - joblib_cache:/tmp/joblib_cache
    depends_on:
      - redis
      - api
    deploy:
      replicas: 2
    networks:
      - app_network

  ui:
    build:
      context: .
      dockerfile: Dockerfile.ui
    ports:
      - "8501:8501"
    environment:
      - API_URL=http://api:8000
      - WS_URL=ws://api:8000/ws
    volumes:
      - .:/app
      - nfs_mount:/mnt/shared
    depends_on:
      - api
    networks:
      - app_network

volumes:
  redis_data:
  nfs_mount:
    driver: local
    driver_opts:
      type: nfs
      o: addr=nfs_server,rw
      device: ":/path/to/nfs/share"
  joblib_cache:
    driver: local

networks:
  app_network:
    driver: bridge 