from fastapi import FastAP<PERSON>, WebSocket, HTTPException, WebSocketDisconnect
from fastapi.responses import JSONResponse
from celery.result import AsyncResult
from tasks import process_data, celery_app
from celery import Celery
import json
import redis
import asyncio

app = FastAPI(title="Processing Service")
redis_client = redis.Redis(host='redis', port=6379, db=0)
celery = Celery("tasks", broker="redis://localhost:6379/0", backend="redis://localhost:6379/0")
active_tasks = {}

@app.get("/health")
async def health_check():
    """Kiểm tra trạng thái hoạt động của service"""
    return {"status": "healthy"}

@app.post("/process")
async def create_process_task(data: dict):
    """
    Tạo task xử lý dữ liệu
    """
    try:
        # Tạo task chính
        task = process_data.delay(data)
        return {"task_id": task.id, "status": "processing"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """
    Lấy trạng thái của task theo task_id
    """
    task = AsyncResult(task_id, app=celery_app)
    response = {
        "task_id": task_id,
        "status": task.status,
        # "progress": redis_client.get(f"progress:{task_id}")
    }
    if task.status == "SUCCESS":
        response["result"] = task.get()
    return response

@app.websocket("/ws/status/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """
    WebSocket endpoint để theo dõi trạng thái task realtime
    """
    await websocket.accept()
    session_id = id(websocket)
    active_tasks[session_id] = task_id  # Lưu task_id
    try:
        while True:
            task = AsyncResult(task_id, app=celery_app)
            # progress = redis_client.get(f"progress:{task_id}")
            data = await websocket.receive_text()
            request = json.loads(data)

            status_data = {
                "task_id": task_id,
                "status": task.status,
                # "progress": progress
            }
            if task.status == "SUCCESS":
                status_data["result"] = task.get()
                await websocket.send_json(status_data)
                break

            if request.get("action") == "cancel":
                celery.control.revoke(active_tasks[session_id], terminate=True)
                del active_tasks[session_id]
                break

            await websocket.send_json(status_data)
            await asyncio.sleep(1)

    # except Exception as e:
    except WebSocketDisconnect:
        await websocket.close()
        if session_id in active_tasks:
            celery.control.revoke(active_tasks[session_id], terminate=True)  # Hủy task khi mất kết nối
            del active_tasks[session_id]

@app.get("/result/{task_id}")
async def get_task_result(task_id: str):
    """
    Lấy kết quả của task
    """
    task = AsyncResult(task_id, app=celery_app)
    if task.status == "SUCCESS":
        return JSONResponse(content=task.get())
    elif task.status == "PENDING":
        return {"status": "pending"}
    else:
        raise HTTPException(status_code=404, detail="Task not found or failed")


#
# from fastapi import FastAPI, WebSocket
# from celery.result import AsyncResult
# from tasks import process_data
# import redis
#
# app = FastAPI()
# redis_client = redis.Redis(host='redis', port=6379, db=0)
#
# @app.post("/process")
# async def start_processing(data: dict):
#     task = process_data.delay(data)
#     return {"job_id": task.id}
#
# @app.get("/status/{job_id}")
# async def get_status(job_id: str):
#     task = AsyncResult(job_id)
#     return {"status": task.status, "progress": redis_client.get(f"progress:{job_id}")}
#
# @app.websocket("/ws/status/{job_id}")
# async def websocket_status(websocket: WebSocket, job_id: str):
#     await websocket.accept()
#     while True:
#         task = AsyncResult(job_id)
#         progress = redis_client.get(f"progress:{job_id}")
#         await websocket.send_json({"status": task.status, "progress": progress})
#         if task.ready():
#             break
#
# @app.get("/result/{job_id}")
# async def get_result(job_id: str):
#     task = AsyncResult(job_id)
#     if task.ready():
#         return {"result": task.result}
#     return {"status": "Task not completed"}