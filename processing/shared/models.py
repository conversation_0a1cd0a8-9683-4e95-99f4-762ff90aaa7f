from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

class EvaluationResult(BaseModel):
    """Model for storing evaluation results in MongoDB"""
    job_id: str = Field(..., description="Unique job identifier")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="When the evaluation was run")
    eval_type: str = Field(..., description="Type of evaluation (all/weight/shortsell)")
    
    # Parameters used for evaluation
    params: Dict[str, Any] = Field(..., description="Evaluation parameters")
    
    # Filter and weight configurations
    filters: Dict[str, str] = Field(..., description="Filters used in evaluation")
    weights: Optional[Dict[str, float]] = Field(None, description="Weights used in evaluation")
    
    # Results
    summary: Optional[Dict[str, Any]] = Field(None, description="Summary statistics")
    deals: Optional[List[Dict[str, Any]]] = Field(None, description="Deal results")
    hits: Optional[List[Dict[str, Any]]] = Field(None, description="Hit results")
    
    # Metadata
    status: str = Field(default="completed", description="Job status")
    error: Optional[str] = Field(None, description="Error message if any")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        } 