from typing import Dict, List, Any, Optional
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import ASCEND<PERSON>, DESCENDING
from .models import EvaluationResult
from .config import settings

class MongoDBService:
    """Service for MongoDB operations"""
    
    def __init__(self):
        self.client = AsyncIOMotorClient(settings.MONGODB_URL)
        self.db = self.client[settings.MONGODB_DB_NAME]
        self.collection = self.db.evaluation_results
        
        # Create indexes
        self.collection.create_index([("job_id", ASCENDING)], unique=True)
        self.collection.create_index([("created_at", DESCENDING)])
        self.collection.create_index([("eval_type", ASCENDING)])
        
    async def save_evaluation(self, result: EvaluationResult) -> str:
        """Save evaluation result to MongoDB"""
        doc = result.model_dump()
        await self.collection.insert_one(doc)
        return result.job_id
        
    async def get_evaluation(self, job_id: str) -> Optional[EvaluationResult]:
        """Get evaluation result by job_id"""
        doc = await self.collection.find_one({"job_id": job_id})
        if doc:
            return EvaluationResult(**doc)
        return None
        
    async def list_evaluations(
        self,
        eval_type: Optional[str] = None,
        limit: int = 100,
        skip: int = 0,
        sort_by: str = "created_at",
        sort_order: int = -1
    ) -> List[EvaluationResult]:
        """List evaluation results with optional filtering"""
        query = {}
        if eval_type:
            query["eval_type"] = eval_type
            
        cursor = self.collection.find(query) \
            .sort(sort_by, sort_order) \
            .skip(skip) \
            .limit(limit)
            
        results = []
        async for doc in cursor:
            results.append(EvaluationResult(**doc))
        return results
        
    async def update_evaluation_status(
        self,
        job_id: str,
        status: str,
        error: Optional[str] = None
    ) -> bool:
        """Update evaluation status"""
        result = await self.collection.update_one(
            {"job_id": job_id},
            {
                "$set": {
                    "status": status,
                    "error": error,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        return result.modified_count > 0
        
    async def delete_evaluation(self, job_id: str) -> bool:
        """Delete evaluation result"""
        result = await self.collection.delete_one({"job_id": job_id})
        return result.deleted_count > 0

# Create singleton instance
mongodb_service = MongoDBService() 