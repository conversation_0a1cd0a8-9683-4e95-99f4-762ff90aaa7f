import os
import json
import pandas as pd
from typing import List, Dict, Any
from processing.core.config import settings

class NFSStorage:
    def __init__(self):
        self.base_path = settings.NFS_MOUNT_PATH
        
    def _get_job_path(self, job_id: str) -> str:
        """Get path for job directory"""
        job_path = os.path.join(self.base_path, "jobs", job_id)
        os.makedirs(job_path, exist_ok=True)
        return job_path
        
    def save_batch_results(self, job_id: str, results: List[Dict[str, Any]]) -> str:
        """Save batch results to parquet file"""
        job_path = self._get_job_path(job_id)
        batch_id = len(os.listdir(job_path))
        batch_path = os.path.join(job_path, f"batch_{batch_id}.parquet")
        
        # Convert results to DataFrame and save
        df = pd.DataFrame(results)
        df.to_parquet(batch_path)
        
        return batch_path
        
    def combine_results(self, job_id: str, batch_paths: List[str]) -> str:
        """Combine all batch results into final output"""
        job_path = self._get_job_path(job_id)
        final_path = os.path.join(job_path, "final.parquet")
        
        # Read and combine all batch results
        dfs = []
        for path in batch_paths:
            df = pd.read_parquet(path)
            dfs.append(df)
            
        # Combine and save final result
        final_df = pd.concat(dfs, ignore_index=True)
        final_df.to_parquet(final_path)
        
        # Clean up batch files
        for path in batch_paths:
            os.remove(path)
            
        return final_path
        
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status and metadata"""
        job_path = self._get_job_path(job_id)
        status_path = os.path.join(job_path, "status.json")
        
        if os.path.exists(status_path):
            with open(status_path, "r") as f:
                return json.load(f)
        return {"status": "unknown"}
        
    def update_job_status(self, job_id: str, status: Dict[str, Any]):
        """Update job status"""
        job_path = self._get_job_path(job_id)
        status_path = os.path.join(job_path, "status.json")
        
        with open(status_path, "w") as f:
            json.dump(status, f) 