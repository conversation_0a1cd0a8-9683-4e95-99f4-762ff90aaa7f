import streamlit as st
import pandas as pd
import requests
import json
import asyncio
import websockets
import os
from pathlib import Path
from typing import List, Dict, Any
import time

# API endpoints
API_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def get_job_status(job_id: str) -> Dict[str, Any]:
    response = requests.get(f"{API_URL}/jobs/{job_id}/status")
    return response.json()

def start_job(files: List[str], eval_type: str, params: Dict[str, Any]) -> str:
    response = requests.post(
        f"{API_URL}/jobs/start",
        json={"files": files, "eval_type": eval_type, "params": params}
    )
    return response.json()["job_id"]

def cancel_job(job_id: str) -> Dict[str, Any]:
    response = requests.post(f"{API_URL}/jobs/{job_id}/cancel")
    return response.json()

async def monitor_job(job_id: str):
    try:
        async with websockets.connect(f"{WS_URL}/{job_id}") as websocket:
            while True:
                try:
                    message = await websocket.recv()
                    status = json.loads(message)
                    yield status
                    
                    # Stop monitoring if job is completed or cancelled
                    if status["status"] in ["COMPLETED", "FAILED", "CANCELLED", "ERROR"]:
                        break
                except websockets.exceptions.ConnectionClosed:
                    # Try to reconnect
                    try:
                        websocket = await websockets.connect(f"{WS_URL}/{job_id}")
                    except:
                        break
    except Exception as e:
        st.error(f"Error monitoring job: {str(e)}")
        yield {"status": "ERROR", "message": str(e)}

def main():
    st.title("Stock Evaluator")
    
    # Initialize session state
    if "job_id" not in st.session_state:
        st.session_state["job_id"] = None
    if "is_running" not in st.session_state:
        st.session_state["is_running"] = False
    if "progress" not in st.session_state:
        st.session_state["progress"] = 0
    if "status_message" not in st.session_state:
        st.session_state["status_message"] = ""
    
    # File selection
    uploaded_files = st.file_uploader("Select CSV files", type="csv", accept_multiple_files=True)
    
    # Evaluation type
    eval_type = st.selectbox(
        "Evaluation Type",
        ["all", "weight", "shortsell"]
    )
    
    # Parameters
    params = {}
    if eval_type == "weight":
        params["weight"] = st.slider("Weight", 0.0, 1.0, 0.5)
    elif eval_type == "shortsell":
        params["threshold"] = st.slider("Threshold", 0.0, 1.0, 0.7)
    
    # Create columns for buttons
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Start Evaluation", disabled=st.session_state["is_running"]):
            if not uploaded_files:
                st.error("Please select at least one file")
                return
                
            # Save uploaded files
            file_paths = []
            for file in uploaded_files:
                path = Path("temp") / file.name
                path.parent.mkdir(exist_ok=True)
                with open(path, "wb") as f:
                    f.write(file.getvalue())
                file_paths.append(str(path))
                
            # Start job
            job_id = start_job(file_paths, eval_type, params)

            st.session_state["job_id"] = job_id
            st.session_state["is_running"] = True
            st.session_state["progress"] = 0
            st.session_state["status_message"] = "Starting job..."
            
            # Monitor progress
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            async def update_progress():
                async for status in monitor_job(job_id):
                    # Update progress
                    st.session_state["progress"] = status["progress"]
                    st.session_state["status_message"] = status["message"]
                    
                    # Update UI
                    progress_bar.progress(status["progress"])
                    status_text.text(status["message"])
                    
                    if status["status"] == "CANCELLED":
                        st.warning("Job cancelled")
                        st.session_state["is_running"] = False
                        break
                    elif status["status"] == "ERROR":
                        st.error(status["message"])
                        st.session_state["is_running"] = False
                        break
                    elif status["status"] == "COMPLETED":
                        st.success("Evaluation completed!")
                        results = get_job_status(job_id)
                        st.download_button(
                            "Download Results",
                            data=pd.read_parquet(results["results_path"]).to_csv(index=False),
                            file_name="results.csv",
                            mime="text/csv"
                        )
                        st.session_state["is_running"] = False
                        break
                    
            asyncio.run(update_progress())
    
    with col2:
        if st.session_state["is_running"] and st.session_state["job_id"]:
            if st.button("Cancel Job"):
                try:
                    cancel_job(st.session_state["job_id"])
                    st.session_state["is_running"] = False
                except Exception as e:
                    st.error(f"Error cancelling job: {str(e)}")
    
    # Show current progress if job is running
    if st.session_state["is_running"]:
        st.progress(st.session_state["progress"])
        st.text(st.session_state["status_message"])

if __name__ == "__main__":
    main() 