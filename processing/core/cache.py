import redis
import joblib
import hashlib
import json
from typing import Any, Optional
from functools import wraps
from processing.core.config import settings

# Redis client
redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    decode_responses=True
)

# Joblib memory cache
memory = joblib.Memory(
    location=settings.JOBLIB_CACHE_DIR,
    verbose=0
)


def cache_generate_key(key_string):
    """
    Generate a unique cache key based on ticker and filter.
    """
    # filter_str = json.dumps(filter, sort_keys=True)
    # unique_string = f"{prefix}:{filter_str}"
    cache_key = hashlib.md5(key_string.encode()).hexdigest()
    return cache_key


def generate_cache_key(func_name: str, *args, **kwargs) -> str:
    """Generate a unique cache key based on function name and arguments"""
    # Convert args and kwargs to a sorted string
    key_parts = [func_name]
    if args:
        key_parts.extend([str(arg) for arg in args])
    if kwargs:
        key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])

    # Create hash
    key_string = ":".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()


def cache_set(key: str, value: Any, expire: int = 3600):
    """Save value to cache with a specific key and expiration"""
    try:
        redis_client.setex(key, expire, json.dumps(value))
    except ConnectionError as e:
        print("Redis connection error during set_cache: %s", e)


def cache_get(key: str) -> Optional[Any]:
    """Get value from cache by key"""
    try:
        cached_value = redis_client.get(key)
        if cached_value:
            return json.loads(cached_value)

        print("Cache miss for key: %s", key)
        return None
    except ConnectionError as e:
        # logger.error("Redis connection error during get_cache: %s", e)
        return None


def cache_delete(key: str):
    """Delete a specific key from cache"""
    try:
        redis_client.delete(key)
    except ConnectionError as e:
        print("Redis connection error during delete_cache: %s", e)


def redis_cache(expire: int = 3600):
    """Redis cache decorator"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"redis:{func.__name__}:{generate_cache_key(func.__name__, *args, **kwargs)}"

            # Try to get from cache
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)

            # Execute function
            result = func(*args, **kwargs)

            # Cache result
            redis_client.setex(
                cache_key,
                expire,
                json.dumps(result)
            )

            return result

        return wrapper

    return decorator


def joblib_cache(expire: Optional[int] = None):
    """Joblib memory cache decorator"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"joblib:{func.__name__}:{generate_cache_key(func.__name__, *args, **kwargs)}"

            # Create cached function
            cached_func = memory.cache(func)

            # Execute function
            result = cached_func(*args, **kwargs)

            return result

        return wrapper

    return decorator


def clear_cache(func_name: Optional[str] = None):
    """Clear cache for a specific function or all functions"""
    if func_name:
        # Clear specific function cache
        pattern = f"*{func_name}*"
        for key in redis_client.scan_iter(pattern):
            redis_client.delete(key)
        memory.clear()
    else:
        # Clear all cache
        redis_client.flushdb()
        memory.clear()
