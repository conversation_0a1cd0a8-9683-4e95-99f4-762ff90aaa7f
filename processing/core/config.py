from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Celery settings
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # Redis settings
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    # Joblib cache settings
    JOBLIB_CACHE_DIR: str = "/tmp/joblib_cache"
    
    # NFS settings
    NFS_MOUNT_PATH: str = "/mnt/shared"
    
    # Processing settings
    BATCH_SIZE: int = 50
    MAX_WORKERS: int = 4
    
    # API settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    
    # Cache settings
    REDIS_CACHE_EXPIRE: int = 3600  # 1 hour
    JOBLIB_CACHE_EXPIRE: Optional[int] = None  # No expiration
    
    class Config:
        env_file = ".env"

settings = Settings() 