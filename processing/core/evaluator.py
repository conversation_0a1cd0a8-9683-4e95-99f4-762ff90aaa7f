import pandas as pd
from typing import Dict, Any, List
from core_utils.base_eval import AllEval, WeightEval, ShortSellEval

class StockEvaluator:
    def __init__(self, eval_type: str, params: Dict[str, Any]):
        self.eval_type = eval_type
        self.params = params
        
    def evaluate_file(self, file_path: str) -> Dict[str, Any]:
        """Evaluate a single stock file"""
        # Read file
        df = pd.read_csv(file_path)
        
        # Get ticker from filename
        ticker = file_path.split("/")[-1].replace(".csv", "")
        
        # Initialize appropriate evaluator
        if self.eval_type == "all":
            evaluator = AllEval(
                stock=ticker,
                data_frame=df,
                dict_filter=self.params["filters"],
                cutloss=self.params.get("cutloss", 0.15)
            )
            result = evaluator.get_deal()
            
        elif self.eval_type == "weight":
            evaluator = WeightEval(
                stock=ticker,
                data_frame=df,
                dict_filter=self.params["filters"],
                weight=self.params["weights"],
                threshold_buy=self.params.get("threshold_buy", 1.0),
                threshold_sell=self.params.get("threshold_sell", -1.0),
                cutloss=self.params.get("cutloss", 0.15),
                lookback=self.params.get("lookback", 5),
                k_exp=self.params.get("k_exp", 0)
            )
            result = evaluator.get_eval_weight_hit()
            
        elif self.eval_type == "shortsell":
            evaluator = ShortSellEval(
                stock=ticker,
                data_frame=df,
                dict_filter=self.params["filters"],
                cutloss=self.params.get("cutloss", 0.15)
            )
            result = evaluator.get_shortsell()
            
        else:
            raise ValueError(f"Unknown evaluation type: {self.eval_type}")
            
        # Add metadata
        result["ticker"] = ticker
        result["eval_type"] = self.eval_type
        
        return result 