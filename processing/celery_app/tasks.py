import os
from typing import List, Dict, Any, Tuple
from celery import chord, group, shared_task
from processing.celery_app.celery_app import celery_app
from processing.core.config import settings
from processing.shared.storage import NFSStorage
from processing.core.cache import redis_cache, joblib_cache
from processing.shared.mongodb import mongodb_service
from processing.shared.models import EvaluationResult
from processing.celery_app.func_task import evaluate_file
import json
import pandas as pd

# Initialize storage
storage = NFSStorage()


@celery_app.task(bind=True)
@redis_cache(expire=settings.REDIS_CACHE_EXPIRE)
@joblib_cache(expire=settings.JOBLIB_CACHE_EXPIRE)
async def process_batch(self, job_id: str, batch_paths: List[str], eval_type: str, params: Dict[str, Any]) -> str:
    """Process a batch of files and save results"""
    try:
        # Process files
        results = []
        for file_path in batch_paths:
            result = process_file(file_path, eval_type, params)
            results.append(result)

        # Combine results
        combined_result = combine_results(results)

        # Save to MongoDB
        eval_result = EvaluationResult(
            job_id=job_id,
            eval_type=eval_type,
            params=params,
            filters=params.get("filters", {}),
            weights=params.get("weights"),
            summary=combined_result.get("summary"),
            deals=combined_result.get("deals"),
            hits=combined_result.get("hits")
        )

        await mongodb_service.save_evaluation(eval_result)

        # Save to storage
        result_path = storage.save_batch_results(job_id, combined_result)
        return result_path

    except Exception as e:
        await mongodb_service.update_evaluation_status(
            job_id=job_id,
            status="failed",
            error=str(e)
        )
        raise


@celery_app.task
@redis_cache(expire=settings.REDIS_CACHE_EXPIRE)
@joblib_cache(expire=settings.JOBLIB_CACHE_EXPIRE)
async def finalize_job(job_id: str, batch_result_paths: List[str]) -> str:
    """Combine all batch results and save final result"""
    try:
        # Load and combine batch results
        final_result = {}
        for path in batch_result_paths:
            batch_result = storage.load_batch_result(path)
            final_result = combine_results([final_result, batch_result])

        # Update MongoDB with final results
        eval_result = await mongodb_service.get_evaluation(job_id)
        if eval_result:
            eval_result.summary = final_result.get("summary")
            eval_result.deals = final_result.get("deals")
            eval_result.hits = final_result.get("hits")
            await mongodb_service.save_evaluation(eval_result)

        # Save final result
        final_path = storage.save_final_result(job_id, final_result)
        return final_path

    except Exception as e:
        await mongodb_service.update_evaluation_status(
            job_id=job_id,
            status="failed",
            error=str(e)
        )
        raise


@celery_app.task(bind=True)
async def start_job(self, file_list: List[str], eval_type: str, params: Dict[str, Any]) -> str:
    """Start a new evaluation job with different evaluation types."""
    batch_size = 10
    try:
        job_id = self.request.id

        # Create initial MongoDB record
        eval_result = EvaluationResult(
            job_id=job_id,
            eval_type=eval_type,
            params=params,
            filters=params.get("filters", {}),
            weights=params.get("weights"),
            status="processing"
        )
        await mongodb_service.save_evaluation(eval_result)

        # Start Evaluate
        batches = [file_list[i:i + batch_size] for i in range(0, len(file_list), batch_size)]

        # Tạo group các task process_batch
        batch_tasks = group(
            process_batch.s(job_id, batch, eval_type, params)
            for batch in batches
        )
        # Storage batch
        # Combine all batch path - > storage
        workflow = chord(batch_tasks)(finalize_job.s(job_id))
        final_path = workflow.get(timeout=600)
        workflow.apply_async()

        # Preprocess all batch
            # Prepare for simulation


        # Start processing based on eval_type
        if eval_type == "weight":
            results = evaluate_for_weight(
                params.get("filters"),
                params.get("weights"),
                params.get("cutloss", 0.15),
                params.get("lookback", 5),
                params.get("k_exp", 0),
                params.get("thres_buy", 1),
                params.get("thres_sell", -1)
            )
        elif eval_type == "all":
            results = evaluate_for_all(
                params.get("filters"),
                params.get("cutloss", 0.1)
            )
        elif eval_type == "shortsell":
            results = evaluate_for_shortsell(
                params.get("filters"),
                params.get("cutloss", 0.1)
            )
        else:
            raise ValueError(f"Unsupported eval_type: {eval_type}")

        # Process results and save final output
        final_result = process_results(results)
        final_path = storage.save_final_result(job_id, final_result)

        # Update evaluation status
        await mongodb_service.update_evaluation_status(
            job_id=job_id,
            status="completed",
            result_path=final_path
        )

        return final_path

    except Exception as e:
        await mongodb_service.update_evaluation_status(
            job_id=job_id,
            status="failed",
            error=str(e)
        )
        raise


def process_file(file_path: str, eval_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
    result = evaluate_file(eval_type, file_path, params)
    return result


def combine_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Combine multiple results into one"""
    # ... existing combine_results code ...
    pass

@app.task
def async_run_simulation(deals_data, params):
    df_deals = pd.read_json(deals_data)
    result, simulate_log = run_simulation(df_deals, **params)
    return result, simulate_log