import json
import os
import os
import time
from datetime import datetime, timezone
from typing import Dict, <PERSON>, Tuple, Any
from celery import chain, group

import numpy as np
import pandas as pd

from core_utils.base_eval import AllEval, WeightEval, ShortSellEval, Simulation
from webui.utils import PreProcess, Simulation_all, memory, Simulation_weight
from core_utils.constant import RANKING, MAX_DECREASE, SIMULATE_FULL_CASH, SIMULATE_FULL_CASH_NOT_FIX, \
    SIMULATE_ALLOCATE, JOBLIB_CACHE_DIR
from processing.celery_app.utils import parse_time

from processing.celery_app.celery_app import celery_app, logger
from processing.core.config import settings
from processing.shared.storage import NFSStorage
from processing.core.cache import redis_cache, joblib_cache, cache_get, cache_set, cache_delete, cache_generate_key
from processing.shared.mongodb import mongodb_service
from processing.shared.models import EvaluationResult
from processing.celery_app.func_task import evaluate_file


@memory.cache
def evaluate_file(eval_type, file_path: str, params) -> Dict[str, Any]:
    """Evaluate a single stock file"""
    # Read file
    df = pd.read_csv(file_path)

    # Get ticker from filename
    ticker = file_path.split("/")[-1].replace(".csv", "")

    # Initialize appropriate evaluator
    if eval_type == "all":
        evaluator = AllEval(
            stock=ticker,
            data_frame=df,
            dict_filter=params["filters"],
            cutloss=params.get("cutloss", 0.15)
        )
        res_h = evaluator.get_hit()
        res_d = evaluator.get_deal()
        res_b = evaluator.append_full_indicators(res_d)

        result = (res_h, res_b, res_d)

    elif eval_type == "weight":
        evaluator = WeightEval(
            stock=ticker,
            data_frame=df,
            dict_filter=params["filters"],
            weight=params["weights"],
            threshold_buy=params.get("threshold_buy", 1.0),
            threshold_sell=params.get("threshold_sell", -1.0),
            cutloss=params.get("cutloss", 0.15),
            lookback=params.get("lookback", 5),
            k_exp=params.get("k_exp", 0)
        )
        res_1 = evaluator.get_eval_weight_hit()
        res_2 = evaluator.get_df_weight()

        result = (res_1, res_2)

    elif eval_type == "shortsell":
        evaluator = ShortSellEval(
            stock=ticker,
            data_frame=df,
            dict_filter=params["filters"],
            cutloss=params.get("cutloss", 0.15)
        )
        res_s = evaluator.get_shortsell()
        res_f = evaluator.append_full_indicators(res_s)
        result = (res_s, res_f)

    else:
        raise ValueError(f"Unknown evaluation type: {eval_type}")

    # Add metadata
    result["ticker"] = ticker
    result["eval_type"] = eval_type

    return result


@celery_app.task()
def simulate(eval_type, df_deals, params, iterate=10, handle=None):
    start_date, end_date = parse_time(params["filters"])
    df_deals = Simulation.preprocess_df_deals(df_deals, start_date=start_date, end_date=end_date)

    np.random.seed(iterate)
    iterate_range = np.linspace(0, 365 * 2, iterate + 1, dtype=int)
    random_list = [np.random.randint(r[0], r[1]) for r in zip(iterate_range[:-1], iterate_range[1:])]

    if handle == 'combine':
        df_deals[RANKING] = df_deals[params['selected_rank_col']]
        df_deals = df_deals[df_deals[RANKING] > 0]
        # Deduplicate rows with the same ticker in the same time
        df_deals = df_deals.drop_duplicates(subset=['ticker', 'time'], keep='first').reset_index(
            drop=True)

    params['start_date'] = start_date
    params['end_date'] = end_date

    # Call subtask celery
    # ++++++++++++++++++
    key = cache_generate_key(f'simulate_task:{eval_type}:{json.dumps(params["filters"], sort_keys=True)}')
    cache_set(key, df_deals, expire=600)
    params['cache_key'] = key

    all_results = group(simulate_task.s(eval_type, seed, params, handle) for seed in random_list)
    all_results = all_results.apply_async()

    cache_delete(key)
    # ++++++++++++++++++
    df = pd.json_normalize(all_results)
    df_result = pd.Series(index=df.columns)

    for col in df.columns:
        if 'return' in col:
            df_result[col] = df[col].mean(skipna=True)
            df_result[col.replace('return', 'return_std')] = df[col].std(skipna=True)
            df_result[col.replace('return', 'return_max')] = df[col].max(skipna=True)
            df_result[col.replace('return', 'return_min')] = df[col].min(skipna=True)
        elif ('set_ticker' in col) or ('set_quarter_ticker' in col):
            lengths = [len(v) for v in df[col].values]
            tickers = [ticker for v in df[col].values for ticker in v]
            df_result[col] = len(set(tickers)) / np.mean(lengths)
            df_result[col.replace('set', 'unique')] = len(set(tickers))

        else:
            df_result[col] = df[col].mean(skipna=True)

    return Simulation.convert_to_dict(df_result)


@celery_app.task()
def simulate_task(eval_type, seeds, params, handle=None):
    df_deal = cache_get(params['cache_key'])

    if eval_type == "all":
        simulation = Simulation_all(df_deals=df_deal, start_date=params["start_date"], end_date=params["end_date"],
                                    initial_assets=params["si_assets"], max_deals=params["si_slots"],
                                    combine=params["combine"], combine_block=params["combine_block"])
    elif eval_type == 'weight':
        simulation = Simulation_weight(start_date=params["start_date"], end_date=params["end_date"],
                                       initial_assets=params["si_assets"], max_deals=params["si_slots"])
    else:
        simulation = Simulation_all(df_deals=df_deal, start_date=params["start_date"], end_date=params["end_date"],
                                    initial_assets=params["si_assets"], max_deals=params["si_slots"],
                                    combine=params["combine"], combine_block=params["combine_block"])

    results = []
    for seed in seeds:
        if handle == 'combine':
            result = simulation.combine_simulation(seed=seed, si_type=params["si_type"])
        else:
            result = simulation.simulation(seed=seed, si_type=params["si_type"])
        results.append(result)

    return results


@celery_app.task()
def simulate_task_get_detail(eval_type, params, df_deal):
    if eval_type == "all":
        simulation = Simulation_all(df_deals=df_deal, start_date=params["start_date"], end_date=params["end_date"],
                                    initial_assets=params["si_assets"], max_deals=params["si_slots"],
                                    combine=params["combine"], combine_block=params["combine_block"])
    elif eval_type == 'weight':
        simulation = Simulation_weight(start_date=params["start_date"], end_date=params["end_date"],
                                       initial_assets=params["si_assets"], max_deals=params["si_slots"])
    else:
        simulation = Simulation_all(df_deals=df_deal, start_date=params["start_date"], end_date=params["end_date"],
                                    initial_assets=params["si_assets"], max_deals=params["si_slots"],
                                    combine=params["combine"], combine_block=params["combine_block"])

    result = simulation.get_detail(s_type=params["si_type"])

    return result

def run_simulation(df_deals, start_date, end_date, simulative_type, simulative_slots, simulative_assets, combines,
                   combine_block):
    simulation = Simulation_all(
        start_date=start_date,
        end_date=end_date,
        initial_assets=simulative_assets,
        max_deals=simulative_slots,
        combine=combines,
        combine_block=combine_block
    )
    result = simulation.run_fast(df_deals=df_deals, iterate=70, s_type=simulative_type)
    detail_simulation_result = simulation.get_detail(s_type=simulative_type)
    return result, detail_simulation_result['log']


def run_simulation_by_weight(df_deals, dictFilter, simulative_type, simulative_slots, simulative_assets,
                             simulation_type):
    df_process = PreProcess()
    df = df_process.weight_hits(df_deals)

    start_date, end_date = parse_time(dictFilter)

    simulate_cols = ['ticker', 'time', 'Close', 'Price', 'buy_price', 'Volume', 'Volume_1M', 'Volume_1M_P50',
                     'score', 'sell_time', 'sell_filter', 'sell_score', 'sell_price', 'profit',
                     'holding_period', 'quarter', 'quarter_fr', 'Volume_sell', 'Low_sell', 'Open_sell', 'High_sell',
                     'Close_sell', 'Close_T1_sell']

    simulation = Simulation_weight(start_date=start_date, end_date=end_date,
                                   initial_assets=simulative_assets, max_deals=simulative_slots)
    simulation_results = simulation.run_fast(df_deals=df[simulate_cols], iterate=30, s_type=simulation_type)

    # simulation logs
    detail_simulation_result = simulation.get_detail(s_type=simulation_type)


def run_simulation_all(df_deals, dictFilter, simulative_type, simulative_slots, simulative_assets, combine_only,
                       combine_type='synthetic', combine_weight=None, combine_block=False):
    def ranking_score_func(deal_df, sumary_df, rank_col, weight):
        deal_df = deal_df.copy()

        if rank_col == 'technical_point':
            conditions = {
                "w_1": deal_df['D_RSI'] / deal_df['D_RSI_T1W'] > 1,
                "w_2": deal_df['Close'] / deal_df['VAP1W'] > 1,
                "w_3": deal_df['D_MFI'] / deal_df['D_MFI_T1W'] > 1,
                "w_4": deal_df['D_MACD'] / deal_df['D_MACD_T1W'] > 1,
                "w_5": (deal_df['Close'] / deal_df['LO_3M_T1'] > 0.97) & (deal_df['Close'] / deal_df['LO_3M_T1'] < 1.2),
                "w_6": deal_df['Volume'] / deal_df['Volume_1M'] > 1,
                "w_7": deal_df['D_RSI_Max1W'] / deal_df['D_RSI_Max3M'] > 1,
                "w_8": 1,
            }

            deal_df[rank_col] = sum(weight.get(k, 0) * v for k, v in conditions.items())

        if rank_col == 'weight_point':
            pass

        if rank_col == 'order_point':
            conditions = {
                "w_1": 'BKMA200',
                "w_2": 'TrendingGrowth',
                "w_3": 'TL3M',
                "w_4": 'BuySupport',
                "w_5": 'RSILow30',
                "w_6": 'UnderBV',
                "w_7": 'SuperGrowth',
                "w_8": 'SurpriseEarning',
                "w_9": 'Conservative',
                "w_10": 'BullDvg',
                "w_11": 'VolMax1Y',
                "w_12": 'T3P4',
            }
            filter_scores = pd.DataFrame({
                'filter': [v for k, v in conditions.items()],
                rank_col: [weight.get(k, 0) for k, v in conditions.items()]
            })

            deal_df = deal_df.merge(filter_scores, on='filter', how='left')

        if rank_col == 'synthetic':
            buy_filter = list(deal_df['filter'].unique())
            deal_df[buy_filter] = 0
            grouped = deal_df.groupby(["time", "ticker"])
            deal_df = grouped.apply(lambda group: group.assign(**{filter: 1 for filter in group['filter']}))
            conditions = {
                "w_1": deal_df['D_RSI'] / deal_df['D_RSI_T1W'] > 1,
                "w_2": deal_df['Close'] / deal_df['VAP1W'] > 1,
                "w_3": deal_df['D_MFI'] / deal_df['D_MFI_T1W'] > 1,
                "w_4": deal_df['D_MACD'] / deal_df['D_MACD_T1W'] > 1,
                "w_5": (deal_df['Close'] / deal_df['LO_3M_T1'] > 0.97) & (deal_df['Close'] / deal_df['LO_3M_T1'] < 1.2),
                "w_6": deal_df['Volume'] / deal_df['Volume_1M'] > 1,
                "w_7": deal_df['D_RSI_Max1W'] / deal_df['D_RSI_Max3M'] > 1,
                "w_8": deal_df['FSCORE'] / 9,
                "w_9": deal_df['BKMA200'] if 'BKMA200' in deal_df.columns else 0,
                "w_10": deal_df['BullDvg'] if 'BullDvg' in deal_df.columns else 0,
                "w_11": deal_df['BuySupport'] if 'BuySupport' in deal_df.columns else 0,
                "w_12": deal_df['Conservative'] if 'Conservative' in deal_df.columns else 0,
                "w_13": deal_df['RSILow30'] if 'RSILow30' in deal_df.columns else 0,
                "w_14": deal_df['SuperGrowth'] if 'SuperGrowth' in deal_df.columns else 0,
                "w_15": deal_df['SurpriseEarning'] if 'SurpriseEarning' in deal_df.columns else 0,
                "w_16": deal_df['TL3M'] if 'TL3M' in deal_df.columns else 0,
                "w_17": deal_df['TrendingGrowth'] if 'TrendingGrowth' in deal_df.columns else 0,
                "w_18": deal_df['UnderBV'] if 'UnderBV' in deal_df.columns else 0,
                "w_19": deal_df['VolMax1Y'] if 'VolMax1Y' in deal_df.columns else 0,
                "w_20": deal_df['T3P4'] if 'T3P4' in deal_df.columns else 0,
                "w_21": 1,
            }

            deal_df[rank_col] = sum(weight.get(k, 0) * v for k, v in conditions.items())

        if rank_col == 'ranking_point':
            sumary_df['win_deal'] = (sumary_df['count_win'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['loss_deal'] = (sumary_df['count_loss'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['hold_deal'] = (sumary_df['count_hold'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df['cutloss_deal'] = (sumary_df['count_cutloss'].astype(float) / sumary_df['deal'].astype(float))
            sumary_df[rank_col] = 0.4 * sumary_df["si_return"] + 100 * (
                    0.3 * sumary_df["win_deal"] +
                    0.15 * sumary_df["win_quarter"] +
                    0.1 * sumary_df["winblock_20quarters"] +
                    0.05 * sumary_df["winblock_24months"]
            )

            # Prepare for ranking
            deal_df = deal_df.merge(sumary_df[['filter', rank_col]], on='filter', how='left')

        if rank_col == 'win_deal':
            sumary_df[rank_col] = (sumary_df['count_win'].astype(float) / sumary_df['deal'].astype(float))
            deal_df = deal_df.merge(sumary_df[['filter', rank_col]], on='filter', how='left')

        if rank_col == 'trading_daily':
            deal_df[rank_col] = deal_df['Volume'] * deal_df['Price']

        return deal_df

    if combine_weight is None:
        combine_weight = {}

    start_date, end_date = parse_time(dictFilter)
    combines = dictFilter.get('CombinePattern', None)

    simulation = Simulation_all(start_date=start_date, end_date=end_date,
                                initial_assets=simulative_assets, max_deals=simulative_slots,
                                combine=combines, combine_block=combine_block)

    if not combine_only:
        simulate_result = simulation.run_fast(df_deals=df_deals, iterate=70, s_type=simulative_type)

    for pattern, value in simulate_result.items():
        if pattern not in _pdd_d['filter'].unique():
            _pdd_d = _pdd_d._append({'filter': pattern}, ignore_index=True)

    df_combine_deals = ranking_score_func(deal_df=df_deals, sumary_df=_pdd_d, rank_col=combine_type,
                                          weight=combine_weight)
    #
    combine_result = simulation.run_fast(df_deals=df_combine_deals, iterate=70, s_type=simulative_type,
                                         r_col=combine_type, handle='combine')

    detail_simulation_result = simulation.get_detail(s_type=simulative_type)
    simulate_log = detail_simulation_result['log']


@memory.cache
def eval_filter_by_weight(evaluate_datas, simulate_datas):
    # parse data
    df, df_weight = evaluate_datas
    simulation_results, detail_simulation_result = simulate_datas

    df_process = PreProcess()
    df = df_process.weight_hits(df)

    df_weight["quarter"] = pd.to_datetime(df_weight['time']).dt.to_period('Q').astype(str)
    df_weight["quarter_fr"] = df_weight['time'].apply(lambda x: df_process.quarter_report(x))

    _df_q = df.groupby(['quarter'], as_index=False).agg({
        'p_win': 'count',
        'p_loss': 'count',
        'p_cutloss': 'count',
    })
    _df_q.rename(columns={'p_win': 'Win', 'p_loss': 'Loss', 'p_cutloss': 'Cutloss'}, inplace=True)
    _df_q_tail = _df_q.tail(9).iloc[:-1]
    ###
    _df_q_fr_s = df_process.group_by(df, ['ticker', 'quarter_fr', 'score'])
    _df_weight_q_fr_s = df_process.group_by(df_weight, ['ticker', 'quarter_fr', 'score'])

    pd_keep = detail_simulation_result['completed_deals']
    pd_skip = detail_simulation_result['skip_deals']
    simulative_log = detail_simulation_result['detail_log']

    si_cols = ['si_hit', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_utilization', 'si_win_deal', 'si_win_quarter', 'si_ticker_diversity',
               'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker',
               'si_peak_number_deals']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'utilization', 'win_deal', 'win_quarter', 'set_ticker',
                      'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker', 'peak_number_deals']

    si_result = {}
    for si_col, si_result_col in zip(si_cols, si_result_cols):
        si_result[si_col] = simulation_results['sum_weight'][si_result_col]

    # Distribute, statistical by hits
    bins = 10
    pd_distribute = df[
        ['score', 'p_win', 'p_loss', 'p_cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P2W', 'P3W', 'P1M',
         'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
    pd_distribute['score'] = pd_distribute['score'].astype(float)
    binx = sorted(list(set(np.percentile(pd_distribute['score'].dropna(), np.arange(0, 101, 100 / bins)))))
    pd_distribute['score'] = pd.cut(pd_distribute['score'], bins=binx)

    pd_distribute['count'] = 1
    pd_distribute['win_count'] = pd_distribute['p_win']
    pd_distribute['loss_count'] = pd_distribute['p_loss']
    pd_distribute['cutloss_count'] = pd_distribute['p_cutloss']

    pd_distribute = pd_distribute.groupby('score', as_index=False, observed=False).agg({
        'count': 'sum',
        "win_count": 'count',
        "loss_count": 'count',
        "cutloss_count": 'count',
        'p_sell': 'mean',
        'corr_s2p_on_ticker': 'mean',
        'P1W': 'mean',
        'P1M': 'mean',
        'P3M': 'mean',
        'P6M': 'mean',
        'P1Y': 'mean',
        'P2Y': 'mean',
    })
    pd_distribute['%win'] = pd_distribute['win_count'] / pd_distribute['count']
    pd_distribute['%loss'] = pd_distribute['loss_count'] / pd_distribute['count']
    pd_distribute['%cutloss'] = pd_distribute['cutloss_count'] / pd_distribute['count']
    pd_distribute = pd_distribute[
        ['score', 'count', '%win', '%loss', '%cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P1M', 'P3M',
         'P6M', 'P1Y', 'P2Y']]

    # Distribute, statistical by hits are deduplicated by (ticker, quarter_fp, score)
    bins = 10
    pd_distribute_q = _df_q_fr_s[
        ['score', 'p_win', 'p_loss', 'p_cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P2W', 'P3W', 'P1M',
         'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
    pd_distribute_q['score'] = pd_distribute_q['score'].astype(float)
    binx = sorted(list(set(np.percentile(pd_distribute_q['score'].dropna(), np.arange(0, 101, 100 / bins)))))
    pd_distribute_q['score'] = pd.cut(pd_distribute_q['score'], bins=binx)

    pd_distribute_q['count'] = 1
    pd_distribute_q['win_count'] = pd_distribute_q['p_win']
    pd_distribute_q['loss_count'] = pd_distribute_q['p_loss']
    pd_distribute_q['cutloss_count'] = pd_distribute_q['p_cutloss']

    pd_distribute_q = pd_distribute_q.groupby('score', as_index=False, observed=False).agg({
        'count': 'sum',
        "win_count": 'count',
        "loss_count": 'count',
        "cutloss_count": 'count',
        'p_sell': 'mean',
        'corr_s2p_on_ticker': 'mean',
        'P1W': 'mean',
        'P1M': 'mean',
        'P3M': 'mean',
        'P6M': 'mean',
        'P1Y': 'mean',
        'P2Y': 'mean',
    })
    pd_distribute_q['%win'] = pd_distribute_q['win_count'] / pd_distribute_q['count']
    pd_distribute_q['%loss'] = pd_distribute_q['loss_count'] / pd_distribute_q['count']
    pd_distribute_q['%cutloss'] = pd_distribute_q['cutloss_count'] / pd_distribute_q['count']
    pd_distribute_q = pd_distribute_q[
        ['score', 'count', '%win', '%loss', '%cutloss', 'p_sell', 'corr_s2p_on_ticker', 'P1W', 'P1M', 'P3M',
         'P6M', 'P1Y', 'P2Y']]

    # Distribute, statistical by filter are deduplicated by (ticker, quarter_fp, score)
    bins = 10
    pd_distribute_score = _df_weight_q_fr_s[
        ['score', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].copy()
    pd_distribute_score['score'] = pd_distribute_score['score'].astype(float)

    eee = 10e-8
    # dive data to 3 space
    ranges = [(-np.inf, -1 + eee), (-1 + eee, 1 - eee), (1 - eee, np.inf)]
    final_bins = []

    for r_min, r_max in ranges:
        mask = (pd_distribute_score['score'] > r_min) & (pd_distribute_score['score'] < r_max)
        subset = pd_distribute_score.loc[mask, 'score']

        if not subset.empty:
            bin_edges = sorted(list(set(np.percentile(subset.dropna(), np.linspace(0, 100, bins + 1)))))
            final_bins.extend(bin_edges)

    final_bins = sorted(list(set(final_bins)))
    pd_distribute_score['score'] = pd.cut(pd_distribute_score['score'], bins=final_bins)
    pd_distribute_score['count'] = 1

    aggs_f = {
        'count': [('', 'count')],
        'P1W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P2W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P3W': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P1M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P2M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P3M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P6M': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P1Y': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
        'P2Y': [('mean', 'mean'), ('std', lambda x: np.std(x, ddof=0))],
    }
    for per in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
        pd_distribute_score[f'%win_{per}'] = pd_distribute_score[f'P{per}'].where(
            pd_distribute_score[f'P{per}'] > 0,
            np.nan)
        aggs_f[f'%win_{per}'] = [('', 'count')]

    pd_distribute_score = pd_distribute_score.groupby('score', as_index=False, observed=False).agg(aggs_f)
    pd_distribute_score.columns = ["_".join(col) if col[1] != '' else col[0] for col in pd_distribute_score.columns]

    for per in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
        pd_distribute_score[f'%win_{per}'] = pd_distribute_score[f'%win_{per}'] / pd_distribute_score['count']

    # lp = [0, 10, 20, 50, 80, 90, 100]
    # pd.DataFrame({'p': lp, 'profit': np.percentile(pdx['sell_profit'], lp),
    #               'hold': np.percentile(pdx['holding_session'], lp)})
    #

    # Summary
    pd_summary = pd.DataFrame({
        'hit': df.shape[0],
        'si_return': si_result['si_return'],
        'si_return_std': si_result['si_return_std'],
        'profit_expected': df['p_sell'].mean(),
        '%win': df['p_win'].count() / df.shape[0] * 100,
        '%loss': df['p_loss'].count() / df.shape[0] * 100,
        '%cutloss': df['p_cutloss'].count() / df.shape[0] * 100,
        '%win_quarter': (_df_q[(_df_q['Win'] > (_df_q['Loss'] + _df_q['Cutloss']))].shape[0] / _df_q.shape[
            0]) * 100,
        '%winblock_8q': (_df_q_tail[(_df_q_tail['Win'] > (_df_q_tail['Loss'] + _df_q_tail['Cutloss']))].shape[0] /
                         _df_q_tail.shape[0]) * 100,
        'profit_win': df['p_win'].mean(),
        'profit_loss': df['p_loss'].mean(),
        'profit_cutloss': df['p_cutloss'].mean(),
        'holding_period': df['holding_period'].mean(),
        'corr_score_profit_on_ticker': df['corr_s2p_on_ticker'].mean(),
        'si_total_time': si_result['si_total_time'],
        'si_time_in_market': si_result['si_time_in_market'],
        'si_hit': si_result['si_hit'],
        'si_ticker_diversity': si_result['si_ticker_diversity'],
        'si_q_ticker_diversity': si_result['si_quarter_ticker_diversity'],
        'si_profit': si_result['si_profit'],
        'si_cash_profit': si_result['si_cash_profit'],
        'si_utilization': si_result['si_utilization'],
        'si_%win_deal': si_result['si_win_deal'],
        'si_%win_quarter': si_result['si_win_quarter'],
        'si_unique_q_tickers': si_result['si_unique_quarter_ticker'],
        'si_peak_number_deals': si_result['si_peak_number_deals']
    }, index=[0])

    now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
    logger.info(
        f"## {now} -- Sum P_performance: {(pd_summary['si_return'] * pd_summary['si_hit']).sum() / pd_summary['si_hit'].sum()}")

    return pd_summary, pd_keep, pd_skip, simulative_log, pd_distribute, pd_distribute_q, pd_distribute_score


@memory.cache
def eval_filter_all(evaluate_datas, simulate_datas):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    pd_deal, pd_all, pd_b = evaluate_datas
    simulate_result, combine_result, simulate_log = simulate_datas

    # Process dataframe
    df_process = PreProcess()
    pd_deal = df_process.deals(pd_deal)
    pd_b = df_process.hits(pd_b)
    pdh = df_process.group_by(pd_b, ['filter'])

    _pdd_d = df_process.group_by(pd_deal, ['filter'])
    _pdd_q = df_process.group_by(pd_deal, ['filter', "quarter"])
    _pdd_q_combine = df_process.group_by(pd_deal, ["quarter"])

    pd_deal['month'] = pd_deal['time'].str[:7]
    _pdd_m = df_process.group_by(pd_deal, ['filter', "month"])
    _pdd_m_combine = df_process.group_by(pd_deal, ["month"])

    # Combine
    combined_deal = pd_deal.copy()
    combined_deal['filter'] = 'Combine'
    combined_deal = df_process.group_by(combined_deal, ['filter'])
    _pdd_d = _pdd_d._append(combined_deal)

    # latest hits dataframe
    pdy = pd_b[
        ['filter', 'ticker', 'time', 'Close', 'Volume', 'P1W', 'P1M', 'P3M', 'P6M', 'P1Y', 'P2Y', 'Sell_profit',
         'Sell_filter', 'Sell_time', 'holding_period']].copy()
    pdy.drop_duplicates(subset=['filter', 'ticker'], keep='last', inplace=True)
    pdy = pdy.sort_values('time', ascending=False).reset_index(drop=True)

    # pattern hit dataframe
    for f in pdh.columns:
        if f.startswith('spec_col_count'):
            col = f.split('_')[-1]
            pdh[f"%{col}"] = (pdh[f].astype(int) / pdh['hit'].astype(int)) * 100

    pdh["%Sell_win"] = (pdh['count_win'].astype(int) / pdh['hit'].astype(int)) * 100
    pdh["%Sell_loss"] = (pdh['count_loss'].astype(int) / pdh['hit'].astype(int)) * 100
    pdh['%Hold'] = (pdh['count_hold'].astype(int) / pdh['hit'].astype(int)) * 100
    pdh["%cutloss"] = (pdh['count_cutloss'].astype(int) / pdh['hit'].astype(int)) * 100

    pdh.insert(pdh.shape[1] - 1, 'P_Sell', pdh.pop("P_Sell"))
    pdh.insert(pdh.shape[1] - 1, 'P_Hold', pdh.pop("P_Hold"))
    pdh.insert(pdh.shape[1] - 1, 'P_cutloss', pdh.pop("P_cutloss"))
    pdh.insert(pdh.shape[1] - 1, 'P_expected', pdh.pop("Sell_profit"))
    pdh.insert(pdh.shape[1] - 1, 'Holding_period', pdh.pop("holding_period"))

    for f in pdh.columns:
        if f.startswith('spec_col'):
            pdh.drop(f, axis=1, inplace=True)
    pdh.drop(['count_win', 'count_loss', 'count_hold', 'count_cutloss'], axis=1, inplace=True)

    # historical deals dataframe
    pdx = pd_deal[['filter', 'ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time',
                   'holding_period']].copy().sort_values('time', ascending=False).reset_index(drop=True)

    # data for barchart
    pd_plot = _pdd_q.pivot_table(index='quarter', columns='filter', values='deal', aggfunc='sum').reset_index()

    # win_quarter
    _pdd_d['win_quarter'] = 0
    _pdd_d['winblock_20quarters'] = 0
    pd_histogram_quarter = {}
    for f in _pdd_d['filter'].unique():
        if f == 'Combine':
            data = _pdd_q_combine
        else:
            data = _pdd_q[_pdd_q['filter'] == f]
        data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                             'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                             'count_cutloss': 'Cutloss'}, inplace=True)

        pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

    for filter, df in pd_histogram_quarter.items():
        # win_quarter
        win = df[(df['Win'] + df['Win_Hold']) >= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
        loss = df[(df['Win'] + df['Win_Hold']) <= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
        _pdd_d.loc[_pdd_d['filter'] == filter, 'win_quarter'] = win / sum([win, loss]) if (
                                                                                                  win + loss) > 0 else 0

        df_tail = df.tail(21).iloc[:-1]
        win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        _pdd_d.loc[_pdd_d['filter'] == filter, 'winblock_20quarters'] = win_tail / sum(
            [win_tail, loss_tail]) if (win_tail + loss_tail) > 0 else 0

    # winblock_24months
    _pdd_d['winblock_24months'] = 0
    pd_histogram_month = {}
    for f in _pdd_d['filter'].unique():
        if f == 'Combine':
            data = _pdd_m_combine
        else:
            data = _pdd_m[_pdd_m['filter'] == f]
        data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                             'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                             'count_cutloss': 'Cutloss'}, inplace=True)

        pd_histogram_month[f] = data[['month', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

    for filter, df in pd_histogram_month.items():
        df_tail = df.tail(24)
        win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]

        _pdd_d.loc[_pdd_d['filter'] == filter, 'winblock_24months'] = win_tail / sum([win_tail, loss_tail]) \
            if (win_tail + loss_tail) > 0 else 0

    # combines = dictFilter.get('CombinePattern', None)

    now = time.time()
    # simulation = Simulation_all(start_date=start_date, end_date=end_date, num_proc=NUM_PROCESS,
    #                             initial_assets=simulative_assets, max_deals=simulative_slots,
    #                             combine=combines, combine_block=combine_block)
    si_cols = ['si_deals', 'si_total_time', 'si_time_in_market', 'si_cash_profit', 'si_profit', 'si_return',
               'si_return_std', 'si_return_max', 'si_return_min', 'si_utilization', 'si_win_deal', 'si_win_quarter',
               'si_ticker_diversity', 'si_quarter_ticker_diversity', 'si_unique_ticker', 'si_unique_quarter_ticker',
               'si_peak_number_deals']
    si_result_cols = ['match_deals', 'total_time', 'time_in_market', 'cash_profit', 'profit', 'return',
                      'return_std', 'return_max', 'return_min', 'utilization', 'win_deal', 'win_quarter',
                      'set_ticker', 'set_quarter_ticker', 'unique_ticker', 'unique_quarter_ticker',
                      'peak_number_deals']

    if not simulate_result.empty:
        for pattern, value in simulate_result.items():
            if pattern not in _pdd_d['filter'].unique():
                _pdd_d = _pdd_d._append({'filter': pattern}, ignore_index=True)

            for si_col, si_result_col in zip(si_cols, si_result_cols):
                _pdd_d.loc[_pdd_d['filter'] == pattern, si_col] = value[si_result_col]

    logger.info(f"Simulation time: {time.time() - now}")

    for pattern, value in combine_result.items():
        if pattern not in _pdd_d['filter'].unique():
            _pdd_d = _pdd_d._append({'filter': pattern}, ignore_index=True)

        for si_col, si_result_col in zip(si_cols, si_result_cols):
            _pdd_d.loc[_pdd_d['filter'] == pattern, si_col] = value[si_result_col]

    # pdd
    pdd = _pdd_d[['filter', 'deal']].copy()
    pdd['si_return'] = _pdd_d['si_return']
    pdd['si_return_std'] = _pdd_d['si_return_std']
    pdd['si_ticker_diversity'] = _pdd_d['si_ticker_diversity']
    pdd['si_q_ticker_diversity'] = _pdd_d['si_quarter_ticker_diversity']
    pdd['profit_expected'] = _pdd_d['profit']

    pdd['%win_deal'] = (_pdd_d['count_win'].astype(float) / _pdd_d['deal'].astype(float)) * 100
    pdd['%loss_deal'] = (_pdd_d['count_loss'].astype(float) / _pdd_d['deal'].astype(float)) * 100
    pdd['%hold_deal'] = (_pdd_d['count_hold'].astype(float) / _pdd_d['deal'].astype(float)) * 100
    pdd['%cutloss_deal'] = (_pdd_d['count_cutloss'].astype(float) / _pdd_d['deal'].astype(float)) * 100

    pdd["%win_quarter"] = _pdd_d['win_quarter'] * 100
    pdd["%winblock_20quarters"] = _pdd_d['winblock_20quarters'] * 100
    pdd["%winblock_24months"] = _pdd_d['winblock_24months'] * 100
    pdd["ranking_point"] = 0.4 * pdd["si_return"] + \
                           0.3 * pdd["%win_deal"] + \
                           0.15 * pdd["%win_quarter"] + \
                           0.1 * pdd["%winblock_20quarters"] + \
                           0.05 * pdd["%winblock_24months"]
    # 0.1 * pdd["deal"] / pdd["deal"].nlargest(2).values[1] * 100

    pdd['profit_win'] = _pdd_d['p_win']
    pdd['profit_loss'] = _pdd_d['p_loss']
    pdd['profit_hold'] = _pdd_d['p_hold']
    pdd['profit_cutloss'] = _pdd_d['p_cutloss']
    pdd['holding_period'] = _pdd_d['holding_period']
    pdd['profit_vni'] = _pdd_d['profit_vni']
    pdd['corr_deal_vni'] = _pdd_d['corr']
    # pdd['n_half'] = _pdd_d['n_half']
    # pdd['entropy'] = _pdd_d['entropy']
    pdd['si_total_time'] = _pdd_d['si_total_time']
    pdd['si_time_in_market'] = _pdd_d['si_time_in_market']
    pdd['si_deals'] = _pdd_d['si_deals']
    pdd['si_profit'] = _pdd_d['si_profit']
    pdd['si_cash_profit'] = _pdd_d['si_cash_profit']
    pdd['si_utilization'] = _pdd_d['si_utilization']
    pdd['si_%win_deal'] = _pdd_d['si_win_deal']
    pdd['si_%win_quarter'] = _pdd_d['si_win_quarter']
    pdd['si_unique_q_tickers'] = _pdd_d['si_unique_quarter_ticker']
    pdd['si_peak_number_deals'] = _pdd_d['si_peak_number_deals']
    pdd['si_return_max'] = _pdd_d['si_return_max']
    pdd['si_return_min'] = _pdd_d['si_return_min']

    now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
    logger.info(
        f"## {now} -- Sum P_performance: {(pdd['si_return'] * pdd['si_deals']).sum() / pdd['si_deals'].sum()}")
    return pdd, pdh, pdx, pdy, pd_all, pd_plot, pd_histogram_quarter, simulate_log


@memory.cache
def eval_filter_shortsell(evaluate_datas):
    """ Return dataframe of profit
        pdp: dataframe of profit
        pdx: dataframe of historical hit
        pdy: dataframe of latest hit
    """

    pd_short, pd_short_all = evaluate_datas

    # Process dataframe
    d_agg = {'trading_val': "sum", 'p_trading_val': "sum", 'trading_val_clip': "sum", 'p_trading_val_clip': "sum",
             'P1W': "median", 'P2W': "median", 'P3W': "median", 'P1M': "median", 'P2M': "median", 'P3M': "median",
             'P6M': "median", 'P1Y': "median", 'P2Y': "median", 'median_profit': "median"}
    df_process = PreProcess()
    pd_short = df_process.shortsell(pd_short)
    _pds_d = df_process.group_by(pd_short, ['filter'], d_agg=d_agg)
    _pdd_q = df_process.group_by(pd_short, ['filter', "quarter"], d_agg=d_agg)
    pd_short['month'] = pd_short['time'].str[:7]
    _pdd_m = df_process.group_by(pd_short, ['filter', "month"], d_agg=d_agg)

    # data for barchart
    # pd_plot = _pdd_q.pivot_table(index='quarter', columns='filter', values='deal', aggfunc='sum').reset_index()

    # win_quarter
    _pds_d['win_quarter'] = 0
    _pds_d['winblock_20quarters'] = 0
    pd_histogram_quarter = {}
    for f in _pdd_q['filter'].unique():
        data = _pdd_q[_pdd_q['filter'] == f]
        data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                             'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                             'count_cutloss': 'Cutloss'}, inplace=True)

        pd_histogram_quarter[f] = data[['quarter', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

    for filter, df in pd_histogram_quarter.items():
        win = df[(df['Win'] + df['Win_Hold']) >= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]
        loss = df[(df['Win'] + df['Win_Hold']) <= (df['Loss'] + df['Cutloss'] + df['Loss_Hold'])].shape[0]

        _pds_d.loc[_pds_d['filter'] == filter, 'win_quarter'] = win / sum([win, loss]) \
            if (win + loss) > 0 else 0

        df_tail = df.tail(21).iloc[:-1]
        win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        _pds_d.loc[_pds_d['filter'] == filter, 'winblock_20quarters'] = win_tail / sum(
            [win_tail, loss_tail]) if (win_tail + loss_tail) > 0 else 0

    # winblock_24months
    _pds_d['winblock_24months'] = 0
    pd_histogram_month = {}
    for f in _pdd_m['filter'].unique():
        data = _pdd_m[_pdd_m['filter'] == f]
        data.rename(columns={'count_win': 'Win', 'count_loss': 'Loss', 'count_hold': 'Hold',
                             'count_hold_win': 'Win_Hold', 'count_hold_loss': 'Loss_Hold',
                             'count_cutloss': 'Cutloss'}, inplace=True)

        pd_histogram_month[f] = data[['month', 'Win', 'Loss', 'Win_Hold', 'Loss_Hold', 'Cutloss']]

    for filter, df in pd_histogram_month.items():
        df_tail = df.tail(24)
        win_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) >= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]
        loss_tail = df_tail[(df_tail['Win'] + df_tail['Win_Hold']) <= (
                df_tail['Loss'] + df_tail['Cutloss'] + df_tail['Loss_Hold'])].shape[0]

        _pds_d.loc[_pds_d['filter'] == filter, 'winblock_24months'] = win_tail / sum([win_tail, loss_tail]) \
            if (win_tail + loss_tail) > 0 else 0

    pds = _pds_d[['filter', 'deal']].copy()
    pds['profit_expected'] = _pds_d['profit'].values
    pds['median_profit'] = _pds_d['median_profit'].values
    pds['weighted_profit'] = _pds_d['p_trading_val'] / _pds_d['trading_val'] * 100
    pds['%win_deal'] = (_pds_d['count_win'].astype(int) / _pds_d['deal'].astype(int)) * 100
    pds['%loss_deal'] = (_pds_d['count_loss'].astype(int) / _pds_d['deal'].astype(int)) * 100
    pds['%hold_deal'] = (_pds_d['count_hold'].astype(int) / _pds_d['deal'].astype(int)) * 100
    pds["%cutloss_deal"] = (_pds_d['count_cutloss'].astype(int) / _pds_d['deal'].astype(int)) * 100
    pds["%win_quarter"] = _pds_d['win_quarter'] * 100
    pds["%winblock_20quarters"] = _pds_d['winblock_20quarters'] * 100
    pds["%winblock_24months"] = _pds_d['winblock_24months'] * 100
    pds["trading_val"] = _pds_d['trading_val']
    pds["p_trading_val"] = _pds_d['p_trading_val']
    pds["weighted_profit_clip"] = _pds_d['p_trading_val_clip'] / _pds_d['trading_val_clip']
    pds[['P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']] = _pds_d[
        ['P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']].values

    pds['profit_win'] = _pds_d['p_win'].values
    pds['profit_loss'] = _pds_d['p_loss'].values
    pds['profit_hold'] = _pds_d['p_hold'].values
    pds['profit_cutloss'] = _pds_d['p_cutloss'].values
    pds['holding_period'] = _pds_d['holding_period'].values
    pds['profit_vni'] = _pds_d['profit_vni'].values
    pds['corr_deal_vni'] = _pds_d['corr'].values

    # historical short deal
    pd_short['weight_before_clip'] = (pd_short['Price'] * pd_short['Volume'] / pd_short['Trading_Session'])
    pd_short['weight_clip'] = (pd_short['Price'] * pd_short['Volume'] / pd_short['Trading_Session']).clip(
        upper=0.01)

    pd_sd = pd_short[['filter', 'ticker', 'time', 'sell_price', 'buy_price', 'profit', 'buy_filter', 'buy_time',
                      'holding_period', 'trading_val', 'p_trading_val', 'weight_before_clip',
                      'weight_clip']].sort_values(
        'time', ascending=False).reset_index(drop=True)

    now = (datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M:%S"))
    logger.info(
        f"## {now} -- Sum P_performance: {(pds['profit_expected'] * pds['deal']).sum() / pds['deal'].sum()}")
    return pds, pd_sd, pd_short_all, pd_histogram_quarter


def process_results(results: List[Tuple]) -> Dict:
    """Process evaluation results and return a structured dictionary."""
    try:
        processed_results = {
            "summary": [],
            "details": [],
        }

        for result in results:
            if result:
                processed_results["summary"].append(result[0])
                processed_results["details"].append(result[1])

        return processed_results
    except Exception as e:
        raise ValueError(f"Error processing results: {e}")
