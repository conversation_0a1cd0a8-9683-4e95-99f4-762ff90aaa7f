from celery import Celery
from processing.core.config import settings
from processing.core_utils.log import get_logger
import os

celery_app = Celery(
    "stock_evaluator",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=["processing.celery_app.tasks"]
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_publish_retry=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
)

logger = get_logger(os.environ.get("SERVICE", "UNKNOWN"))
