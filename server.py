# server.py
import requests
import json
import datetime
from requests_oauthlib import OAuth1
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Netsuite")


import logging # Added logging module
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    filename='./server.log', # Log to a file
    filemode='a', # Append mode
    
)
logger = logging.getLogger(__name__)

# Define a global base URL for NetSuite API
NETSUITE_BASE_URL = "https://td2948319.suitetalk.api.netsuite.com/services/rest"
# It's good practice to store credentials securely, e.g., environment variables or a config file,
# rather than hardcoding them directly in the script. For this example, I'll keep them as is.
CLIENT_KEY = "****************************************************************"
CLIENT_SECRET = "41fd12f5efc1a652a9d0212b58ab7fa66e9b2f87a33d4e6f3ac670826cbef041"
RESOURCE_OWNER_KEY = "****************************************************************"
RESOURCE_OWNER_SECRET = "e1e9458bead196ac92c4da57bd58b1b747cc293f642a2b674ebef508288ca993"
REALM = "TD2948319"

def get_netsuite_auth():
    """Helper function to create OAuth1 object."""
    return OAuth1(
        client_key=CLIENT_KEY,
        client_secret=CLIENT_SECRET,
        resource_owner_key=RESOURCE_OWNER_KEY,
        resource_owner_secret=RESOURCE_OWNER_SECRET,
        realm=REALM,
        signature_method="HMAC-SHA256",
    )

@mcp.tool()
def ns_get_all_customer(offset: int = 0, limit: int = 100) -> json:

    if limit > 1000: # Giới hạn cho Record API có thể khác SuiteQL, nhưng 1000 vẫn là con số hợp lý
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000 # Điều chỉnh nếu cần, API có thể có giới hạn riêng
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/customer?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching customers: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        # THÊM LOGGING CHI TIẾT CHO RAW RESPONSE (TRƯỚC KHI RAISE_FOR_STATUS)
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} customers. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample customer data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        # Sử dụng raw_text_snippet đã ghi log ở trên nếu http_err.response.text vẫn rỗng
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet # Fallback to logged raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching customers: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch customer information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error # Sẽ chứa raw_text_snippet nếu response.text của lỗi là rỗng
        }
    except json.JSONDecodeError as json_err:
        # ... (phần xử lý lỗi JSONDecodeError giữ nguyên) ...
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching customers: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        # ... (phần xử lý lỗi Exception giữ nguyên) ...
        logger.error(f"An unexpected error occurred while fetching customers: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_customer(customer_id: str) -> json:
    """Fetch specific customer information from NetSuite by ID."""
    logger.info(f"Fetching customer information for ID: {customer_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/customer/{customer_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched customer ID: {customer_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching customer {customer_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch customer information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching customer {customer_id}: {e}")
        return {"error": "Failed to fetch customer information", "details": str(e)}

@mcp.tool()
def ns_sales_order(order_id: str) -> json:
    """Fetch sales order details from NetSuite by its internal ID."""
    logger.info(f"Fetching order details for ID: {order_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/salesOrder/{order_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Response from NetSuite for order ID {order_id}: {response.text[:200]}...") # Log snippet of response
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching sales order {order_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch sales order", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"Error fetching order details for ID {order_id}: {str(e)}")
        return {"error": "Failed to fetch sales order", "details": str(e)}

@mcp.tool()
def ns_update_customer(customer_id: str, customer_data: dict) -> json:
    """Update customer details in NetSuite with the provided data.
    Requires the internal ID of the customer."""
    logger.info(f"Attempting to update customer ID: {customer_id}")
    if not customer_id:
        logger.error("Customer ID is required for update operation.")
        return {"error": "Customer ID is required"}
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/customer/{customer_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json", "Prefer": "return=representation"}
        payload = json.dumps(customer_data)
        logger.debug(f"Update payload for customer {customer_id}: {payload}")
        response = requests.request("PATCH", URL, auth=auth, headers=headers, data=payload)
        response.raise_for_status()
        if response.status_code == 204: # Should ideally not happen if Prefer is respected
            logger.info(f"Customer {customer_id} updated successfully (204 No content returned).")
            return {"success": True, "id": customer_id, "message": "Customer updated successfully."}
        r_response = json.loads(response.text)
        logger.info(f"Customer {customer_id} updated successfully. Response: {response.text[:200]}...")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error updating customer {customer_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to update customer details", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"Error updating customer {customer_id}: {str(e)}")
        return {"error": "Failed to update customer details", "details": str(e)}

@mcp.tool()
def ns_create_customer(
    subsidiary_id: str,
    is_person: bool,
    company_name: str = None,
    first_name: str = None,
    last_name: str = None,
    email: str = None,
    phone: str = None,
    alt_phone: str = None,
    fax: str = None,
    url: str = None,
    vat_reg_number: str = None,
    comments: str = None, # Initial comments
    # Các trường tham chiếu thường yêu cầu internal ID của record tương ứng
    currency_id: str = None, # ID của đơn vị tiền tệ (ví dụ: "1" cho USD)
    terms_id: str = None, # ID của điều khoản thanh toán
    credit_limit: float = None,
    price_level_id: str = None, # ID của mức giá
    lead_source_id: str = None, # ID của nguồn khách hàng tiềm năng
    sales_rep_id: str = None, # ID của nhân viên bán hàng
    category_id: str = None, # ID của danh mục khách hàng
    entity_status_id: str = None, # ID của trạng thái thực thể (ví dụ: CUSTOMER-Lead)
    other_data: dict = None # Dữ liệu bổ sung sẽ được đưa vào comments
) -> dict:
    """
    Tạo một khách hàng mới trong NetSuite với dữ liệu được cung cấp.

    Các tham số bắt buộc và các trường phổ biến được tách riêng để rõ ràng và dễ xác thực.
    - subsidiary_id: Internal ID của công ty con (subsidiary).
    - is_person: True nếu là khách hàng cá nhân, False nếu là công ty.
    - company_name: Bắt buộc nếu is_person là False.
    - first_name: Bắt buộc nếu is_person là True.
    - last_name: Bắt buộc nếu is_person là True.
    - email: Địa chỉ email của khách hàng.
    - phone: Số điện thoại chính.
    - alt_phone: Số điện thoại thay thế.
    - fax: Số fax.
    - url: Địa chỉ website.
    - vat_reg_number: Mã số thuế GTGT.
    - comments: Ghi chú ban đầu về khách hàng.
    - currency_id: Internal ID của đơn vị tiền tệ chính của khách hàng.
    - terms_id: Internal ID của điều khoản thanh toán được áp dụng.
    - credit_limit: Hạn mức tín dụng.
    - price_level_id: Internal ID của bảng giá được áp dụng.
    - lead_source_id: Internal ID của nguồn gốc khách hàng tiềm năng.
    - sales_rep_id: Internal ID của nhân viên kinh doanh phụ trách.
    - category_id: Internal ID của danh mục khách hàng.
    - entity_status_id: Internal ID của trạng thái khách hàng.
    - other_data: Dictionary chứa các thông tin bổ sung. Các cặp key-value trong
                  other_data sẽ được định dạng thành chuỗi Markdown và nối vào trường 'comments' của khách hàng.
                  LƯU Ý: other_data sẽ KHÔNG được dùng để tạo/cập nhật các trường riêng biệt khác
                  trên bản ghi khách hàng nữa.
    """
    logger.info("Đang tiến hành tạo một khách hàng mới.")

    customer_data = {}

    # Xác thực subsidiary_id bắt buộc
    if not subsidiary_id:
        logger.error("Subsidiary ID là bắt buộc để tạo khách hàng.")
        return {"error": "Subsidiary ID là bắt buộc"}
    customer_data["subsidiary"] = {"id": subsidiary_id}

    # Đặt trường isperson
    customer_data["isperson"] = is_person

    # Xác thực và đặt các trường cụ thể cho công ty hoặc cá nhân
    if is_person:
        if not first_name or not last_name:
            logger.error("Họ và tên là bắt buộc đối với khách hàng cá nhân.")
            return {"error": "Họ và tên là bắt buộc đối với khách hàng cá nhân"}
        customer_data["firstname"] = first_name
        customer_data["lastname"] = last_name
        if company_name:
            logger.warning("company_name được cung cấp cho khách hàng cá nhân. Thông tin này sẽ không được đặt làm tên công ty chính thức trừ khi được xử lý riêng (ví dụ, đưa vào comments thông qua other_data nếu muốn).")
    else: # Đây là công ty
        if not company_name:
            logger.error("Tên công ty là bắt buộc đối với khách hàng công ty.")
            return {"error": "Tên công ty là bắt buộc đối với khách hàng công ty"}
        customer_data["companyname"] = company_name
        if first_name or last_name:
            logger.warning("first_name hoặc last_name được cung cấp cho khách hàng công ty. Các trường này thường dành cho liên hệ, không phải cho bản ghi công ty.")

    # Thêm các trường tiêu chuẩn nếu được cung cấp
    if email:
        customer_data["email"] = email
    if phone:
        customer_data["phone"] = phone
    if alt_phone:
        customer_data["altphone"] = alt_phone
    if fax:
        customer_data["fax"] = fax
    if url:
        customer_data["url"] = url
    if vat_reg_number:
        customer_data["vatregnumber"] = vat_reg_number
    if credit_limit is not None:
        customer_data["creditlimit"] = credit_limit

    # Các trường tham chiếu (cần Internal ID)
    if currency_id:
        customer_data["currency"] = {"id": currency_id}
    if terms_id:
        customer_data["terms"] = {"id": terms_id}
    if price_level_id:
        customer_data["priceLevel"] = {"id": price_level_id}
    if lead_source_id:
        customer_data["leadSource"] = {"id": lead_source_id}
    if sales_rep_id:
        customer_data["salesRep"] = {"id": sales_rep_id}
    if category_id:
        customer_data["category"] = {"id": category_id}
    if entity_status_id:
        customer_data["entityStatus"] = {"id": entity_status_id}

    # Xử lý comments và other_data
    # comments_param là giá trị từ tham số 'comments'
    # other_data sẽ được nối vào comments_param
    
    final_comments_list = []
    if comments: # Sử dụng tham số comments tường minh nếu có
        final_comments_list.append(comments)

    if other_data:
        if not isinstance(other_data, dict):
            logger.error("other_data phải là một dictionary.")
            return {"error": "Định dạng không hợp lệ cho other_data, phải là một dictionary."}
        
        # Nối các cặp key-value từ other_data vào danh sách comments dưới dạng Markdown
        other_data_md_parts = []
        for key, value in other_data.items():
            # Chuyển đổi giá trị thành chuỗi nếu nó là list hoặc dict để hiển thị tốt hơn
            if isinstance(value, list):
                value_str = ", ".join(map(str, value))
            elif isinstance(value, dict):
                value_str = json.dumps(value, ensure_ascii=False)
            else:
                value_str = str(value)
            other_data_md_parts.append(f"- **{key}:** {value_str}")
        
        if other_data_md_parts:
            # Thêm một dòng tiêu đề nhỏ nếu có dữ liệu từ other_data
            if final_comments_list: # Nếu đã có comments ban đầu, thêm dòng mới trước khi thêm other_data
                 final_comments_list.append("\n**Thông tin bổ sung:**")
            else:
                 final_comments_list.append("**Thông tin bổ sung:**")
            final_comments_list.extend(other_data_md_parts)
            
    if final_comments_list:
        customer_data["comments"] = "\n".join(final_comments_list)
    
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/customer"
        auth = get_netsuite_auth()
        if auth is None:
            logger.error("Xác thực NetSuite (get_netsuite_auth) chưa được cấu hình.")
            return {"error": "Xác thực chưa được cấu hình."}
            
        headers = {"Content-Type": "application/json", "Prefer": "return=representation"}
        payload = json.dumps(customer_data)
        
        logger.debug(f"Payload tạo khách hàng: {payload}")
        response = requests.post(URL, auth=auth, headers=headers, data=payload)
        
        # Log mã trạng thái, header và nội dung phản hồi
        logger.debug(f"NetSuite response status: {response.status_code}")
        logger.debug(f"NetSuite response headers: {dict(response.headers)}")
        logger.debug(f"NetSuite response text: {response.text[:1000] or 'Empty response'}")
        
        # Xử lý mã trạng thái 204 (No Content)
        if response.status_code == 204:
            logger.info("Yêu cầu thành công nhưng không có nội dung trả về (204 No Content).")
            new_customer_url = response.headers.get("Location")
            if new_customer_url:
                try:
                    new_customer_id = new_customer_url.split('/')[-1]
                    logger.info(f"ID khách hàng được trích xuất từ header Location: {new_customer_id}")
                    return {
                        "id": new_customer_id,
                        "message": "Khách hàng được tạo thành công"
                    }
                except IndexError:
                    logger.error(f"Không thể phân tích ID khách hàng từ header Location: {new_customer_url}")
                    return {"error": "Không thể lấy ID khách hàng từ header Location"}
            else:
                logger.error("Header Location không có trong phản hồi 204.")
                return {"error": "Không thể lấy ID khách hàng vì thiếu header Location"}
        
        response.raise_for_status()

        # Kiểm tra nội dung phản hồi
        if not response.text.strip():
            logger.error(f"Phản hồi từ NetSuite rỗng. Status code: {response.status_code}")
            return {
                "error": "Phản hồi từ NetSuite rỗng",
                "status_code": response.status_code,
                "headers": dict(response.headers)
            }
        
        try:
            r_response = response.json()
        except ValueError as json_err:
            logger.error(f"Không thể phân tích phản hồi JSON từ NetSuite: {str(json_err)}")
            return {
                "error": "Phản hồi từ NetSuite không phải JSON hợp lệ",
                "details": str(json_err),
                "response_text": response.text[:1000]
            }

        new_customer_id = r_response.get("id")
        
        if not new_customer_id:
            new_customer_url = response.headers.get("Location")
            if new_customer_url:
                try:
                    new_customer_id = new_customer_url.split('/')[-1]
                    r_response["id"] = new_customer_id
                    logger.info(f"ID khách hàng được trích xuất từ header Location: {new_customer_id}")
                except IndexError:
                    logger.warning(f"Không thể phân tích ID khách hàng từ header Location: {new_customer_url}")
            else:
                logger.warning("Không tìm thấy ID khách hàng trong body phản hồi hoặc header Location.")
        
        logger.info(f"Khách hàng được tạo thành công. ID: {new_customer_id if new_customer_id else 'UNKNOWN'}. Phản hồi: {json.dumps(r_response)[:200]}...")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        error_details_msg = http_err.response.text if http_err.response else "Không có nội dung phản hồi"
        logger.error(f"Lỗi HTTP khi tạo khách hàng: {http_err} - {error_details_msg}")
        
        parsed_netsuite_error = error_details_msg
        try:
            error_json = json.loads(error_details_msg)
            if "o:errorDetails" in error_json and isinstance(error_json["o:errorDetails"], list) and error_json["o:errorDetails"]:
                parsed_netsuite_error = error_json["o:errorDetails"][0].get("detail", parsed_netsuite_error)
                if isinstance(parsed_netsuite_error, dict) and "message" in parsed_netsuite_error:
                     parsed_netsuite_error = parsed_netsuite_error["message"]
            elif "detail" in error_json:
                parsed_netsuite_error = error_json["detail"]
                if isinstance(parsed_netsuite_error, dict) and "message" in parsed_netsuite_error:
                    parsed_netsuite_error = parsed_netsuite_error["message"]
        except (json.JSONDecodeError, IndexError, TypeError, AttributeError):
            logger.debug("Không thể phân tích lỗi chi tiết từ văn bản phản hồi của NetSuite.")
            
        return {
            "error": "Không thể tạo khách hàng",
            "details": str(http_err),
            "status_code": http_err.response.status_code if http_err.response else None,
            "netsuite_error_details": parsed_netsuite_error
        }
    except Exception as e:
        logger.error(f"Lỗi không mong muốn khi tạo khách hàng: {str(e)}", exc_info=True)
        return {"error": "Không thể tạo khách hàng do lỗi không mong muốn", "details": str(e)}

@mcp.tool()
def ns_search_customers(
    global_search_string: str = None,
    entityid_contains: str = None,
    companyname_contains: str = None,
    firstname_contains: str = None,
    lastname_contains: str = None,
    email_contains: str = None,
    phone_contains: str = None,
    subsidiary_id: str = None,
    date_created_from: str = None,
    date_created_to: str = None,
    last_modified_from: str = None,
    last_modified_to: str = None,
    is_inactive: bool = None,
    currency_id: str = None,
    category_id: str = None,
    sales_rep_id: str = None,
    limit: int = 20,
    offset: int = 0,
    order_by_field: str = "dateCreated",
    order_by_direction: str = "DESC"
) -> dict:
    all_params = locals()
    logger.info(f"Searching for customers with parameters: { {k: v for k, v in all_params.items() if v is not None} }")

    where_conditions = []

    def format_like_value(value: str) -> str:
        return f"%{value.replace('%', '%%').replace('_', '__')}%"

    def format_exact_value(value: str) -> str:
        return value.replace("'", "''")

    if global_search_string:
        safe_global_search = format_like_value(global_search_string)
        global_search_conditions = [
            f"LOWER(companyname) LIKE LOWER('{safe_global_search}')",
            f"LOWER(entityid) LIKE LOWER('{safe_global_search}')",
            f"LOWER(email) LIKE LOWER('{safe_global_search}')",
            f"LOWER(phone) LIKE LOWER('{safe_global_search}')",
            f"LOWER(NVL(firstName, ' ')) LIKE LOWER('{safe_global_search}')",
            f"LOWER(NVL(lastName, ' ')) LIKE LOWER('{safe_global_search}')"
        ]
        where_conditions.append(f"({ ' OR '.join(global_search_conditions) })")

    if entityid_contains is not None:
        where_conditions.append(f"LOWER(entityid) LIKE LOWER('{format_like_value(entityid_contains)}')")
    if companyname_contains is not None:
        where_conditions.append(f"LOWER(companyname) LIKE LOWER('{format_like_value(companyname_contains)}')")
    if firstname_contains is not None:
        where_conditions.append(f"LOWER(NVL(firstName, ' ')) LIKE LOWER('{format_like_value(firstname_contains)}')")
    if lastname_contains is not None:
        where_conditions.append(f"LOWER(NVL(lastName, ' ')) LIKE LOWER('{format_like_value(lastname_contains)}')")
    if email_contains is not None:
        where_conditions.append(f"LOWER(email) LIKE LOWER('{format_like_value(email_contains)}')")
    if phone_contains is not None:
        where_conditions.append(f"LOWER(phone) LIKE LOWER('{format_like_value(phone_contains)}')")

    if subsidiary_id is not None:
        try:
            where_conditions.append(f"subsidiary = {int(subsidiary_id)}")
        except ValueError:
            logger.warning(f"Invalid subsidiary_id: {subsidiary_id}. Must be an integer. Skipping filter.")
    if currency_id is not None:
        try:
            where_conditions.append(f"currency = {int(currency_id)}")
        except ValueError:
            logger.warning(f"Invalid currency_id: {currency_id}. Must be an integer. Skipping filter.")
    if category_id is not None:
        try:
            where_conditions.append(f"category = {int(category_id)}")
        except ValueError:
            logger.warning(f"Invalid category_id: {category_id}. Must be an integer. Skipping filter.")
    if sales_rep_id is not None:
        try:
            where_conditions.append(f"salesRep = {int(sales_rep_id)}")
        except ValueError:
            logger.warning(f"Invalid sales_rep_id: {sales_rep_id}. Must be an integer. Skipping filter.")

    date_filter_map = {
        'date_created_from': (date_created_from, 'dateCreated', '>='),
        'date_created_to': (date_created_to, 'dateCreated', '<='),
        'last_modified_from': (last_modified_from, 'lastModifiedDate', '>='),
        'last_modified_to': (last_modified_to, 'lastModifiedDate', '<=')
    }
    for filter_name, (value, field_name, operator) in date_filter_map.items():
        if value:
            try:
                datetime.datetime.strptime(value, '%Y-%m-%d')
                safe_value = format_exact_value(value)
                where_conditions.append(f"{field_name} {operator} TO_DATE('{safe_value}', 'YYYY-MM-DD')")
            except ValueError:
                logger.warning(f"Invalid date format for {filter_name}: {value}. Expected YYYY-MM-DD. Skipping filter.")

    if is_inactive is not None:
        if isinstance(is_inactive, bool):
            where_conditions.append(f"isinactive = '{'T' if is_inactive else 'F'}'")
        else:
            logger.warning(f"Invalid value for is_inactive: {is_inactive}. Expected boolean. Skipping filter.")

    where_clause = ""
    if where_conditions:
        where_clause = "WHERE " + " AND ".join(where_conditions)

    safe_order_by_field = "".join(c if c.isalnum() or c == '_' else '' for c in order_by_field)
    if not safe_order_by_field:
        safe_order_by_field = "dateCreated"

    safe_order_by_direction = "DESC" if order_by_direction.upper() == "DESC" else "ASC"

    query_string = f"""
        SELECT 
            id, 
            entityid, 
            companyname, 
            firstName, 
            lastName, 
            email, 
            phone,
            dateCreated,
            lastModifiedDate,
            isinactive
        FROM 
            customer
        {where_clause}
        ORDER BY
            {safe_order_by_field} {safe_order_by_direction}
    """

    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        if auth is None:
            logger.error("NetSuite authentication (get_netsuite_auth) is not configured.")
            return {"error": "Authentication not configured."}

        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_payload = {"q": query_string}
        logger.error(f"SuiteQL Query for customer search (first 500 chars): {query_string[:500]}...")
        
        payload_json = json.dumps(query_payload)
        response = requests.post(URL, auth=auth, headers=headers, data=payload_json, timeout=30)
        
        logger.error(f"NetSuite response status: {response.status_code}")
        logger.error(f"NetSuite response headers: {dict(response.headers)}")
        logger.error(f"NetSuite response text: {response.text[:1000] or 'No response text'}")
        
        response.raise_for_status()
        
        r_response = response.json()
        logger.info(f"Customer search successful. Parameters: {{ {', '.join(f'{k}={v}' for k, v in all_params.items() if v is not None)} }}. Found {r_response.get('count', 0)} items (considering pagination).")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        error_text = http_err.response.text if http_err.response else "No response text"
        logger.error(f"HTTP error during customer search. Parameters: {{ {', '.join(f'{k}={v}' for k, v in all_params.items() if v is not None)} }}. "
                     f"Error: {http_err}. Status code: {http_err.response.status_code if http_err.response else 'None'}. "
                     f"Headers: {dict(http_err.response.headers) if http_err.response else 'None'}. "
                     f"Response: {error_text[:1000]}")
        return {
            "error": "Failed to search for customers",
            "details": str(http_err),
            "status_code": http_err.response.status_code if http_err.response else None,
            "headers": dict(http_err.response.headers) if http_err.response else None,
            "response": error_text[:1000],
            "query_attempted": query_string
        }
    except Exception as e:
        logger.error(f"Error searching for customers. Parameters: {{ {', '.join(f'{k}={v}' for k, v in all_params.items() if v is not None)} }}. Error: {str(e)}", exc_info=True)
        return {"error": "Failed to search for customers", "details": str(e), "query_attempted": query_string}
        
@mcp.tool()
def ns_get_all_sales_orders_current_year(limit: int = 100) -> json:
    """
    Lấy tất cả các Sales Orders trong năm hiện tại từ NetSuite bằng SuiteQL.
    Trả về nhiều thuộc tính nhất có thể từ bản ghi Transaction.
    Lưu ý: API có giới hạn tối đa 1000 bản ghi cho mỗi yêu cầu.
    """
    current_year = datetime.datetime.now().year
    logger.info(f"Attempting to fetch all sales orders for the current year: {current_year} with limit {limit}")

    if limit > 1000:
        logger.warning(f"Requested limit {limit} exceeds API maximum of 1000. Setting limit to 1000.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit={limit}"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }

        # Truy vấn SuiteQL đã đơn giản hóa thêm: loại bỏ Class, Department (và tạm thời Location)
        ql_query_string = f"""
                SELECT
                    Transaction.ID AS internalId,
                    Transaction.TranID AS documentNumber,
                    Transaction.Type AS recordType,
                    Transaction.Status AS statusInternalId,
                    Transaction.TranDate AS date,
                    Transaction.Entity AS customerInternalId,
                    Transaction.CreatedDate AS dateCreated,
                    Transaction.LastModifiedDate AS dateLastModified,
                    Transaction.Subsidiary AS subsidiaryInternalId,
                    Transaction.Currency AS currencyInternalId,
                    Transaction.ExchangeRate AS exchangeRate,
                    Transaction.Total AS totalAmount,
                    Transaction.TaxTotal AS taxTotalAmount,
                    Transaction.Memo AS memoHeader,
                    Transaction.DueDate AS dueDate,
                    Transaction.LeadSource AS leadSourceId,
                    Transaction.Terms AS termsInternalId
                FROM
                    Transaction
                WHERE
                    Transaction.Type = 'SalesOrd'
                    AND TO_CHAR(Transaction.TranDate, 'YYYY') = '{current_year}'
                ORDER BY
                    Transaction.TranDate DESC, Transaction.ID DESC
            """

        query_payload_dict = {
            "q": ql_query_string
        }

        logger.debug(f"Request URL: {URL}")
        logger.debug(f"SuiteQL Query for current year's sales orders (more simplified): {query_payload_dict['q']}")
        payload_str = json.dumps(query_payload_dict)
        logger.debug(f"Request payload (JSON body): {payload_str}")

        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)

        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched sales orders for year {current_year}. Count: {r_response.get('count', 0)}.")
        if r_response.get('items'):
            logger.debug(f"First item sample: {r_response['items'][0]}")
        else:
            logger.debug("No items returned in the response.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        # ... (phần xử lý lỗi giữ nguyên như cũ) ...
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else "No response text available in HTTPError object"
        error_message = (f"HTTP error occurred while fetching sales orders for year {current_year}: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error.")
        logger.error(error_message)
        return {
            "error": f"Failed to fetch sales orders for year {current_year} due to HTTPError",
            "details": str(http_err),
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response for year {current_year} sales orders: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching sales orders for year {current_year}: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_create_sales_order(customer_id: str, items: list, trandate: str = None, duedate: str = None, currency: str = None, terms: str = None, memo: str = None, location: str = None, department: str = None) -> json:
    """Create a new sales order in NetSuite with the provided data."""
    logger.info(f"Attempting to create a sales order for customer ID: {customer_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/salesOrder"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json", "Prefer": "return=representation"}
        
        # Construct the payload
        payload = {
            "entity": {"id": customer_id},
            "item": [{"item": {"id": item["item_id"]}, "quantity": item["quantity"], "rate": item["rate"]} for item in items]
        }
        
        if trandate:
            payload["trandate"] = trandate
        if duedate:
            payload["duedate"] = duedate
        if currency:
            payload["currency"] = {"id": currency}
        if terms:
            payload["terms"] = {"id": terms}
        if memo:
            payload["memo"] = memo
        if location:
            payload["location"] = {"id": location}
        if department:
            payload["department"] = {"id": department}
        
        payload_str = json.dumps(payload)
        logger.debug(f"Create sales order payload: {payload_str}")
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        
        r_response = json.loads(response.text)
        new_order_id = r_response.get("id")
        
        if not new_order_id:
            new_order_url = response.headers.get("Location")
            if new_order_url:
                new_order_id = new_order_url.split('/')[-1]
                r_response["id"] = new_order_id

        logger.info(f"Sales order created successfully. ID: {new_order_id}. Response: {response.text[:200]}...")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        error_details_msg = http_err.response.text if http_err.response else "No response text"
        logger.error(f"HTTP error creating sales order: {http_err} - {error_details_msg}")
        try:
            error_json = json.loads(error_details_msg)
            error_details_msg = error_json.get("o:errorDetails", [{}])[0].get("detail", error_details_msg)
        except (json.JSONDecodeError, IndexError, AttributeError):
            pass
        return {"error": "Failed to create sales order", "details": str(http_err), "response_code": http_err.response.status_code if http_err.response else None, "netsuite_error": error_details_msg}
    except Exception as e:
        logger.error(f"Error creating sales order: {str(e)}")
        return {"error": "Failed to create sales order", "details": str(e)}

@mcp.tool()
def ns_search_sales_orders(status: str = None, start_date: str = None, end_date: str = None, customer_id: str = None, limit: int = 100) -> json:
    """Search for sales orders in NetSuite using SuiteQL."""
    logger.info(f"Searching for sales orders with status: {status}, date range: {start_date} to {end_date}, customer: {customer_id}, limit: {limit}")
    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit={limit}"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_str = """
            SELECT
                Transaction.ID AS internalId,
                Transaction.TranID AS documentNumber,
                Transaction.Type AS recordType,
                Transaction.Status AS statusInternalId,
                Transaction.TranDate AS date,
                Transaction.Entity AS customerInternalId,
                Transaction.Total AS totalAmount
            FROM
                Transaction
            WHERE
                Transaction.Type = 'SalesOrd'
        """
        
        conditions = []
        if status:
            conditions.append(f"Transaction.Status = '{status}'")
        if start_date:
            conditions.append(f"Transaction.TranDate >= '{start_date}'")
        if end_date:
            conditions.append(f"Transaction.TranDate <= '{end_date}'")
        if customer_id:
            conditions.append(f"Transaction.Entity = {customer_id}")
        
        if conditions:
            query_str += " AND " + " AND ".join(conditions)
        
        query_str += " ORDER BY Transaction.TranDate DESC"
        
        query_payload = {
            "q": query_str
        }
        
        payload_str = json.dumps(query_payload)
        logger.debug(f"SuiteQL Query for sales orders search: {query_str}")
        
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully searched for sales orders. Found {r_response.get('count', 0)} items.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error during sales orders search: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to search for sales orders", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"Error searching for sales orders: {str(e)}")
        return {"error": "Failed to search for sales orders", "details": str(e)}

@mcp.tool()
def ns_close_sales_order(order_id: str) -> json:
    """Close a sales order by setting all line items to closed."""
    logger.info(f"Attempting to close sales order ID: {order_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/salesOrder/{order_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json", "Prefer": "return=representation"}
        
        # First, get the current sales order to know how many line items there are
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        sales_order = json.loads(response.text)
        
        # Check if there are line items
        if 'item' in sales_order and sales_order['item'] and 'items' in sales_order['item']:
            # Create a payload to update each line item
            update_payload = {
                "item": {
                    "items": [
                        {"id": item['id'], "isclosed": "T"} for item in sales_order['item']['items']
                    ]
                }
            }
            # Send PATCH
            payload_str = json.dumps(update_payload)
            logger.debug(f"Update payload for closing sales order {order_id}: {payload_str}")
            patch_response = requests.request("PATCH", URL, auth=auth, headers=headers, data=payload_str)
            patch_response.raise_for_status()
            if patch_response.status_code == 204:
                logger.info(f"Sales order {order_id} closed successfully (204 No content returned).")
                return {"success": True, "id": order_id, "message": "Sales order closed successfully."}
            else:
                r_response = json.loads(patch_response.text)
                logger.info(f"Sales order {order_id} closed successfully. Response: {patch_response.text[:200]}...")
                return r_response
        else:
            logger.warning(f"Sales order {order_id} has no line items to close.")
            return {"warning": "Sales order has no line items to close."}

    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error closing sales order {order_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to close sales order", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"Error closing sales order {order_id}: {str(e)}")
        return {"error": "Failed to close sales order", "details": str(e)}

@mcp.tool()
def ns_get_all_locations(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all locations in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/location?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching locations: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} locations. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample location data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching locations: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch location information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching locations: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching locations: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_all_departments(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all departments in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/department?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching departments: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} departments. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample department data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching departments: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch department information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching departments: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching departments: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_all_currencies(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all currencies in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/currency?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching currencies: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} currencies. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample currency data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching currencies: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch currency information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching currencies: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching currencies: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_all_terms(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all terms in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/terms?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching terms: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} terms. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample term data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching terms: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch terms information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching terms: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching terms: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_all_subsidiaries(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all subsidiaries in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/subsidiary?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching subsidiaries: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} subsidiaries. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample subsidiary data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching subsidiaries: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch subsidiary information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching subsidiaries: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching subsidiaries: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_all_classes(offset: int = 0, limit: int = 100) -> json:
    """Fetch a list of all classes in NetSuite."""
    if limit > 1000:
        logger.warning(f"Requested limit {limit} is high for a single GET request, considering 1000 for this call.")
        limit = 1000
    elif limit <= 0:
        logger.warning(f"Requested limit {limit} is invalid. Setting limit to default 100.")
        limit = 100

    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/classification?limit={limit}&offset={offset}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        
        logger.debug(f"Request URL for fetching classes: {URL}")
        
        response = requests.request("GET", URL, auth=auth, headers=headers)
        
        logger.info(f"Raw response status code: {response.status_code}")
        logger.info(f"Raw response headers: {response.headers}")
        raw_text_snippet = response.text[:500] if response.text else "No raw response text received by requests library"
        logger.info(f"Raw response text snippet: {raw_text_snippet}")

        response.raise_for_status() 
        
        r_response = json.loads(response.text)
        
        items_count = len(r_response.get('items', []))
        logger.info(f"Successfully fetched {items_count} classes. Has more: {r_response.get('hasMore', False)}")
        if items_count > 0:
            logger.debug(f"Sample class data (first item): {r_response['items'][0]}")
            
        return r_response

    except requests.exceptions.HTTPError as http_err:
        response_obj = http_err.response
        response_code = response_obj.status_code if response_obj is not None else "N/A"
        response_text_in_error = response_obj.text if response_obj is not None and response_obj.text else raw_text_snippet
        
        error_message = (f"HTTP error occurred while fetching classes: {http_err} "
                         f"(Status: {response_code}) - Response from HTTPError: '{response_text_in_error}'. "
                         f"Check logs for 'Raw response text snippet' recorded just before this error for potentially more info.")
        logger.error(error_message)
        return {
            "error": "Failed to fetch class information", 
            "details": str(http_err), 
            "response_code": response_code,
            "response_body": response_text_in_error
        }
    except json.JSONDecodeError as json_err:
        response_text_for_json_error = response.text if response else "No response object available"
        logger.error(f"Failed to decode JSON response while fetching classes: {json_err}. Status code was: {response.status_code if response else 'N/A'}. Response text snippet: {response_text_for_json_error[:500]}")
        return {
            "error": "Failed to decode JSON response from NetSuite",
            "details": str(json_err),
            "response_code": response.status_code if response else None,
            "response_text": response_text_for_json_error
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching classes: {e}", exc_info=True)
        return {"error": "An unexpected error occurred", "details": str(e)}

@mcp.tool()
def ns_get_location_by_id(location_id: str) -> json:
    """Fetch specific location information from NetSuite by ID."""
    logger.info(f"Fetching location information for ID: {location_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/location/{location_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched location ID: {location_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching location {location_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch location information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching location {location_id}: {e}")
        return {"error": "Failed to fetch location information", "details": str(e)}

@mcp.tool()
def ns_get_department_by_id(department_id: str) -> json:
    """Fetch specific department information from NetSuite by ID."""
    logger.info(f"Fetching department information for ID: {department_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/department/{department_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched department ID: {department_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching department {department_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch department information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching department {department_id}: {e}")
        return {"error": "Failed to fetch department information", "details": str(e)}

@mcp.tool()
def ns_get_currency_by_id(currency_id: str) -> json:
    """Fetch specific currency information from NetSuite by ID."""
    logger.info(f"Fetching currency information for ID: {currency_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/currency/{currency_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched currency ID: {currency_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching currency {currency_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch currency information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching currency {currency_id}: {e}")
        return {"error": "Failed to fetch currency information", "details": str(e)}

@mcp.tool()
def ns_get_terms_by_id(terms_id: str) -> json:
    """Fetch specific terms information from NetSuite by ID."""
    logger.info(f"Fetching terms information for ID: {terms_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/terms/{terms_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched terms ID: {terms_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching terms {terms_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch terms information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching terms {terms_id}: {e}")
        return {"error": "Failed to fetch terms information", "details": str(e)}

@mcp.tool()
def ns_get_subsidiary_by_id(subsidiary_id: str) -> json:
    """Fetch specific subsidiary information from NetSuite by ID."""
    logger.info(f"Fetching subsidiary information for ID: {subsidiary_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/subsidiary/{subsidiary_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched subsidiary ID: {subsidiary_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching subsidiary {subsidiary_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch subsidiary information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching subsidiary {subsidiary_id}: {e}")
        return {"error": "Failed to fetch subsidiary information", "details": str(e)}

@mcp.tool()
def ns_get_class_by_id(class_id: str) -> json:
    """Fetch specific class information from NetSuite by ID."""
    logger.info(f"Fetching class information for ID: {class_id}")
    try:
        URL = f"{NETSUITE_BASE_URL}/record/v1/classification/{class_id}"
        auth = get_netsuite_auth()
        headers = {"Content-Type": "application/json"}
        response = requests.request("GET", URL, auth=auth, headers=headers)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully fetched class ID: {class_id}")
        return r_response
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred while fetching class {class_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch class information", "details": str(http_err), "response": http_err.response.text if http_err.response else "No response"}
    except Exception as e:
        logger.error(f"An error occurred while fetching class {class_id}: {e}")
        return {"error": "Failed to fetch class information", "details": str(e)}

import json
import requests
import logging # Assuming logger is configured elsewhere

# --- Start of the modified function and its helper ---
def _get_current_sales_order_status(order_id: str, auth) -> dict:
    """
    Helper function to get the current status of a sales order.
    Returns a dictionary with 'current_status' or 'error'.
    """
    if not order_id:
        return {"error": "Order ID is required to fetch current status."}
    
    try:
        url = f"{NETSUITE_BASE_URL}/record/v1/salesOrder/{order_id}?fields=orderStatus"
        headers = {"Prefer": "return=representation"} # Not strictly needed for GET fields, but good practice
        
        logger.debug(f"Fetching current status for sales order {order_id}...")
        response = requests.get(url, auth=auth, headers=headers)
        response.raise_for_status()
        
        order_details = response.json()
        current_status = order_details.get("orderStatus")
        
        if not current_status:
            logger.warning(f"orderStatus field not found or is null for order {order_id}. Response: {order_details}")
            # Depending on NetSuite's response for a missing field, you might get None or it might be absent.
            # If the field can be legitimately empty and that's a valid state, this logic might need adjustment.
            return {"error": f"orderStatus field not found or is null for order {order_id}."}
            
        logger.info(f"Current status for sales order {order_id} is '{current_status}'.")
        return {"current_status": current_status}
        
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error fetching status for sales order {order_id}: {http_err} - {http_err.response.text if http_err.response else 'No response text'}")
        return {"error": "Failed to fetch sales order status", "details": str(http_err)}
    except Exception as e:
        logger.error(f"Error fetching status for sales order {order_id}: {str(e)}")
        return {"error": "Failed to fetch sales order status", "details": str(e)}

@mcp.tool() # Uncomment if using a specific tool decorator
def ns_update_sales_order(order_id: str, order_data: dict = None, workflow_action: str = None) -> dict:
    """
    Update a sales order in NetSuite with the provided data or workflow action.
    - If `workflow_action` is provided, it validates the current order status and then sets the `orderStatus` field accordingly.
    - If `order_data` is provided directly, it can include any fields, including `orderStatus` (bypassing workflow validation).
    - Workflow actions and their intended transitions (NetSuite status codes):
      - "close": Set status to "F" (User-defined "Closed" or "Pending Billing")
      - "mark_finished": Set status to "F" (User-defined "Closed" or "Pending Billing")
      - "reopen": Set status to "B" (Pending Fulfillment)
    """
    
    # Define workflow action to status code mapping and allowed previous statuses
    # Note: 'F' is typically "Pending Billing". 'H' is "Closed". Adjust if 'F' has a different meaning in your setup.
    allowed_transitions = {
        "close": {
            "target_status": "F",
            "allowed_from": ["B", "D", "E", "G"]  # e.g., Pending Fulfillment, Partially Fulfilled, Pending Billing/Partially Fulfilled, Billed
        },
        "mark_finished": {
            "target_status": "F",
            "allowed_from": ["B", "D", "E", "G"]  # Same as "close"
        },
        "reopen": {
            "target_status": "B",
            "allowed_from": ["F", "H", "C"]  # e.g., From your "F" state, standard Closed, Cancelled
        }
    }

    auth = get_netsuite_auth() # Get authentication credentials

    # If workflow_action is provided, validate and set orderStatus in order_data
    if workflow_action:
        if workflow_action not in allowed_transitions:
            logger.error(f"Invalid workflow_action: {workflow_action}")
            return {"error": "Invalid workflow_action", "details": f"Action '{workflow_action}' is not defined."}

        transition_rule = allowed_transitions[workflow_action]
        target_status = transition_rule["target_status"]
        
        # Get current order status
        status_check_result = _get_current_sales_order_status(order_id, auth)
        
        if "error" in status_check_result:
            logger.error(f"Could not perform workflow action '{workflow_action}' for order {order_id} due to: {status_check_result['error']}")
            return status_check_result # Return the error from status check

        current_status = status_check_result["current_status"]

        if current_status not in transition_rule["allowed_from"]:
            logger.error(
                f"Workflow action '{workflow_action}' to target status '{target_status}' is not allowed "
                f"from current status '{current_status}' for order {order_id}. "
                f"Allowed from: {transition_rule['allowed_from']}"
            )
            return {
                "error": "Invalid state transition",
                "details": f"Action '{workflow_action}' not allowed from current status '{current_status}'. "
                           f"Must be one of: {transition_rule['allowed_from']}."
            }
        
        # If validation passes, prepare order_data
        if order_data is None:
            order_data = {}
        order_data["orderStatus"] = target_status
        logger.info(f"Workflow action '{workflow_action}': Setting orderStatus to '{target_status}' for order {order_id} from '{current_status}'.")

    # Validate order_id (essential for the PATCH URL)
    if not order_id:
        logger.error("Order ID is required for update operation.")
        return {"error": "Order ID is required"}
        
    # Ensure order_data is not None if we didn't go through workflow_action block
    if order_data is None:
        logger.warning(f"No order_data and no workflow_action provided for order {order_id}. No update will be performed.")
        return {"error": "No update data or workflow action provided."}


    # Proceed with the update
    try:
        url = f"{NETSUITE_BASE_URL}/record/v1/salesOrder/{order_id}"
        headers = {"Content-Type": "application/json", "Prefer": "return=representation"}
        
        payload = json.dumps(order_data)
        logger.debug(f"Update payload for sales order {order_id}: {payload}")
        
        response = requests.patch(url, auth=auth, headers=headers, data=payload)
        response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
        
        # NetSuite returns 204 No Content for successful PATCH if "Prefer: return=minimal"
        # or if there's truly no content to return. With "return=representation", it should be 200 OK.
        if response.status_code == 204:
            logger.info(f"Sales order {order_id} updated successfully (204 No Content returned, implies success).")
            # Fetch the record again if representation is needed but not returned by 204
            return {"success": True, "id": order_id, "message": "Sales order updated successfully (204)."}
        
        r_response = response.json()
        logger.info(f"Sales order {order_id} updated successfully. Status: {response.status_code}. Response: {str(r_response)[:200]}...")
        return r_response # This is usually the updated record representation
    
    except requests.exceptions.HTTPError as http_err:
        error_details = http_err.response.text if http_err.response else "No response text"
        try:
            # Try to parse NetSuite's error JSON
            ns_error = json.loads(error_details)
            error_message = ns_error.get("detail", {}).get("message", str(http_err))
            o_error = ns_error.get("o:errorDetails", [{}])[0].get("detail", error_details)
            if isinstance(o_error, dict): # sometimes o:errorDetails.detail is a dict
                o_error = o_error.get("message", json.dumps(o_error))

        except json.JSONDecodeError:
            error_message = str(http_err)
            o_error = error_details

        logger.error(f"HTTP error updating sales order {order_id}: {error_message} - {o_error}")
        return {"error": f"Failed to update sales order: {error_message}", "details": o_error, "status_code": http_err.response.status_code if http_err.response else None}
    
    except Exception as e:
        logger.error(f"General error updating sales order {order_id}: {str(e)}")
        return {"error": "Failed to update sales order", "details": str(e)}

@mcp.tool()
def ns_search_work_orders(
    status: str = None,
    start_date: str = None,
    end_date: str = None,
    limit: int = 100
) -> dict:
    """Search for work orders in NetSuite using SuiteQL."""
    logger.info(f"Searching for work orders with status: {status}, date range: {start_date} to {end_date}, limit: {limit}")
    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit={limit}"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_str = """
            SELECT
                Transaction.ID AS internalId,
                Transaction.TranID AS documentNumber,
                Transaction.Type AS recordType,
                Transaction.Status AS statusInternalId,
                BUILTIN.DF(Transaction.Status) AS statusName,
                Transaction.TranDate AS date,
                Transaction.Entity AS entityId,
                BUILTIN.DF(Transaction.Entity) AS entityName,
                Transaction.Subsidiary AS subsidiaryId,
                BUILTIN.DF(Transaction.Subsidiary) AS subsidiaryName,
                Transaction.Location AS locationId,
                BUILTIN.DF(Transaction.Location) AS locationName,
                Transaction.Currency AS currencyId,
                BUILTIN.DF(Transaction.Currency) AS currencyName,
                Transaction.Memo AS memo,
                Transaction.CreatedDate AS createdDate,
                Transaction.LastModifiedDate AS lastModifiedDate,
                Transaction.PostingPeriod AS postingPeriodId,
                BUILTIN.DF(Transaction.PostingPeriod) AS postingPeriodName,
                Transaction.Employee AS employeeId,
                BUILTIN.DF(Transaction.Employee) AS employeeName
                -- Note: Work Order-specific fields (e.g., startDate, endDate, quantity) may be custom (e.g., custbody_start_date).
                -- Validate in SuiteQL Query Tool before adding, e.g.:
                -- Transaction.custbody_start_date AS startDate,
                -- Transaction.custbody_end_date AS endDate,
                -- Transaction.custbody_actual_production_start AS actualProductionStartDate,
                -- Transaction.custbody_actual_production_end AS actualProductionEndDate
            FROM
                Transaction
            WHERE
                Transaction.Type = 'WorkOrd'
        """
        
        conditions = []
        if status:
            conditions.append(f"Transaction.Status = '{status}'")
        if start_date:
            conditions.append(f"Transaction.TranDate >= '{start_date}'")
        if end_date:
            conditions.append(f"Transaction.TranDate <= '{end_date}'")
        
        if conditions:
            query_str += " AND " + " AND ".join(conditions)
        
        query_str += " ORDER BY Transaction.TranDate DESC"
        
        query_payload = {
            "q": query_str
        }
        
        payload_str = json.dumps(query_payload)
        logger.debug(f"SuiteQL Query for work orders search: {query_str}")
        
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        r_response = json.loads(response.text)
        logger.info(f"Successfully searched for work orders. Found {r_response.get('count', 0)} items.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        status_code = http_err.response.status_code if http_err.response else None
        response_text = http_err.response.text if http_err.response else "No response text"
        response_headers = dict(http_err.response.headers) if http_err.response else {}
        parsed_error_json = None
        
        try:
            if http_err.response and http_err.response.text:
                parsed_error_json = json.loads(http_err.response.text)
        except json.JSONDecodeError:
            logger.warning("Failed to parse error response as JSON.")
        
        logger.error(
            f"HTTP error during work orders search: {http_err}\n"
            f"Status Code: {status_code}\n"
            f"Response Text: {response.text}\n"
            f"Response Headers: {response_headers}\n"
            f"Parsed Error JSON: {parsed_error_json}\n"
            f"Full Query: {query_str}"
        )
        return {
            "error": "Failed to search for work orders",
            "details": str(http_err),
            "status_code": status_code,
            "response_text": response.text,
            "response_headers": response_headers,
            "parsed_error_json": parsed_error_json,
            "query": query_str
        }
    except Exception as e:
        logger.error(
            f"Unexpected error during work orders search: {str(e)}\n"
            f"Full Query: {query_str}",
            exc_info=True
        )
        return {
            "error": "Failed to search for work orders",
            "details": str(e),
            "status_code": None,
            "response_text": None,
            "response_headers": None,
            "parsed_error_json": None,
            "query": query_str
        }

@mcp.tool()
def ns_get_work_order_details(
    work_order_id: str
) -> dict:
    """Retrieve detailed information for a single Work Order in NetSuite using SuiteQL, including item and planning details."""
    logger.info(f"Retrieving details for Work Order ID: {work_order_id}")
    
    # Validate work_order_id
    try:
        int(work_order_id)
    except ValueError:
        logger.error(f"Invalid work_order_id: {work_order_id}. Must be a valid integer.")
        return {
            "error": "Invalid work_order_id",
            "details": f"Work Order ID '{work_order_id}' must be a valid integer.",
            "status_code": None,
            "response_text": None,
            "response_headers": None,
            "parsed_error_json": None,
            "query": None
        }
    
    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit=1"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_str = """
            SELECT
                Transaction.ID AS internalId,
                Transaction.TranID AS documentNumber,
                Transaction.Type AS recordType,
                Transaction.Status AS statusInternalId,
                BUILTIN.DF(Transaction.Status) AS statusName,
                Transaction.TranDate AS date,
                Transaction.TranDate AS plannedStartDate,
                Transaction.Entity AS entityId,
                BUILTIN.DF(Transaction.Entity) AS entityName,
                Transaction.Subsidiary AS subsidiaryId,
                BUILTIN.DF(Transaction.Subsidiary) AS subsidiaryName,
                Transaction.Location AS locationId,
                BUILTIN.DF(Transaction.Location) AS locationName,
                Transaction.Currency AS currencyId,
                BUILTIN.DF(Transaction.Currency) AS currencyName,
                Transaction.Memo AS memo,
                Transaction.CreatedDate AS createdDate,
                Transaction.LastModifiedDate AS lastModifiedDate,
                Transaction.LastModifiedDate AS actualEndDate,
                Transaction.PostingPeriod AS postingPeriodId,
                BUILTIN.DF(Transaction.PostingPeriod) AS postingPeriodName,
                Transaction.Employee AS employeeId,
                BUILTIN.DF(Transaction.Employee) AS employeeName,
                WOLine.Item AS itemId,
                BUILTIN.DF(WOLine.Item) AS itemName,
                WOLine.Quantity AS quantity,
                WOLine.createdFrom AS salesOrderId
                -- Note: Work Order-specific planning fields (e.g., startDate, endDate) are not available in Transaction without custom fields.
                -- Using TranDate as plannedStartDate and LastModifiedDate as actualEndDate as proxies.
                -- Validate WorkOrderCompletion.createdDate for a precise completion date, e.g.:
                -- LEFT JOIN WorkOrderCompletion ON WorkOrderCompletion.workOrder = Transaction.ID
                -- WorkOrderCompletion.createdDate AS completionDate
            FROM
                Transaction
                LEFT JOIN TransactionLine AS WOLine ON WOLine.Transaction = Transaction.ID
            WHERE
                Transaction.Type = 'WorkOrd'
                AND Transaction.ID = :work_order_id
        """
        
        # Replace :work_order_id with the validated integer to prevent SQL injection
        query_str = query_str.replace(":work_order_id", str(int(work_order_id)))
        
        query_payload = {
            "q": query_str
        }
        
        payload_str = json.dumps(query_payload)
        logger.debug(f"SuiteQL Query for work order details: {query_str}")
        
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        r_response = json.loads(response.text)
        count = r_response.get('count', 0)
        logger.info(f"Successfully retrieved Work Order details. Found {count} items.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        status_code = getattr(http_err.response, 'status_code', None)
        response_text = getattr(http_err.response, 'text', "No response text")
        response_headers = dict(http_err.response.headers) if hasattr(http_err.response, 'headers') else {}
        parsed_error_json = None
        
        try:
            if response_text:
                parsed_error_json = json.loads(response_text)
        except json.JSONDecodeError:
            logger.warning("Failed to parse error response as JSON.")
        
        logger.error(
            f"HTTP error during work order details retrieval\n"
            f"Exception Message: {str(http_err)}\n"
            f"Response Text: {response.text}\n"
            f"Status Code: {status_code}\n"
            f"Response Headers: {response_headers}\n"
            f"Parsed Error JSON: {parsed_error_json}\n"
            f"Full Query: {query_str}"
        )
        return {
            "error": "Failed to retrieve work order details",
            "details": str(http_err),
            "status_code": status_code,
            "response_text": response_text,
            "response_headers": response_headers,
            "parsed_error_json": parsed_error_json,
            "query": query_str
        }
    except Exception as e:
        response_text = "No response text (non-HTTP exception)"
        logger.error(
            f"Unexpected error during work order details retrieval\n"
            f"Exception Message: {str(e)}\n"
            f"Response Text: {response.text}\n"
            f"Full Query: {query_str}",
            exc_info=True
        )
        return {
            "error": "Failed to retrieve work order details",
            "details": str(e),
            "status_code": None,
            "response_text": response_text,
            "response_headers": None,
            "parsed_error_json": None,
            "query": query_str
        }
        
@mcp.tool()
def ns_get_work_order_briefs(
    work_order_id: str
) -> dict:
    """Retrieve detailed information for a single Work Order in NetSuite using SuiteQL."""
    logger.info(f"Retrieving details for Work Order ID: {work_order_id}")
    
    # Validate work_order_id
    try:
        int(work_order_id)
    except ValueError:
        logger.error(f"Invalid work_order_id: {work_order_id}. Must be a valid integer.")
        return {
            "error": "Invalid work_order_id",
            "details": f"Work Order ID '{work_order_id}' must be a valid integer.",
            "status_code": None,
            "response_text": None,
            "response_headers": None,
            "parsed_error_json": None,
            "query": None
        }
    
    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit=1"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_str = """
            SELECT
                Transaction.ID AS internalId,
                Transaction.TranID AS documentNumber,
                Transaction.Type AS recordType,
                Transaction.Status AS statusInternalId,
                BUILTIN.DF(Transaction.Status) AS statusName,
                Transaction.TranDate AS date,
                Transaction.Entity AS entityId,
                BUILTIN.DF(Transaction.Entity) AS entityName,
                Transaction.Subsidiary AS subsidiaryId,
                BUILTIN.DF(Transaction.Subsidiary) AS subsidiaryName,
                Transaction.Location AS locationId,
                BUILTIN.DF(Transaction.Location) AS locationName,
                Transaction.Currency AS currencyId,
                BUILTIN.DF(Transaction.Currency) AS currencyName,
                Transaction.Memo AS memo,
                Transaction.CreatedDate AS createdDate,
                Transaction.LastModifiedDate AS lastModifiedDate,
                Transaction.PostingPeriod AS postingPeriodId,
                BUILTIN.DF(Transaction.PostingPeriod) AS postingPeriodName,
                Transaction.Employee AS employeeId,
                BUILTIN.DF(Transaction.Employee) AS employeeName
                -- Note: Work Order-specific fields (e.g., startDate, endDate, quantity) may be custom (e.g., custbody_start_date).
                -- Validate in SuiteQL Query Tool before adding, e.g.:
                -- Transaction.custbody_start_date AS startDate,
                -- Transaction.custbody_end_date AS endDate,
                -- Transaction.custbody_actual_production_start AS actualProductionStartDate,
                -- Transaction.custbody_actual_production_end AS actualProductionEndDate
            FROM
                Transaction
            WHERE
                Transaction.Type = 'WorkOrd'
                AND Transaction.ID = :work_order_id
        """
        
        # Replace :work_order_id with the validated integer to prevent SQL injection
        query_str = query_str.replace(":work_order_id", str(int(work_order_id)))
        
        query_payload = {
            "q": query_str
        }
        
        payload_str = json.dumps(query_payload)
        logger.debug(f"SuiteQL Query for work order details: {query_str}")
        
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        r_response = json.loads(response.text)
        count = r_response.get('count', 0)
        logger.info(f"Successfully retrieved Work Order details. Found {count} items.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        status_code = http_err.response.status_code if http_err.response else None
        response_text = http_err.response.text if http_err.response else "No response text"
        response_headers = dict(http_err.response.headers) if http_err.response else {}
        parsed_error_json = None
        
        try:
            if http_err.response and http_err.response.text:
                parsed_error_json = json.loads(http_err.response.text)
        except json.JSONDecodeError:
            logger.warning("Failed to parse error response as JSON.")
        
        logger.error(
            f"HTTP error during work order details retrieval\n"
            f"Exception Message: {str(http_err)}\n"
            f"Response Text: {response.text}\n"
            f"Status Code: {status_code}\n"
            f"Response Headers: {response_headers}\n"
            f"Parsed Error JSON: {parsed_error_json}\n"
            f"Full Query: {query_str}"
        )
        return {
            "error": "Failed to retrieve work order details",
            "details": str(http_err),
            "status_code": status_code,
            "response_text": response.text
        }
    except Exception as e:
        response_text = "No response text (non-HTTP exception)"
        logger.error(
            f"Unexpected error during work order details retrieval\n"
            f"Exception Message: {str(e)}\n"
            f"Response Text: {response.text}\n"
            f"Full Query: {query_str}",
            exc_info=True
        )
        return {
            "error": "Failed to retrieve work order details",
            "details": str(e),
            "status_code": None,
            "response_text": response_text
        }

@mcp.tool()
def ns_get_work_orders_by_sales_order(
    sales_order_id: str,
    limit: int = 100
) -> dict:
    """Retrieve Work Orders related to a Sales Order in NetSuite using SuiteQL."""
    logger.info(f"Retrieving Work Orders for Sales Order ID: {sales_order_id}, limit: {limit}")
    
    # Validate sales_order_id
    try:
        int(sales_order_id)
    except ValueError:
        logger.error(f"Invalid sales_order_id: {sales_order_id}. Must be a valid integer.")
        return {
            "error": "Invalid sales_order_id",
            "details": f"Sales Order ID '{sales_order_id}' must be a valid integer.",
            "status_code": None,
            "response_text": None,
            "response_headers": None,
            "parsed_error_json": None,
            "query": None
        }
    
    try:
        URL = f"{NETSUITE_BASE_URL}/query/v1/suiteql?limit={limit}"
        auth = get_netsuite_auth()
        headers = {
            "Content-Type": "application/json",
            "Prefer": "transient",
        }
        
        query_str = """
            SELECT
                Transaction.ID AS internalId,
                Transaction.TranID AS documentNumber,
                Transaction.Type AS recordType,
                Transaction.Status AS statusInternalId,
                BUILTIN.DF(Transaction.Status) AS statusName,
                Transaction.TranDate AS date,
                Transaction.Entity AS entityId,
                BUILTIN.DF(Transaction.Entity) AS entityName,
                Transaction.Subsidiary AS subsidiaryId,
                BUILTIN.DF(Transaction.Subsidiary) AS subsidiaryName,
                Transaction.Location AS locationId,
                BUILTIN.DF(Transaction.Location) AS locationName,
                Transaction.Currency AS currencyId,
                BUILTIN.DF(Transaction.Currency) AS currencyName,
                Transaction.Memo AS memo,
                Transaction.CreatedDate AS createdDate,
                Transaction.LastModifiedDate AS lastModifiedDate,
                Transaction.PostingPeriod AS postingPeriodId,
                BUILTIN.DF(Transaction.PostingPeriod) AS postingPeriodName,
                Transaction.Employee AS employeeId,
                BUILTIN.DF(Transaction.Employee) AS employeeName
                -- Note: Work Order-specific fields (e.g., startDate, endDate, quantity) may be custom (e.g., custbody_start_date).
                -- Validate in SuiteQL Query Tool before adding, e.g.:
                -- Transaction.custbody_start_date AS startDate,
                -- Transaction.custbody_end_date AS endDate,
                -- Transaction.custbody_actual_production_start AS actualProductionStartDate,
                -- Transaction.custbody_actual_production_end AS actualProductionEndDate
            FROM
                Transaction
                INNER JOIN TransactionLine AS WOLine ON WOLine.Transaction = Transaction.ID
            WHERE
                Transaction.Type = 'WorkOrd'
                AND WOLine.createdFrom = :sales_order_id
        """
        
        # Replace :sales_order_id with the validated integer to prevent SQL injection
        query_str = query_str.replace(":sales_order_id", str(int(sales_order_id)))
        
        query_payload = {
            "q": query_str
        }
        
        payload_str = json.dumps(query_payload)
        logger.debug(f"SuiteQL Query for work orders by sales order: {query_str}")
        
        response = requests.request("POST", URL, auth=auth, headers=headers, data=payload_str)
        response.raise_for_status()
        r_response = json.loads(response.text)
        count = r_response.get('count', 0)
        logger.info(f"Successfully retrieved Work Orders for Sales Order ID {sales_order_id}. Found {count} items.")
        return r_response

    except requests.exceptions.HTTPError as http_err:
        status_code = http_err.response.status_code if http_err.response else None
        response_text = http_err.response.text if http_err.response else "No response text"
        response_headers = dict(http_err.response.headers) if http_err.response else {}
        parsed_error_json = None
        
        try:
            if http_err.response and http_err.response.text:
                parsed_error_json = json.loads(http_err.response.text)
        except json.JSONDecodeError:
            logger.warning("Failed to parse error response as JSON.")
        
        logger.error(
            f"HTTP error during work orders retrieval for sales order\n"
            f"Exception Message: {str(http_err)}\n"
            f"Response Text: {response_text}\n"
            f"Status Code: {status_code}\n"
            f"Response Headers: {response_headers}\n"
            f"Parsed Error JSON: {parsed_error_json}\n"
            f"Full Query: {query_str}"
        )
        return {
            "error": "Failed to retrieve work orders for sales order",
            "details": str(http_err),
            "status_code": status_code,
            "response_text": response_text,
            "response_headers": response_headers,
            "parsed_error_json": parsed_error_json,
            "query": query_str
        }
    except Exception as e:
        response_text = "No response text (non-HTTP exception)"
        logger.error(
            f"Unexpected error during work orders retrieval for sales order\n"
            f"Exception Message: {str(e)}\n"
            f"Response Text: {response_text}\n"
            f"Full Query: {query_str}",
            exc_info=True
        )
        return {
            "error": "Failed to retrieve work orders for sales order",
            "details": str(e),
            "status_code": None,
            "response_text": response_text,
            "response_headers": None,
            "parsed_error_json": None,
            "query": query_str
        }
        
if __name__ == "__main__":
    host = "0.0.0.0"
    port = 8002 
    
    print(f"Attempting to start MCP server with SSE transport on {host}:{port}...")
    logger.info(f"Starting MCP server with SSE transport on {host}:{port}...")
    try:
        mcp.run(transport='sse', host=host, port=port) 
        logger.info(f"MCP server with SSE transport reportedly started.")
    except TypeError as te: 
        logger.warning(f"Failed to start server with host/port parameters for SSE transport: {te}. Trying without them.")
        try:
            mcp.run(transport='sse')
            logger.info(f"MCP server with SSE transport reportedly started (without host/port params in run()).")
        except Exception as e_inner:
            logger.error(f"Failed to start server with transport='sse' (fallback): {e_inner}")
            print(f"Failed to start server with transport='sse' (fallback): {e_inner}")
    except Exception as e:
        logger.error(f"Failed to start server with transport='sse': {e}")
        print(f"Failed to start server with transport='sse': {e}")