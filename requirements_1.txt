Commits on Jul 31, 2025
improve session state checks for histogram and plot data
workaround calculate alert future vnindex
Commits on Jul 30, 2025
hot fix email report
Commits on Jul 29, 2025
fix: update variable names to use hyphen instead of underscore for downside metrics, add beta and deviation
Merge remote-tracking branch 'origin/hainguyen' into hainguyen
hot fix email report
hot fix email report
feat: update filter.json to enhance volume-based calculations and add new bear divergence metrics for VNINDEX
Commits on Jul 28, 2025
feat: implement Mailjet email service and refactor EmailServices class
feat: replace gcs_service uploads with celery_utils for data synchronization
feat: update risk rating calculation to use binned downside metrics
Commits on Jul 26, 2025
add html_builder
feat: enhance VNINDEX RSI metrics by adding Volume data and updating calculations
Commits on Jul 23, 2025
update risk rating
feat: update time simulation logic and adjust dataframe filtering for win/loss calculations
Commits on Jul 21, 2025
feat: refactor Simulation class to accept df_deals parameter and update preprocessing method
Commits on Jul 20, 2025
feat: add Volume metrics to RSI calculations in dictionary and tdlib
feat: add MACDdiff metrics to RSI calculations in dictionary and tdlib
Commits on Jul 18, 2025
feat: comment out unused VNINDEX and VN30 metrics in dictionary and tdlib
add Volume_1Y_P50
feat: enhance risk indicator processing with mask handling and task retry improvements
Commits on Jul 17, 2025
hot fix shortsell webui
feat: update CMF function parameter from 21 to 20 for consistency in calculations
feat: refactor CONSTRAIN
Commits on Jul 16, 2025
feat: add return statements to data processing tasks for improved flow control
feat: update stock data retrieval and processing for improved accuracy and caching for calculating index
feat: add caching for risk indicators data loading and include CMF calculations
feat: add compute_additional_indicators function for enhanced risk rating calculations
Commits on Jul 15, 2025
feat: refactor risk indicators processing to use shared_task and improve error handling
fix: refactor data loading and processing for market indicators in data_tasks and schedule_tasks
feat: add risk indicators processing and percentile binning for downside metrics
Commits on Jul 14, 2025
fix: update script configurations to activate Python environment and adjust tmux commands
Commits on Jul 12, 2025
fix: enhance null_tickers method to safely drop ignored indicators from DataFrame
Commits on Jul 10, 2025
fix: update financial report processing to handle release dates and ensure year report updates
Commits on Jul 9, 2025
ignore some indicator about market in monitorservice
fix: improve VAP_v1 function to handle edge cases in price_step calculation and ensure safe index access
Commits on Jul 8, 2025
fix: update Debt/Equity key and improve rolling mean calculations
hot fix indicators
feat: add additional financial indicators for previous quarters in dictionary and falib modules
feat: add selected_quarter_v1 function to enhance financial data processing
Commits on Jul 7, 2025
feat: hot fix calculate EVEB
Commits on Jul 4, 2025
init breakdown services



