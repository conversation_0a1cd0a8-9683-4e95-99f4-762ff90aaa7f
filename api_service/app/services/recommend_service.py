from datetime import date, datetime
from typing import List, Optional, Union
from app.db.mongodb import get_database
from app.models.recommend_model import StockRecommend, ReturnStockRecommend
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class StockRecommendService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_by_date(
            self,
            target_date: date,
            signal_type: Optional[str] = None,
    ) -> List[ReturnStockRecommend]:
        """
        Get list of recommendation by date
        """
        query = {"hit_date": target_date.isoformat()}

        if signal_type:
            query["signal"] = signal_type

        cursor = self.database.recommends.find(query)
        stocks = await cursor.to_list(length=None)

        return [ReturnStockRecommend(**stock) for stock in stocks]

    async def get_by_code(
            self,
            stock_code: str,
            target_date: Optional[date] = None
    ) -> List[StockRecommend]:
        """
        Get list of recommendation by ticker
        """
        query = {"ticker": stock_code}
        if target_date:
            query["hit_date"] = target_date.isoformat()

        cursor = self.database.recommends.find(query).sort("score", -1)
        stocks = await cursor.to_list()
        return [StockRecommend(**stock) for stock in stocks]

    async def insert_db(
            self,
            recommend_data: Union[StockRecommend, List[StockRecommend]]
    ) -> bool:
        """
        Insert một hoặc nhiều dữ liệu cổ phiếu vào database
        """
        try:
            if isinstance(recommend_data, list):
                await self.database.recommends.insert_many([stock.model_dump() for stock in recommend_data])
                logger.info(f"Successfully inserted {len(recommend_data)} stock data records")
            else:
                await self.database.recommends.insert_one(recommend_data.model_dump())
                logger.info(
                    f"Successfully inserted ticker data for {recommend_data.ticker} on {recommend_data.hit_date}")
            return True
        except Exception as e:
            logger.error(f"Error inserting stock data: {str(e)}")
            return False
