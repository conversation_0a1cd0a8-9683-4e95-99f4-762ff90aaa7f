from datetime import date, datetime
from typing import List, Optional, Union
from app.db.mongodb import get_database
from app.models.recommend_model import StockRecommend, ReturnStockRecommend
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class StockRecommendService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_by_date(
            self,
            target_date: date,
            signal_type: Optional[str] = None,
    ) -> List[ReturnStockRecommend]:
        """
        Get list of recommendation by date
        """
        logger.debug(f"Starting get_by_date operation for date: {target_date}, signal_type: {signal_type}")
        try:
            query = {"hit_date": target_date.isoformat()}

            if signal_type:
                query["signal"] = signal_type
                logger.debug(f"Added signal filter to query: {signal_type}")

            logger.debug(f"Querying database with: {query}")
            cursor = self.database.recommends.find(query)
            stocks = await cursor.to_list(length=None)

            logger.debug(f"Found {len(stocks)} recommendations for date {target_date}")
            result = [ReturnStockRecommend(**stock) for stock in stocks]
            logger.info(f"Successfully retrieved {len(result)} recommendations for date {target_date}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving recommendations by date {target_date}: {str(e)}", exc_info=True)
            return []

    async def get_by_code(
            self,
            stock_code: str,
            target_date: Optional[date] = None
    ) -> List[StockRecommend]:
        """
        Get list of recommendation by ticker
        """
        logger.debug(f"Starting get_by_code operation for ticker: {stock_code}, target_date: {target_date}")
        try:
            query = {"ticker": stock_code}
            if target_date:
                query["hit_date"] = target_date.isoformat()
                logger.debug(f"Added date filter to query: {target_date}")

            logger.debug(f"Querying database with: {query}")
            cursor = self.database.recommends.find(query).sort("score", -1)
            stocks = await cursor.to_list()

            logger.debug(f"Found {len(stocks)} recommendations for ticker {stock_code}")
            result = [StockRecommend(**stock) for stock in stocks]
            logger.info(f"Successfully retrieved {len(result)} recommendations for ticker {stock_code}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving recommendations by ticker {stock_code}: {str(e)}", exc_info=True)
            return []

    async def insert_db(
            self,
            recommend_data: Union[StockRecommend, List[StockRecommend]]
    ) -> bool:
        """
        Insert một hoặc nhiều dữ liệu cổ phiếu vào database
        """
        logger.debug(f"Starting insert_db operation with data type: {type(recommend_data)}")
        try:
            if isinstance(recommend_data, list):
                logger.debug(f"Inserting {len(recommend_data)} stock recommendations")
                data_to_insert = [stock.model_dump() for stock in recommend_data]

                # Log sample data for debugging
                if data_to_insert:
                    logger.debug(f"Sample data keys: {list(data_to_insert[0].keys())}")
                    logger.debug(f"Sample tickers: {[data.get('ticker', 'unknown') for data in data_to_insert[:5]]}")

                result = await self.database.recommends.insert_many(data_to_insert)
                logger.debug(f"Insert many result - inserted_ids count: {len(result.inserted_ids)}")
                logger.info(f"Successfully inserted {len(recommend_data)} stock data records")
            else:
                logger.debug(f"Inserting single stock recommendation for ticker: {recommend_data.ticker}")
                data_to_insert = recommend_data.model_dump()
                logger.debug(f"Single data keys: {list(data_to_insert.keys())}")

                result = await self.database.recommends.insert_one(data_to_insert)
                logger.debug(f"Insert one result - inserted_id: {result.inserted_id}")
                logger.info(f"Successfully inserted ticker data for {recommend_data.ticker} on {recommend_data.hit_date}")
            return True
        except Exception as e:
            logger.error(f"Error inserting stock data: {str(e)}", exc_info=True)
            return False
