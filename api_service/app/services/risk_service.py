from typing import List, Optional, Union
from app.db.mongodb import get_database
from app.models.risk_model import RiskRating
import logging
from pymongo import UpdateOne

logger = logging.getLogger(__name__)

class RiskService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_risks(self, quarter: Optional[str] = None) -> List[RiskRating]:
        """
        Get all risk ratings
        """
        logger.debug(f"Starting get_risks operation for quarter: {quarter}")
        try:
            if quarter:
                query = {"quarter": quarter}
                logger.debug(f"Filtering by quarter: {quarter}")
            else:
                query = {}
                logger.debug("Getting all risk ratings (no quarter filter)")

            logger.debug(f"Querying database with: {query}")
            cursor = self.database.risks.find(query)
            risks = await cursor.to_list(length=None)

            logger.debug(f"Found {len(risks)} risk ratings")
            result = [RiskRating(**risk) for risk in risks]
            logger.info(f"Successfully retrieved {len(result)} risk ratings for quarter: {quarter or 'all'}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving risk ratings for quarter {quarter}: {str(e)}", exc_info=True)
            return []

    async def get_risk_by_ticker(self, ticker: str) -> Optional[RiskRating]:
        """
        Get risk rating by ticker
        """
        logger.debug(f"Starting get_risk_by_ticker operation for ticker: {ticker}")
        try:
            query = {"ticker": ticker}
            logger.debug(f"Querying database with: {query}")

            risk = await self.database.risks.find_one(query)
            if risk:
                logger.debug(f"Found risk data with keys: {list(risk.keys())}")
                result = RiskRating(**risk)
                logger.info(f"Successfully retrieved risk rating for ticker: {ticker}")
                return result
            else:
                logger.warning(f"No risk rating found for ticker: {ticker}")
                return None
        except Exception as e:
            logger.error(f"Error retrieving risk rating for ticker {ticker}: {str(e)}", exc_info=True)
            return None

    async def insert_db(self, risk_data: Union[RiskRating, List[RiskRating]]) -> bool:
        """
        Insert hoặc update risk ratings theo ticker + quarter (Async-safe)
        """
        logger.debug(f"Starting insert_db operation with data type: {type(risk_data)}")
        try:
            if isinstance(risk_data, list):
                logger.debug(f"Processing {len(risk_data)} risk ratings for upsert")

                for i, risk in enumerate(risk_data):
                    doc = risk.model_dump()
                    query = {"ticker": doc["ticker"], "quarter": doc["quarter"]}

                    logger.debug(f"Upserting risk {i+1}/{len(risk_data)} - ticker: {doc['ticker']}, quarter: {doc['quarter']}")

                    result = await self.database.risks.update_one(
                        query,
                        {"$set": doc},
                        upsert=True
                    )

                    logger.debug(f"Upsert result for {doc['ticker']}-{doc['quarter']} - matched: {result.matched_count}, modified: {result.modified_count}, upserted_id: {result.upserted_id}")

                logger.info(f"Upserted {len(risk_data)} risk records")
            else:
                doc = risk_data.model_dump()
                query = {"ticker": doc["ticker"], "quarter": doc["quarter"]}

                logger.debug(f"Upserting single risk - ticker: {doc['ticker']}, quarter: {doc['quarter']}")
                logger.debug(f"Risk data keys: {list(doc.keys())}")

                result = await self.database.risks.update_one(
                    query,
                    {"$set": doc},
                    upsert=True
                )

                logger.debug(f"Upsert result - matched: {result.matched_count}, modified: {result.modified_count}, upserted_id: {result.upserted_id}")
                logger.info(f"Upserted risk data for {doc['ticker']} - {doc['quarter']}")
            return True
        except Exception as e:
            logger.error(f"Error inserting/updating risk data: {str(e)}", exc_info=True)
            return False