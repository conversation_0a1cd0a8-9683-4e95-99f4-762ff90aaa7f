from typing import List, Optional, Union
from app.db.mongodb import get_database
from app.models.risk_model import RiskRating
import logging
from pymongo import UpdateOne

logger = logging.getLogger(__name__)

class RiskService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_risks(self, quarter: Optional[str] = None) -> List[RiskRating]:
        """
        Get all risk ratings
        """
        if quarter:
            cursor = self.database.risks.find({"quarter": quarter})
        else:
            cursor = self.database.risks.find({})
        risks = await cursor.to_list(length=None)
        return [RiskRating(**risk) for risk in risks]

    async def get_risk_by_ticker(self, ticker: str) -> Optional[RiskRating]:
        """
        Get risk rating by ticker
        """
        risk = await self.database.risks.find_one({"ticker": ticker})
        if risk:
            return RiskRating(**risk)
        return None

    async def insert_db(self, risk_data: Union[RiskRating, List[RiskRating]]) -> bool:
        """
        Insert hoặc update risk ratings theo ticker + quarter (Async-safe)
        """
        try:
            if isinstance(risk_data, list):
                for risk in risk_data:
                    doc = risk.model_dump()
                    await self.database.risks.update_one(
                        {"ticker": doc["ticker"], "quarter": doc["quarter"]},
                        {"$set": doc},
                        upsert=True
                    )
                logger.info(f"Upserted {len(risk_data)} risk records")
            else:
                doc = risk_data.model_dump()
                await self.database.risks.update_one(
                    {"ticker": doc["ticker"], "quarter": doc["quarter"]},
                    {"$set": doc},
                    upsert=True
                )
                logger.info(f"Upserted risk data for {doc['ticker']} - {doc['quarter']}")
            return True
        except Exception as e:
            logger.error(f"Error inserting/updating risk data: {str(e)}")
            return False