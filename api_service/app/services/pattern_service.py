from typing import List, Optional
from app.db.mongodb import get_database
from app.models.pattern_model import BuyPattern, ReturnBuyPattern
import logging

logger = logging.getLogger(__name__)


class PatternService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_patterns(
            self
    ) -> List[BuyPattern]:
        """
        Get all buy patterns
        """
        logger.debug("Starting get_patterns operation")
        try:
            cursor = self.database.patterns.find()
            patterns = await cursor.to_list()
            logger.debug(f"Found {len(patterns)} patterns in database")

            result = [BuyPattern(**pattern) for pattern in patterns]
            logger.info(f"Successfully retrieved {len(result)} buy patterns")
            return result
        except Exception as e:
            logger.error(f"Error retrieving patterns: {str(e)}", exc_info=True)
            return []

    async def get_pattern(
            self,
            pattern: str
    ) -> Optional[BuyPattern]:
        """
        Get a buy pattern
        """
        logger.debug(f"Starting get_pattern operation for pattern: {pattern}")
        try:
            query = {"buy_pattern": pattern}
            logger.debug(f"Querying database with: {query}")

            pattern_data = await self.database.patterns.find_one(query)
            if pattern_data:
                logger.debug(f"Found pattern data with keys: {list(pattern_data.keys())}")
                result = BuyPattern(**pattern_data)
                logger.info(f"Successfully retrieved pattern: {pattern}")
                return result
            else:
                logger.warning(f"Pattern not found: {pattern}")
                return None
        except Exception as e:
            logger.error(f"Error retrieving pattern {pattern}: {str(e)}", exc_info=True)
            return None

    async def get_patterns_by_strategy(
            self,
            strategy: str
    ) -> List[ReturnBuyPattern]:
        """
        Get patterns filtered by strategy
        """
        logger.debug(f"Starting get_patterns_by_strategy operation for strategy: {strategy}")
        try:
            cursor = self.database.patterns.find()
            raw_patterns = await cursor.to_list()
            logger.debug(f"Found {len(raw_patterns)} raw patterns in database")

            patterns = []
            for pattern in raw_patterns:
                # Find score for the specific strategy
                score = next((sty['score'] for sty in pattern.get('strategies', []) if sty['strategy'] == strategy), 0)
                pattern['score'] = score
                logger.debug(f"Pattern {pattern.get('buy_pattern', 'unknown')} has score {score} for strategy {strategy}")

            result = [ReturnBuyPattern(**pattern) for pattern in raw_patterns]
            logger.info(f"Successfully retrieved {len(result)} patterns for strategy: {strategy}")
            return result
        except Exception as e:
            logger.error(f"Error retrieving patterns by strategy {strategy}: {str(e)}", exc_info=True)
            return []

    async def update_pattern(
            self,
            pattern: str,
            new_data: BuyPattern
    ) -> bool:
        """
        Update pattern
        """
        logger.debug(f"Starting update_pattern operation for pattern: {pattern}")
        try:
            pattern_data = new_data.model_dump()
            logger.debug(f"Updating pattern {pattern} with data keys: {list(pattern_data.keys())}")

            result = await self.database.patterns.update_one(
                {"buy_pattern": pattern},
                {"$set": pattern_data},
                upsert=True
            )

            logger.debug(f"Update pattern result - matched: {result.matched_count}, modified: {result.modified_count}, upserted_id: {result.upserted_id}")
            logger.info(f"Pattern {pattern} updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating pattern {pattern}: {str(e)}", exc_info=True)
            return False

    async def update_sell_pattern(
            self,
            pattern: str,
            new_data: list) -> bool:
        """
        Update list_sell_pattern
        """
        logger.debug(f"Starting update_sell_pattern operation for pattern: {pattern} with {len(new_data)} sell patterns")
        try:
            logger.debug(f"Updating sell patterns for {pattern} with data: {new_data}")

            result = await self.database.patterns.update_one(
                {"buy_pattern": pattern},
                {"$set": {"list_sell_pattern": new_data}},
                upsert=True
            )

            logger.debug(f"Update sell pattern result - matched: {result.matched_count}, modified: {result.modified_count}")
            logger.info(f"List_sell_pattern of {pattern} updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating list_sell_pattern for {pattern}: {str(e)}", exc_info=True)
            return False

    async def insert_pattern(
            self,
            pattern_data: BuyPattern
    ) -> bool:
        """
        Insert pattern
        """
        logger.debug(f"Starting insert_pattern operation for pattern: {pattern_data.buy_pattern}")
        try:
            pattern_dict = pattern_data.model_dump()
            logger.debug(f"Inserting pattern data with keys: {list(pattern_dict.keys())}")

            # Note: This seems to insert into 'recommends' collection, might be a bug?
            result = await self.database.recommends.insert_one(pattern_dict)
            logger.debug(f"Insert pattern result - inserted_id: {result.inserted_id}")
            logger.info(f"Successfully inserted pattern data for {pattern_data.buy_pattern}")
            return True
        except Exception as e:
            logger.error(f"Error inserting pattern data for {pattern_data.buy_pattern}: {str(e)}", exc_info=True)
            return False
