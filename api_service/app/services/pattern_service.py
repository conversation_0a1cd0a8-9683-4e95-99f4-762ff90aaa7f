from typing import List, Optional
from app.db.mongodb import get_database
from app.models.pattern_model import BuyPattern, ReturnBuyPattern
import logging

logger = logging.getLogger(__name__)


class PatternService:
    def __init__(self):
        self.db = None

    @property
    def database(self):
        if self.db is None:
            self.db = get_database()
        return self.db

    async def get_patterns(
            self
    ) -> List[BuyPattern]:
        """
        Get all buy patterns
        """
        cursor = self.database.patterns.find()
        patterns = await cursor.to_list()
        return [BuyPattern(**pattern) for pattern in patterns]

    async def get_pattern(
            self,
            pattern: str
    ) -> Optional[BuyPattern]:
        """
        Get a buy pattern
        """
        query = {"buy_pattern": pattern}
        pattern = await self.database.patterns.find_one(query)
        return BuyPattern(**pattern) if pattern else None

    async def get_patterns_by_strategy(
            self,
            strategy: str
    ) -> List[ReturnBuyPattern]:
        """
        Get a buy pattern
        """
        cursor = self.database.patterns.find()
        raw_patterns = await cursor.to_list()
        patterns = []
        for pattern in raw_patterns:
            pattern['score'] = next((sty['score'] for sty in pattern['strategies'] if sty['strategy'] == strategy), 0)
        return [ReturnBuyPattern(**pattern) for pattern in raw_patterns]

    async def update_pattern(
            self,
            pattern: str,
            new_data: BuyPattern
    ) -> bool:
        """
        Update pattern
        """
        try:
            await self.database.patterns.update_one(
                {"buy_pattern": pattern},
                {"$set": new_data.model_dump()},
                upsert=True
            )
            logger.info(f"Pattern {pattern} updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating pattern: {str(e)}")
            return False

    async def update_sell_pattern(
            self,
            pattern: str,
            new_data: list) -> bool:
        """
        Update list_sell_pattern
        """
        try:
            await self.database.patterns.update_one(
                {"buy_pattern": pattern},
                {"$set": {"list_sell_pattern": new_data}},
                upsert=True
            )
            logger.info(f"List_sell_pattern of {pattern} updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating list_sell_pattern: {str(e)}")
            return False

    async def insert_pattern(
            self,
            pattern_data: BuyPattern
    ) -> bool:
        """
        Insert pattern
        """
        try:
            await self.database.recommends.insert_one(pattern_data.model_dump())
            logger.info(f"Successfully inserted pattern data for {pattern_data.buy_pattern}")
            return True
        except Exception as e:
            logger.error(f"Error inserting pattern data: {str(e)}")
            return False
