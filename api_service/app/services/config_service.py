from typing import Optional
from app.db.mongodb import get_database
from app.models.config_model import SystemConfig
import logging

logger = logging.getLogger(__name__)

class ConfigService:
    def __init__(self):
        self.db = get_database()

    async def get_config(self) -> Optional[SystemConfig]:
        """
        <PERSON><PERSON><PERSON> c<PERSON>u hình hệ thống
        """
        logger.debug("Starting get_config operation")
        try:
            config = await self.db.config.find_one()
            if config:
                logger.debug(f"Found config with keys: {list(config.keys())}")
                result = SystemConfig(**config)
                logger.info("Successfully retrieved system configuration")
                return result
            else:
                logger.warning("No system configuration found in database")
                return None
        except Exception as e:
            logger.error(f"Error retrieving system configuration: {str(e)}", exc_info=True)
            return None

    async def update_config(self, config: SystemConfig) -> bool:
        """
        Cậ<PERSON> nh<PERSON>t c<PERSON>u hình hệ thống
        """
        logger.debug(f"Starting update_config operation with config: {config.model_dump()}")
        try:
            config_data = config.model_dump()
            logger.debug(f"Updating config with data: {config_data}")

            result = await self.db.config.update_one(
                {},
                {"$set": config_data},
                upsert=True
            )

            logger.debug(f"Update result - matched: {result.matched_count}, modified: {result.modified_count}, upserted_id: {result.upserted_id}")
            logger.info("System configuration updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating system configuration: {str(e)}", exc_info=True)
            return False

    async def get_rules(self) -> Optional[dict]:
        """
        Lấy các quy tắc cấu hình
        """
        logger.debug("Starting get_rules operation")
        try:
            config = await self.get_config()
            if config and config.rules:
                logger.debug(f"Found rules with keys: {list(config.rules.keys()) if isinstance(config.rules, dict) else 'non-dict rules'}")
                logger.info("Successfully retrieved system rules")
                return config.rules
            else:
                logger.warning("No rules found in system configuration")
                return None
        except Exception as e:
            logger.error(f"Error retrieving system rules: {str(e)}", exc_info=True)
            return None

    async def update_rules(self, rules: dict) -> bool:
        """
        Cập nhật các quy tắc cấu hình
        """
        logger.debug(f"Starting update_rules operation with rules: {rules}")
        try:
            logger.debug(f"Updating rules with data: {rules}")

            result = await self.db.config.update_one(
                {},
                {"$set": {"rules": rules}},
                upsert=True
            )

            logger.debug(f"Update rules result - matched: {result.matched_count}, modified: {result.modified_count}")
            logger.info("System rules updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating system rules: {str(e)}", exc_info=True)
            return False

    async def get_active_indicators(self) -> Optional[list]:
        """
        Lấy danh sách các chỉ báo kỹ thuật đang hoạt động
        """
        logger.debug("Starting get_active_indicators operation")
        try:
            config = await self.get_config()
            if config and config.active_indicators:
                logger.debug(f"Found {len(config.active_indicators)} active indicators")
                logger.info("Successfully retrieved active indicators")
                return config.active_indicators
            else:
                logger.warning("No active indicators found in system configuration")
                return None
        except Exception as e:
            logger.error(f"Error retrieving active indicators: {str(e)}", exc_info=True)
            return None

    async def update_active_indicators(self, indicators: list) -> bool:
        """
        Cập nhật danh sách các chỉ báo kỹ thuật đang hoạt động
        """
        logger.debug(f"Starting update_active_indicators operation with {len(indicators)} indicators: {indicators}")
        try:
            logger.debug(f"Updating active indicators with data: {indicators}")

            result = await self.db.config.update_one(
                {},
                {"$set": {"active_indicators": indicators}},
                upsert=True
            )

            logger.debug(f"Update indicators result - matched: {result.matched_count}, modified: {result.modified_count}")
            logger.info("Active indicators updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating active indicators: {str(e)}", exc_info=True)
            return False