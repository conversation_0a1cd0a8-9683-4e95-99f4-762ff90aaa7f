from typing import Optional
from app.db.mongodb import get_database
from app.models.config_model import SystemConfig
import logging

logger = logging.getLogger(__name__)

class ConfigService:
    def __init__(self):
        self.db = get_database()

    async def get_config(self) -> Optional[SystemConfig]:
        """
        <PERSON><PERSON>y cấu hình hệ thống
        """
        config = await self.db.config.find_one()
        return SystemConfig(**config) if config else None

    async def update_config(self, config: SystemConfig) -> bool:
        """
        Cập nhật cấu hình hệ thống
        """
        try:
            await self.db.config.update_one(
                {},
                {"$set": config.model_dump()},
                upsert=True
            )
            logger.info("System configuration updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating system configuration: {str(e)}")
            return False

    async def get_rules(self) -> Optional[dict]:
        """
        <PERSON><PERSON><PERSON> các quy tắc cấu hình
        """
        config = await self.get_config()
        return config.rules if config else None

    async def update_rules(self, rules: dict) -> bool:
        """
        Cập nhật các quy tắc cấu hình
        """
        try:
            await self.db.config.update_one(
                {},
                {"$set": {"rules": rules}},
                upsert=True
            )
            logger.info("System rules updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating system rules: {str(e)}")
            return False

    async def get_active_indicators(self) -> Optional[list]:
        """
        Lấy danh sách các chỉ báo kỹ thuật đang hoạt động
        """
        config = await self.get_config()
        return config.active_indicators if config else None

    async def update_active_indicators(self, indicators: list) -> bool:
        """
        Cập nhật danh sách các chỉ báo kỹ thuật đang hoạt động
        """
        try:
            await self.db.config.update_one(
                {},
                {"$set": {"active_indicators": indicators}},
                upsert=True
            )
            logger.info("Active indicators updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating active indicators: {str(e)}")
            return False 