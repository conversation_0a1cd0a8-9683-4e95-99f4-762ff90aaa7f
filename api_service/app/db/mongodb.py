from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class MongoDB:
    client: AsyncIOMotorClient = None
    db = None


async def connect_to_mongo():
    logger.info("Connecting to MongoDB...")
    MongoDB.client = AsyncIOMotorClient(settings.MONGO_URL)
    MongoDB.db = MongoDB.client[settings.MONGO_DB_NAME]
    logger.info("Connected to MongoDB")


async def close_mongo_connection():
    logger.info("Closing MongoDB connection...")
    if MongoDB.client:
        MongoDB.client.close()
    logger.info("MongoDB connection closed")


def get_database():
    return MongoDB.db
