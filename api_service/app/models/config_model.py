from pydantic import BaseModel, Field
from typing import List, Dict, Optional

class Rules(BaseModel):
    score_threshold: float = Field(..., description="Minimum score threshold for recommendations")
    volume_threshold: int = Field(..., description="Minimum volume threshold")
    rsi_threshold: Dict[str, float] = Field(
        ...,
        description="RSI thresholds for different signals",
        example={"oversold": 30.0, "overbought": 70.0}
    )
    macd_threshold: float = Field(..., description="MACD signal threshold")

class SystemConfig(BaseModel):
    rules: Rules
    update_frequency: str = Field(..., description="How often the data is updated")
    update_time: str = Field(..., description="Time of day for updates (HH:MM)")
    active_indicators: List[str] = Field(..., description="List of active technical indicators")
    excluded_stocks: Optional[List[str]] = Field(None, description="List of stocks to exclude from analysis")
    notification_settings: Optional[Dict] = Field(
        None,
        description="Notification settings for alerts",
        example={"email": True, "telegram": False}
    )

    class Config:
        json_schema_extra = {
            "example": {
                "rules": {
                    "score_threshold": 0.7,
                    "volume_threshold": 1000000,
                    "rsi_threshold": {
                        "oversold": 30.0,
                        "overbought": 70.0
                    },
                    "macd_threshold": 0.5
                },
                "update_frequency": "daily",
                "update_time": "00:00",
                "active_indicators": ["rsi", "macd", "volume", "ma_20", "ma_50"],
                "excluded_stocks": ["STOCK1", "STOCK2"],
                "notification_settings": {
                    "email": True,
                    "telegram": False
                }
            }
        }
