from pydantic import BaseModel, Field
from typing import Optional

class RiskRating(BaseModel):
    ticker: str = Field(..., description="Stock Code")
    quarter: str = Field(..., description="Quarter time")
    beta: Optional[float] = Field(None, description="Beta value")
    downside_beta: Optional[float] = Field(None, description="Downside beta")
    deviation: Optional[float] = Field(None, description="Standard deviation")
    downside_deviation: Optional[float] = Field(None, description="Downside deviation")
    risk_rating: Optional[float] = Field(None, description="Risk rating")
    downside_risk_rating: Optional[float] = Field(None, description="Downside risk rating")


class Config:
        json_schema_extra = {
            "example": {
                "ticker": "VNM",
                "quarter": "2024Q1",
                "beta": 1,
                "downside_beta": 3,
                "deviation": 2,
                "downside_deviation": 4,
                "risk_rating": 2,
                "downside_risk_rating": 4,
            }
        }

