from pydantic import BaseModel, Field
from typing import List, Dict, Optional


class StrategyScore(BaseModel):
    strategy: str = Field(..., description="Strategy name")
    score: float = Field(..., description="Strategy score")


class BuyPattern(BaseModel):
    buy_pattern: str = Field(..., description="Pattern name")
    list_sell_pattern: List[str] = Field(..., description="List of sell patterns")
    strategies: List[StrategyScore]

    class Config:
        json_schema_extra = {
            "example": {
                "buy_pattern": "TrendingGrowth",
                "list_sell_pattern": ["BearDvg2", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellPE", "SellResistance",
                              "SellVolMax"],
                "strategies": [
                    {"strategy": "ranking_point", "score": 0.85},
                    {"strategy": "weight_score", "score": 0.78}
                ]
            }
        }


class ReturnBuyPattern(BaseModel):
    buy_pattern: str = Field(..., description="Pattern name")
    list_sell_pattern: List[str] = Field(..., description="List of sell patterns")
    score: float = Field(..., description="Score")

    class Config:
        json_schema_extra = {
            "example": {
                "buy_pattern": "TrendingGrowth",
                "list_sell_pattern": ["BearDvg2", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellPE",
                                      "SellResistance",
                                      "SellVolMax"],
                "score": 5
            }
        }
