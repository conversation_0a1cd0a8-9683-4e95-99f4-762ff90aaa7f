# class TechnicalIndicators(BaseModel):
#     rsi: float = Field(..., description="Relative Strength Index")
#     macd: float = Field(..., description="Moving Average Convergence Divergence")
#     volume: int = Field(..., description="Trading Volume")
#     ma_20: Optional[float] = Field(None, description="20-day Moving Average")
#     ma_50: Optional[float] = Field(None, description="50-day Moving Average")


# class StockRecommend(BaseModel):
#     ticker: str = Field(..., description="Stock Code")
#     deal_date: date = Field(..., description="Date of the deal data")
#     hit_date: date = Field(..., description="Date of the hit data")
#     signal: str = Field(..., description="Sell/buy pattern")
#     score: float = Field(..., description="Stock Score")
#     recommendation: str = Field(..., description="Buy/Sell Recommendation")
#     indicators: Optional[Dict] = Field(None, description="Technical Indicators")
#     price: float = Field(..., description="Current Stock Price")
#     volume: float = Field(..., description="Current volume")
#     deal_profit: float = Field(..., description="Deal profit")
#     hit_profit: float = Field(..., description="Hit profit")
#     target: float = Field(..., description="Target")
#
#     class Config:
#         json_schema_extra = {
#             "example": {
#                 "ticker": "VNM",
#                 "deal_date": "2024-01-01",
#                 "hit_date": "2024-02-01",
#                 "signal": "_BullDvg",
#                 "score": 3,
#                 "recommendation": "buy",
#                 "indicators": {
#                     "rsi": 65.5,
#                     "macd": 0.25,
#                     "volume": 1500000,
#                     "ma_20": 85.5,
#                     "ma_50": 82.3
#                 },
#                 "price": 85.5,
#                 "volume": 2.5,
#                 "deal_profit": 3.0,
#                 "hit_profit": 2.5,
#                 "target": 90.0
#             }
#         }
