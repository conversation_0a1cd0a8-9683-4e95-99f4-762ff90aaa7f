from pydantic import BaseModel, Field, field_serializer
from typing import Dict, Optional
from datetime import date


class StockRecommend(BaseModel):
    signal: str = Field(..., description="Buy/Sell Recommendation")
    ticker: str = Field(..., description="Stock Code")
    hit_date: date = Field(..., description="Date of the hit data")
    pattern: str = Field(..., description="Sell/buy pattern")
    median_volume_1m: float = Field(..., description="Current volume")
    close_price: float = Field(..., description="Current Stock Price")
    score: float = Field(..., description="Weight score")

    @field_serializer("hit_date")
    def serialize_date(self, v: date, _info):
        return v.isoformat()

    class Config:
        json_schema_extra = {
            "example": {
                "signal": "buy",
                "ticker": "VNM",
                "hit_date": "2024-02-01",
                "pattern": "_BullDvg",
                "median_volume_1m": 150000,
                "close_price": 85.5,
                "score": 4,
            }
        }


class ReturnStockRecommend(BaseModel):
    signal: str = Field(..., description="Buy/Sell Recommendation")
    ticker: str = Field(..., description="Stock Code")
    pattern: str = Field(..., description="Sell/buy pattern")
    median_volume_1m: float = Field(..., description="Current volume")
    close_price: float = Field(..., description="Current Stock Price")
    score: float = Field(..., description="Weight score")

    class Config:
        json_schema_extra = {
            "example": {
                "signal": "buy",
                "ticker": "VNM",
                "pattern": "_BullDvg",
                "median_volume_1m": 150000,
                "close_price": 85.5,
                "score": 4,
            }
        }
