import logging
import logging.handlers
import os
import sys
from datetime import datetime
from app.core.config import settings

def setup_logging():
    """
    Enhanced logging setup with detailed formatting and multiple handlers
    """
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

    # Create handlers
    handlers = []

    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        settings.LOG_FILE,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(detailed_formatter)
    file_handler.setLevel(logging.DEBUG)
    handlers.append(file_handler)

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(simple_formatter)
    console_handler.setLevel(getattr(logging, settings.LOG_LEVEL))
    handlers.append(console_handler)

    # Error file handler (separate file for errors)
    error_file = settings.LOG_FILE.replace('.log', '_errors.log')
    error_handler = logging.FileHandler(error_file)
    error_handler.setFormatter(detailed_formatter)
    error_handler.setLevel(logging.ERROR)
    handlers.append(error_handler)

    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,  # Set to DEBUG to capture all levels
        handlers=handlers,
        force=True  # Override any existing configuration
    )

    # Set specific loggers to appropriate levels
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    logging.getLogger('motor').setLevel(logging.WARNING)
    logging.getLogger('pymongo').setLevel(logging.WARNING)

    # Create main logger
    logger = logging.getLogger(__name__)
    logger.info("="*50)
    logger.info("Enhanced logging setup completed")
    logger.info(f"Log level: {settings.LOG_LEVEL}")
    logger.info(f"Log file: {settings.LOG_FILE}")
    logger.info(f"Error log file: {error_file}")
    logger.info("="*50)

    return logger

def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger instance with the specified name
    """
    return logging.getLogger(name or __name__)

def log_function_call(func_name: str, args: dict = None, kwargs: dict = None):
    """
    Helper function to log function calls with parameters
    """
    logger = get_logger()
    params = []
    if args:
        params.extend([f"{k}={v}" for k, v in args.items()])
    if kwargs:
        params.extend([f"{k}={v}" for k, v in kwargs.items()])

    param_str = ", ".join(params) if params else "no parameters"
    logger.debug(f"Calling {func_name}({param_str})")

def log_database_operation(operation: str, collection: str, query: dict = None, result_count: int = None):
    """
    Helper function to log database operations
    """
    logger = get_logger()
    query_str = f" with query: {query}" if query else ""
    result_str = f" - returned {result_count} records" if result_count is not None else ""
    logger.debug(f"Database {operation} on {collection}{query_str}{result_str}")