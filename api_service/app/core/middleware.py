import time
import logging
import j<PERSON>
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse

logger = logging.getLogger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log all HTTP requests and responses for debugging
    """
    
    def __init__(self, app, log_requests: bool = True, log_responses: bool = True):
        super().__init__(app)
        self.log_requests = log_requests
        self.log_responses = log_responses
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID for tracking
        request_id = f"{int(time.time() * 1000)}"
        
        # Log request
        if self.log_requests:
            await self._log_request(request, request_id)
        
        # Process request and measure time
        start_time = time.time()
        
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # Log response
            if self.log_responses:
                await self._log_response(response, request_id, process_time)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(process_time, 4))
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"Request {request_id} failed after {process_time:.4f}s: {str(e)}",
                exc_info=True
            )
            raise
    
    async def _log_request(self, request: Request, request_id: str):
        """Log incoming request details"""
        try:
            # Basic request info
            logger.info(
                f"Request {request_id}: {request.method} {request.url.path} "
                f"from {request.client.host if request.client else 'unknown'}"
            )
            
            # Query parameters
            if request.query_params:
                logger.debug(f"Request {request_id} query params: {dict(request.query_params)}")
            
            # Headers (excluding sensitive ones)
            headers = dict(request.headers)
            sensitive_headers = ['authorization', 'cookie', 'x-api-key']
            filtered_headers = {
                k: v if k.lower() not in sensitive_headers else '[REDACTED]'
                for k, v in headers.items()
            }
            logger.debug(f"Request {request_id} headers: {filtered_headers}")
            
            # Request body (for POST/PUT requests)
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = await self._get_request_body(request)
                    if body:
                        logger.debug(f"Request {request_id} body: {body}")
                except Exception as e:
                    logger.warning(f"Could not read request {request_id} body: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error logging request {request_id}: {str(e)}")
    
    async def _log_response(self, response: Response, request_id: str, process_time: float):
        """Log response details"""
        try:
            logger.info(
                f"Response {request_id}: {response.status_code} "
                f"in {process_time:.4f}s"
            )
            
            # Response headers
            headers = dict(response.headers)
            logger.debug(f"Response {request_id} headers: {headers}")
            
            # Response body (only for small responses and non-streaming)
            if not isinstance(response, StreamingResponse):
                try:
                    body = await self._get_response_body(response)
                    if body and len(str(body)) < 1000:  # Only log small responses
                        logger.debug(f"Response {request_id} body: {body}")
                    elif body:
                        logger.debug(f"Response {request_id} body: [Large response - {len(str(body))} chars]")
                except Exception as e:
                    logger.warning(f"Could not read response {request_id} body: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error logging response {request_id}: {str(e)}")
    
    async def _get_request_body(self, request: Request) -> str:
        """Safely get request body"""
        try:
            body = await request.body()
            if body:
                # Try to parse as JSON for better formatting
                try:
                    json_body = json.loads(body.decode('utf-8'))
                    return json.dumps(json_body, indent=2)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return body.decode('utf-8', errors='replace')
            return ""
        except Exception:
            return "[Could not read body]"
    
    async def _get_response_body(self, response: Response) -> str:
        """Safely get response body"""
        try:
            if hasattr(response, 'body') and response.body:
                # Try to parse as JSON for better formatting
                try:
                    json_body = json.loads(response.body.decode('utf-8'))
                    return json.dumps(json_body, indent=2)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return response.body.decode('utf-8', errors='replace')
            return ""
        except Exception:
            return "[Could not read body]"


class DatabaseLoggingMiddleware:
    """
    Context manager for database operation logging
    """
    
    def __init__(self, operation: str, collection: str, query: dict = None):
        self.operation = operation
        self.collection = collection
        self.query = query
        self.start_time = None
        self.logger = logging.getLogger(__name__)
    
    def __enter__(self):
        self.start_time = time.time()
        query_str = f" with query: {self.query}" if self.query else ""
        self.logger.debug(f"Starting {self.operation} on {self.collection}{query_str}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        if exc_type is None:
            self.logger.debug(f"Completed {self.operation} on {self.collection} in {duration:.4f}s")
        else:
            self.logger.error(
                f"Failed {self.operation} on {self.collection} after {duration:.4f}s: {exc_val}",
                exc_info=True
            )
    
    def log_result(self, result_count: int = None, result_info: str = None):
        """Log additional result information"""
        if result_count is not None:
            self.logger.debug(f"{self.operation} on {self.collection} returned {result_count} records")
        if result_info:
            self.logger.debug(f"{self.operation} on {self.collection} result: {result_info}")


def log_api_call(endpoint: str, params: dict = None):
    """
    Decorator for logging API endpoint calls
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            logger = logging.getLogger(func.__module__)
            
            # Log the API call
            param_str = f" with params: {params}" if params else ""
            logger.info(f"API call to {endpoint}{param_str}")
            
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"API call to {endpoint} completed in {duration:.4f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"API call to {endpoint} failed after {duration:.4f}s: {str(e)}", exc_info=True)
                raise
        
        return wrapper
    return decorator
