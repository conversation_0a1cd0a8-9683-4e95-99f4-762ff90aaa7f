from fastapi import Security, HTTPException, status
from fastapi.security.api_key import APIKeyHeader
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Tạo API key header với tên "X-API-Key"
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)


async def get_api_key(api_key_header: str = Security(api_key_header)) -> str:
    """
    Validate API key từ header
    """
    if api_key_header == settings.API_KEY:
        return api_key_header
    logger.warning(f"Invalid API key attempt: {api_key_header[:4]}...")
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Invalid API key"
    )
