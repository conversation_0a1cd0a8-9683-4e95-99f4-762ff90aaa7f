# Stock Data API Service

## Giới thiệu
Hệ thống Stock Data API là một service cung cấp dữ liệu liên quan đến cổ phiếu, bao gồm:
- <PERSON>h sách cổ phiếu nên mua/bán
- Score cho từng cổ phiếu
- Các dữ liệu phân tích kỹ thuật

## Cấu trúc Dự án
```
stock_data_api/
│── app/
│   ├── main.py                # Entry point FastAPI
│   ├── api/
│   │   ├── routes.py          # Định nghĩa các endpoint
│   ├── services/
│   │   ├── stock_data_service.py  # Logic xử lý dữ liệu cổ phiếu
│   │   ├── config_service.py      # Logic xử lý cấu hình
│   ├── db/
│   │   ├── mongodb.py         # Kết nối MongoDB
│   ├── models/
│   │   ├── stock_model.py     # Schema dữ liệu cổ phiếu
│   │   ├── config_model.py    # Schema cấu hình
│   ├── core/
│   │   ├── config.py          # Cấu hình hệ thống
│   │   ├── logging.py         # Cấu hình logging
│── tests/                     # Unit tests
│── Dockerfile                 # Docker configuration
│── docker-compose.yml         # Docker Compose configuration
│── requirements.txt           # Python dependencies
│── .env.example              # Environment variables template
│── README.md                 # Documentation
```

## Yêu cầu Hệ thống
- Python 3.8+
- MongoDB 4.4+
- Docker & Docker Compose
- Git

## Cài đặt và Chạy

### 1. Cài đặt Thông thường
1. Clone repository:
```bash
git clone <repository_url>
cd stock_data_api
```

2. Tạo môi trường ảo:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
.\venv\Scripts\activate  # Windows
```

3. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

4. Tạo file .env:
```bash
cp .env.example .env
# Chỉnh sửa các biến môi trường trong file .env
```

5. Chạy ứng dụng:
```bash
uvicorn app.main:app --reload
```

### 2. Chạy bằng Docker
1. Build và chạy containers:
```bash
docker-compose up --build
```

2. Dừng containers:
```bash
docker-compose down
```

## API Endpoints

### Stock Data Endpoints
- `GET /api/v1/stocks`: Lấy danh sách cổ phiếu
  - Query params:
    - date: Ngày cần lấy dữ liệu (YYYY-MM-DD)
    - type: Loại dữ liệu (buy/sell)
    - limit: Số lượng kết quả (mặc định: 10)

- `GET /api/v1/stocks/{stock_code}`: Lấy thông tin chi tiết của một cổ phiếu
  - Path params:
    - stock_code: Mã cổ phiếu

- `GET /api/v1/stocks/top`: Lấy danh sách cổ phiếu có điểm cao nhất
  - Query params:
    - date: Ngày cần lấy dữ liệu
    - limit: Số lượng kết quả

- `GET /api/v1/stocks/recommendation/{recommendation}`: Lấy danh sách cổ phiếu theo khuyến nghị
  - Path params:
    - recommendation: Loại khuyến nghị (buy/sell)
  - Query params:
    - date: Ngày cần lấy dữ liệu
    - limit: Số lượng kết quả

### Config Endpoints
- `GET /api/v1/config`: Lấy cấu hình hệ thống
- `POST /api/v1/config`: Cập nhật cấu hình hệ thống

## Testing
1. Chạy unit tests:
```bash
pytest tests/
```

2. Chạy với coverage:
```bash
pytest --cov=app tests/
```

## Monitoring và Logging
- Logs được lưu trong thư mục `logs/`
- Có thể xem logs realtime:
```bash
tail -f logs/app.log
```

## Troubleshooting
1. Kiểm tra kết nối MongoDB:
```bash
mongosh <MONGO_URL>
```

2. Kiểm tra logs:
```bash
docker-compose logs -f api
```

3. Restart service:
```bash
docker-compose restart api
```

## Best Practices
1. Luôn sử dụng version control cho code
2. Viết unit tests cho các tính năng mới
3. Cập nhật documentation khi có thay đổi
4. Sử dụng logging cho các hoạt động quan trọng
5. Backup dữ liệu định kỳ

## Liên hệ và Hỗ trợ
- Technical Lead: [email]
- DevOps Team: [email]
- Documentation: [link] 