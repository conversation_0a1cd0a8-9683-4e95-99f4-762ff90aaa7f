1. Rule cho Vice Coder
Configuration Files:

Đ<PERSON>m bảo file .env chứa tất cả các biến môi trường cần thiết (ví dụ: MONGO_URL, PORT, LOG_LEVEL,...).

File cấu hình phải có mẫu (template) được lưu trong repo.

Code Structure & Conventions:

Tuân thủ cấu trúc dự án đã mô tả ở trên.

Code phải được viết theo nguyên tắc SOLID, DRY, YAGNI.

Các module phải có unit test kèm theo.

Sử dụng logging và circuit breaker cho các thao tác kết nối đến MongoDB.

Deployment Automation:

Tự động tạo Docker image và container thông qua file Dockerfile và docker-compose.

Vice coder cần thực hiện các bước sau:

Clone Repository: Tạo workspace dựa trên cấu trúc dự án.

Install Dependencies: Sử dụng pip install -r requirements.txt.

Cấu hình Environment: Thiết lập file .env với thông số phù hợp.

Build & Test: Chạy tất cả các test tự động (sử dụng CI/CD nếu có).

Run Application: Khởi động ứng dụng FastAPI thông qua docker-compose.

Monitoring & Logging: Thiết lập cơ chế logging và audit theo chuẩn.

Documentation & Checklist:

Mỗi module phải có documentation hướng dẫn cách sử dụng và mở rộng.

Checklist deployment phải được cập nhật mỗi khi có tính năng mới hoặc thay đổi cấu trúc hệ thống.

Hướng dẫn cho vice coder phải rõ ràng từng bước để đảm bảo không bỏ sót bất kỳ yêu cầu nào của dự án.

2.Quy trình Query & Data Processing
Input từ API:

Các tham số chính: path, date, type (có thể bổ sung các filter khác nếu cần).

Query MongoDB:

Sử dụng các service trong stock_data_service.py để query dữ liệu theo ngày từ collection data.

Sử dụng indexing trên trường date để tối ưu hóa query.

Parse & Format Response:

Xử lý dữ liệu trả về từ MongoDB và parse thành định dạng JSON theo yêu cầu của bot.

Có thể tạo các helper functions trong helpers.py để xử lý convert data.

Auditing:

Ghi lại log của từng request/response để phục vụ việc auditing và tracking lỗi.

