version: '3.8'

services:
  api:
    build: .
    ports:
      - "8502:8000"
    restart: always
    volumes:
      - .:/app
      - ./logs:/app/logs
    environment:
      - MONGO_URL=mongodb://mongodb:27017
      - MONGO_DB_NAME=stock_recommend_db
      - DEBUG=True
      - ENVIRONMENT=development
    depends_on:
      - mongodb
    networks:
      - stock-network

  mongodb:
    image: mongo:4.4
    ports:
      - "27017:27017"
    restart: always
    volumes:
      - mongodb_data:/data/db
    networks:
      - stock-network

volumes:
  mongodb_data:

networks:
  stock-network:
    driver: bridge 