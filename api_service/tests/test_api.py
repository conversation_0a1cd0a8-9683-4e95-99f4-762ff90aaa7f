import requests
from datetime import date, datetime
import json

BASE_URL = "http://localhost:8000/api/v1"
API_KEY = "your-secret-api-key-here"  # Thay thế bằng API key của bạn

headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

pattern_data = [
    {
        "buy_pattern": "TrendingGrowth",
        "list_sell_pattern": ["BearDvg2", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellPE", "SellResistance",
                              "SellVolMax"],
        "strategies": [
            {"strategy": "ranking_point", "score": 0.85},
            {"strategy": "weight_score", "score": 0.78}
        ]
    },
    {
        "buy_pattern": "UnderBV",
        "list_sell_pattern": ["BearDvg2", "MA21", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellResistance",
                              "SellResistance1M", "SellResistance1Y", "SellVolMax"],
        "strategies": [
            {"strategy": "ranking_point", "score": 0.92},
            {"strategy": "weight_score", "score": 0.88}
        ]
    }
]

# Dữ liệu mẫu cho stock recommends
stock_recommend_datas = [
    {
        "signal": "buy",
        "ticker": "VNM",
        "hit_date": "2024-02-01",
        "pattern": "TrendingGrowth",
        "median_volume_1m": 150000,
        "close_price": 85.5,
        "score": 4.0
    },
    {
        "signal": "sell",
        "ticker": "FPT",
        "hit_date": datetime(2024, 2, 1),
        "pattern": "UnderBV",
        "median_volume_1m": 200000,
        "close_price": 92.3,
        "score": 3.5
    },
    {
        "signal": "buy",
        "ticker": "MWG",
        "hit_date": "2024-02-01",
        "pattern": "UnderBV",
        "median_volume_1m": 180000,
        "close_price": 45.2,
        "score": 4.2
    }
]

stock_recommend_data = {
    "signal": "buy",
    "ticker": "NHH",
    "hit_date": "2024-02-01",
    "pattern": "TrendingGrowth",
    "median_volume_1m": 150000,
    "close_price": 85.5,
    "score": 4.0
}


def test_get_patterns():
    response = requests.get(f"{BASE_URL}/patterns", headers=headers)
    print("\nTest get all patterns:")
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")


def test_get_pattern_by_name():
    pattern_name = "Bullish Divergence"
    response = requests.get(f"{BASE_URL}/patterns/{pattern_name}", headers=headers)
    print(f"\nTest get pattern {pattern_name}:")
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")


def test_get_recommends():
    params = {
        "target_date": "2024-02-01"
        # "target_date": datetime(2024, 2, 1)
    }
    response = requests.get(f"{BASE_URL}/recommends", headers=headers, params=params)
    print("\nTest get recommends by date:")
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")


def test_get_recommend_by_ticker():
    ticker = "VNM"
    params = {
        "target_date": "2024-02-01"
    }


def test_post_patterns():
    response = requests.post(f"{BASE_URL}/patterns", headers=headers, json=pattern_data)
    print("\nTest post patterns:")
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")


def test_post_recommends():
    response = requests.post(f"{BASE_URL}/recommends", headers=headers, json=stock_recommend_data)
    print("\nTest post recommends:")
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")


# test_post_recommends()
test_get_recommends()