from datetime import datetime, date
from pymongo import MongoClient
from bson import ObjectId

# <PERSON><PERSON><PERSON> nối MongoDB
client = MongoClient('mongodb://192.168.100.7:27017/')
db = client['stock_recommend_db']

# D<PERSON> liệu mẫu cho patterns
pattern_data = [
    {
        "buy_pattern": "TrendingGrowth",
        "list_sell_pattern": ["BearDvg2", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellPE", "SellResistance",
                              "SellVolMax"],
        "strategies": [
            {"strategy": "ranking_point", "score": 0.85},
            {"strategy": "weight_score", "score": 0.78}
        ]
    },
    {
        "buy_pattern": "UnderBV",
        "list_sell_pattern": ["BearDvg2", "MA21", "MA41", "SellBV", "SellBV2", "SellLowGrowth", "SellResistance",
                              "SellResistance1M", "SellResistance1Y", "SellVolMax"],
        "strategies": [
            {"strategy": "ranking_point", "score": 0.92},
            {"strategy": "weight_score", "score": 0.88}
        ]
    }
]

# <PERSON><PERSON> liệu mẫu cho stock recommends
stock_recommend_data = [
    {
        "signal": "buy",
        "ticker": "VNM",
        "hit_date": "2024-02-01",
        "pattern": "TrendingGrowth",
        "median_volume_1m": 150000,
        "close_price": 85.5,
        "score": 4.0
    },
    {
        "signal": "sell",
        "ticker": "FPT",
        "hit_date": datetime(2024, 2, 1),
        "pattern": "UnderBV",
        "median_volume_1m": 200000,
        "close_price": 92.3,
        "score": 3.5
    },
    {
        "signal": "buy",
        "ticker": "MWG",
        "hit_date": "2024-02-01",
        "pattern": "UnderBV",
        "median_volume_1m": 180000,
        "close_price": 45.2,
        "score": 4.2
    }
]


def init_test_data():
    try:
        # Xóa dữ liệu cũ
        db.patterns.drop()
        db.stock_recommends.drop()

        # Thêm dữ liệu mẫu
        db.patterns.insert_many(pattern_data)
        db.recommends.insert_many(stock_recommend_data)

        print("Đã khởi tạo dữ liệu test thành công!")
    except Exception as e:
        print(f"Lỗi khi khởi tạo dữ liệu: {str(e)}")


if __name__ == "__main__":
    # init_test_data()
    # a = db.recommends.find({"hit_date": "2024-02-01"})
    a = db.patterns.find({"strategy": "ranking_point"})
    print(a.to_list(length=None))
    pass
