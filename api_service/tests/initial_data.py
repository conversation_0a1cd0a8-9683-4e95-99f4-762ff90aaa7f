import os
import time

import numpy as np
import pandas as pd
from pymongo import MongoClient

from core_utils.api_client import APIClient
from core_utils.redis_cache import EvalRedis
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/api_service/tests", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

redis_cache = EvalRedis()
client = MongoClient('mongodb://localhost:27017/')
db = client['stock_recommend_db']
FPATH = 'ticker_v1a'

filter = {
    "$BKMA200": "BearDvg2, MA21, Sell<PERSON>owGrowth, SellPE, SellResistance, SellResistance1M",
    "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
    "$TL3M": "BearDvg2, Sell<PERSON><PERSON>, SellBV2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sell<PERSON><PERSON><PERSON><PERSON>, SellR<PERSON>tance1M",
    "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
    "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
    "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
    "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
    "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
    "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
    "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
    "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
    "Init": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
    "_BKMA200": "{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
    "_TrendingGrowth": "{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
    "_TL3M": "{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
    "_BuySupport": "{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
    "_RSILow30": "{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
    "_UnderBV": "{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
    "_SuperGrowth": "{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
    "_SurpriseEarning": "{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
    "_Conservative": "(Volume*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
    "_BullDvg": "{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
    "_VolMax1Y": "{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
    "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
    "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
    "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
    "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
    "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
    "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
    "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
    "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
    "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
    "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
    "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
    "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
    "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)"
}

weight = {
    "_BKMA200": 1,
    "_TrendingGrowth": 1,
    "_TL3M": 1,
    "_RSILow30": 1,
    "_UnderBV": 1,
    "_SuperGrowth": 1,
    "_SurpriseEarning": 1,
    "_Conservative": 1,
    "_BullDvg": 1,
    "~MA21": -1,
    "~MA31": -1,
    "~MA41": -1,
    "~S13": -1,
    "~SellLowGrowth": -1,
    "~SellBV": -1,
    "~SellBV2": -1,
    "~SellPE": -1,
    "~SellResistance": -1,
    "~SellResistance1M": -1,
    "~SellResistance1Y": -1,
    "~BearDvg2": -1,
    "~SellVolMax": -1
}

strategies_data = {
    "_BKMA200": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_TrendingGrowth": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_TL3M": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_BuySupport": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_RSILow30": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_UnderBV": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_SuperGrowth": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_SurpriseEarning": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_Conservative": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_BullDvg": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    },
    "_VolMax1Y": {
        "ranking_point": 0.0,
        "weight_score": 0.0
    }
}


def eval_filter_ticker(filter, pdxx, weight, lookback=10):
    """
    Evaluate filter for a ticker
    Input:
        filter: dictionary of filters
        pdxx: ticker daily series
        weight: dictionary of filter weights
        lookback: lookback period (default: 10)
    Output:
        df: dataframe of latest hit
    """

    pd_all = pdxx.copy()
    ly = []
    # sell_filters and buy filters
    for f in filter:
        try:
            if f.startswith('~') or f.startswith('_'):
                pdx = pd_all.query(f'({filter[f]})').sort_values('time', ascending=False).copy()
                pdx['filter'] = f
                ly.append(pdx)
        except Exception:
            continue

    cols = ['time', 'ticker', 'filter', 'Volume_1M_P50', 'Price']
    df = pd.concat(ly, axis=0).sort_values('time', ascending=True)
    df = df[cols]
    df['idx'] = df.index

    df['score'] = 0
    for i in range(df.shape[0]):
        now_idx = df['idx'].iloc[i]
        lookback_idx = now_idx - lookback
        slice = df.query('idx >= @lookback_idx & idx <= @now_idx')
        filters = slice['filter'].unique().tolist()
        df.loc[df.index[i], 'score'] = sum([weight.get(f, 0) for f in filters])
    # Auto fill NaN values with 0.0
    df = df.fillna(0.0)
    return df


def get_ticker_indicators(pdxx, indicators, query_by='time'):
    pd_all = pdxx.copy()
    cols = [query_by] + indicators
    pd_return = pd_all[cols]
    pd_return.drop_duplicates(subset=[query_by], keep='first', inplace=True)

    return pd_return


def init_pattern_data():
    try:
        pattern_data = []
        for k, v in filter.items():
            if k.startswith("_"):
                pattern_data.append(
                    {
                        "buy_pattern": k,
                        "list_sell_pattern": [f"~{p.strip()}" for p in filter[f"${k[1:]}"].split(",")],
                        "strategies": [{"strategy": st, "score": sc} for st, sc in strategies_data[k].items()]
                    }
                )

        # Xóa dữ liệu cũ
        db.patterns.drop()

        # Thêm dữ liệu mẫu
        db.patterns.insert_many(pattern_data)

        print("Đã khởi tạo dữ liệu test thành công!")
    except Exception as e:
        print(f"Lỗi khi khởi tạo dữ liệu: {str(e)}")


def init_recommend_data(filter, weight, start='2020-01-01', end='2025-01-01'):
    def post_report_to_api_service(df_ticker) -> None:
        api_client = APIClient(base_url="http://localhost:8502/api/v1", api_key="tav2")
        data = []

        for _, row in df_ticker.iterrows():
            data.append({
                "signal": "buy" if row['filter'].startswith('_') else 'sell',
                "pattern": row['filter'],
                "ticker": row['ticker'],
                "hit_date": row['time'],
                "median_volume_1m": row['Volume_1M_P50'],
                "close_price": row['Price'],
                "score": row['score']
            })
        max_retries = 3
        retry_delay = 1  # seconds

        for attempt in range(max_retries):
            try:
                result = api_client.insert_recommends(recommend_data_list=data)
                if result:
                    print('Inserted successfully')
                    break
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    print("Failed to insert data after maximum retries")

    try:
        db.recommends.drop()
        list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
        for ticker in list_processed_ticker:
            try:
                pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
                df = eval_filter_ticker(filter=filter, pdxx=pdxx, weight=weight, lookback=10)
                post_report_to_api_service(df)
                # Put to db
            except Exception as error:
                print(f"Error: {ticker}: {error}")
    except:
        pass


def init_riskrating_data():
    try:
        db.risks.drop()
        list_processed_ticker = [f.replace('.csv', '') for f in os.listdir(FPATH) if f.endswith('.csv')]
        #
        # df_risk = pd.read_csv('webui/risk_rating_bin.csv')
        # df_risk = df_risk.rename(columns={'Unnamed: 0': 'quarter'})
        # df_risk['quarter'] = df_risk['quarter'].astype(str)
        # df_risk = df_risk.fillna(0.0)
        for ticker in list_processed_ticker:
            try:
                pdxx = pd.read_csv(f'{FPATH}/{ticker}.csv')
                df_risk = get_ticker_indicators(pdxx,
                                                ['Beta', 'D_Beta', 'Dev', 'D_Dev', 'Risk_Rating', 'D_Risk_Rating'],
                                                query_by='quarter')
                # Put to db
                data = []
                df_risk = df_risk.astype(object).where(pd.notnull(df_risk), None)
                for _, row in df_risk.iterrows():
                    data.append({
                        "ticker": ticker,
                        "quarter": row['quarter'],
                        "beta": row['Beta'],
                        "downside_beta": row['D_Beta'],
                        "deviation": row['Dev'],
                        "downside_deviation": row['D_Dev'],
                        "risk_rating": row['Risk_Rating'],
                        "downside_risk_rating": row['D_Risk_Rating']
                    })
                api_client = APIClient(base_url="http://localhost:8502/api/v1", api_key="tav2")
                api_client.insert_risk_ratings(riskrating_data_list=data)

            except Exception as error:
                print(f"Error: {ticker}: {error}")

        print("Đã khởi tạo dữ liệu risk rating thành công!")
    except Exception as e:
        print(f"Lỗi khi khởi tạo dữ liệu: {str(e)}")


if __name__ == "__main__":
    # init_recommend_data(filter=filter, weight=weight)
    # init_pattern_data()
    init_riskrating_data()
