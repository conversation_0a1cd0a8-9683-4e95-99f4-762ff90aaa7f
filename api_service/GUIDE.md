# Hướng dẫn Sử dụng Hệ thống Stock Data API

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu
<PERSON>ệ thống Stock Data API là một service cung cấp dữ liệu liên quan đến cổ phiếu, bao gồm:
- <PERSON><PERSON> sách cổ phiếu nên mua/bán
- Score cho từng cổ phiếu
- Các dữ liệu phân tích kỹ thuật

## 2. <PERSON><PERSON><PERSON> c<PERSON>u <PERSON>ệ thống
- Python 3.8+
- MongoDB 4.4+
- Docker & Docker Compose
- Git

## 3. Cài đặt và Chạy Hệ thống

### 3.1. Cài đặt Thông thường
1. Clone repository:
```bash
git clone <repository_url>
cd stock_data_api
```

2. Tạo môi trường ảo:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
.\venv\Scripts\activate  # Windows
```

3. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

4. Tạo file .env:
```bash
cp .env.example .env
# Chỉnh sửa các biến môi trường trong file .env
```

5. Chạy ứng dụng:
```bash
uvicorn app.main:app --reload
```

### 3.2. Chạy bằng Docker
1. Build và chạy containers:
```bash
docker-compose up --build
```

2. Dừng containers:
```bash
docker-compose down
```

## 4. Cấu trúc API Endpoints

### 4.1. Stock Data Endpoints
- `GET /api/v1/stocks`: Lấy danh sách cổ phiếu
  - Query params:
    - date: Ngày cần lấy dữ liệu (YYYY-MM-DD)
    - type: Loại dữ liệu (buy/sell)
    - limit: Số lượng kết quả (mặc định: 10)

- `GET /api/v1/stocks/{stock_code}`: Lấy thông tin chi tiết của một cổ phiếu
  - Path params:
    - stock_code: Mã cổ phiếu

### 4.2. Config Endpoints
- `GET /api/v1/config`: Lấy cấu hình hệ thống
- `POST /api/v1/config`: Cập nhật cấu hình hệ thống

## 5. Cấu trúc Dữ liệu

### 5.1. Stock Data Model
```json
{
  "stock_code": "string",
  "date": "YYYY-MM-DD",
  "score": "float",
  "recommendation": "buy/sell",
  "technical_indicators": {
    "rsi": "float",
    "macd": "float",
    "volume": "integer"
  }
}
```

### 5.2. Config Model
```json
{
  "rules": {
    "score_threshold": "float",
    "volume_threshold": "integer"
  },
  "update_frequency": "string",
  "active_indicators": ["string"]
}
```

## 6. Monitoring và Logging
- Logs được lưu trong thư mục `logs/`
- Có thể xem logs realtime:
```bash
tail -f logs/app.log
```

## 7. Testing
1. Chạy unit tests:
```bash
pytest tests/
```

2. Chạy với coverage:
```bash
pytest --cov=app tests/
```

## 8. Troubleshooting
1. Kiểm tra kết nối MongoDB:
```bash
mongosh <MONGO_URL>
```

2. Kiểm tra logs:
```bash
docker-compose logs -f api
```

3. Restart service:
```bash
docker-compose restart api
```

## 9. Best Practices
1. Luôn sử dụng version control cho code
2. Viết unit tests cho các tính năng mới
3. Cập nhật documentation khi có thay đổi
4. Sử dụng logging cho các hoạt động quan trọng
5. Backup dữ liệu định kỳ

## 10. Liên hệ và Hỗ trợ
- Technical Lead: [email]
- DevOps Team: [email]
- Documentation: [link] 