Project Overview: Stock Data API Service
1. <PERSON><PERSON> tả Dự án
Dự án này nhằm cung cấp dữ liệu liên quan đến cổ phiếu, cụ thể:

<PERSON>h sách cổ phiếu nên đượ<PERSON> mua/bán vào ngày mai (<PERSON><PERSON><PERSON><PERSON> fill vào database từ một triger celery task từ bên ngoài)

Các dữ liệu này được thu thập, xử lý và lưu trữ vào MongoDB. Service sẽ thực hiện query từ MongoDB, parse dữ liệu thành file JSON và trả về cho client (bot hoặc các ứng dụng khác).

2. Kiến trúc Hệ thống
2.1. <PERSON><PERSON> tách Command & Query (CQRS)
Command Side:

Nhận dữ liệu đầu vào từ các trigger bên ngoài (ví dụ: từ Celery hoặc các task scheduler) để fill dữ liệu vào MongoDB.

Thực hiện ghi dữ liệu theo ngày (daily ingestion) vào collection data.

Query Side:

FastAPI cung cấp endpoint để truy vấn dữ liệu từ MongoDB dựa trên các tham số như ngày khuyến nghị

Dữ liệu được xử lý và trả về dưới dạng file JSON.

2.2. Domain-Driven Design & Core Domain Models
Domain Model:

StockData: Mô hình chứa các thông tin của từng cổ phiếu như mã, score, ngày, dữ liệu phân tích,...

Core Logic:

Tách biệt logic nghiệp vụ (business logic) ra khỏi tầng hạ tầng (infrastructure).

Sử dụng các service riêng biệt cho việc query dữ liệu và xử lý logic domain.

2.3. Infrastructure & Các Pattern hỗ trợ
MongoDB Integration:

Kết nối đến MongoDB sử dụng driver phù hợp, đảm bảo tính sẵn sàng và khả năng mở rộng.

Cấu hình collection cho data và config.

Auditing:

Ghi log các request/response, theo dõi các thao tác ghi và đọc dữ liệu.

Circuit Breaker Pattern:

Xử lý các lỗi kết nối đến MongoDB hoặc các service bên ngoài khác, tránh ảnh hưởng đến toàn bộ hệ thống.

Deployment Rules:

Áp dụng quy trình CI/CD với các bước kiểm tra unit-test, integration test.

Các rule deployment đảm bảo tính lặp lại và tự động hoá quá trình build, test và deploy (dockerization, docker-compose).

3. Cấu trúc Dự án

stock_data_api/
│── app/
│   ├── main.py                # Entry point FastAPI
│   ├── api/
│   │   ├── routes.py          # Định nghĩa các endpoint (GET /data, GET/POST /config, v.v.)
│   ├── services/
│   │   ├── stock_data_service.py  # Logic xử lý nghiệp vụ cho StockData
│   │   ├── config_service.py      # Logic xử lý nghiệp vụ cho Config
│   ├── db/
│   │   ├── mongodb.py         # Kết nối và tương tác với MongoDB
│   ├── models/
│   │   ├── stock_model.py     # Định nghĩa schema cho StockData
│   │   ├── config_model.py    # Định nghĩa schema cho Config
│   ├── utils/
│   │   ├── helpers.py         # Các hàm tiện ích chung (logging, circuit breaker, validation, ...)
│   ├── config/
│   │   ├── settings.py        # Cấu hình hệ thống, đọc từ .env
│── tests/
│   ├── test_api.py            # Các test cho API endpoint
│   ├── test_services.py       # Các test cho business logic
│── Dockerfile                 # File Docker cho ứng dụng FastAPI
│── docker-compose.yml         # Định nghĩa service cho API và MongoDB
│── requirements.txt           # Các thư viện cần cài đặt
│── .env                     # Các biến cấu hình môi trường (ví dụ: URL MongoDB, các thông số hệ thống)
│── README.md                # Hướng dẫn sử dụng, triển khai và các rule liên quan
