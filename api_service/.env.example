# MongoDB Configuration
MONGO_URL=mongodb://localhost:27017
DB_NAME=stock_data

# API Configuration
API_KEY=your-secret-api-key-here

# Threshold Settings
SCORE_THRESHOLD=0.7
VOLUME_THRESHOLD=1000000

# Default Settings
DEFAULT_LIMIT=10

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=Stock Data API
DEBUG=True
ENVIRONMENT=development

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Update Frequency
UPDATE_FREQUENCY=daily
UPDATE_TIME=00:00 