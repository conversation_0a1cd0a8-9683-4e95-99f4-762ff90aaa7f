import os
import sys

current_dir = '/workspace/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
# Load the necessary libraries
import random

import numpy as np

random.seed(123)
from joblib import Memory

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
from sklearn.metrics import classification_report
from sklearn.model_selection import RandomizedSearchCV
from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
# from ydata_profiling import ProfileReport

# import tensorflow as tf
# print(f'TensorFlow version: {tf.__version__}')
from sklearn.preprocessing import label_binarize

memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)


def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def label_strength_4(row, label, strong_th=10, weak_th=0, fail_th=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 3
    elif v >= weak_th:
        return 2
    elif v >= fail_th:
        return 1
    elif v < fail_th:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=20):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


df_data_all = pd.read_csv("deeplearning/dl_train.csv")

cname_tvt = 'tvt'
labels_tag = ['profit_2W', 'profit_1M', 'profit_3M', cname_tvt, 'time', 'ticker']
# Load data and labels
# list_eval = get_list_eval(df_data)

for label in ['2W', '1M', '3M']:
    # df_data_all[f'label_{label}'] = df_data_all.apply(lambda row: label_strength(row, label), axis=1)
    # df_data_all[f'label_{label}'] = df_data_all.apply(lambda row: label_binary(row, label), axis=1)
    df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    labels_tag.append(f'label_{label}_8')

    df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=12), axis=1)
    labels_tag.append(f'label_{label}_12')

    df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    labels_tag.append(f'label_{label}_26')

df_data = df_data_all.dropna(axis=0, how='any')

data_train, data_test, data_val = df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)

y_train_m, y_test_m, y_val_m = data_train[labels_tag], data_test[labels_tag], data_val[labels_tag]

X_train, X_test, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)

# y_train = y_train_m['label_1M']
# y_val = y_val_m['label_1M']
# y_test = y_test_m['label_1M']


scaler = StandardScaler()
scaler.fit(X_train)

X_train_scaled = scaler.transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)

# --- PHÂN TÍCH PHÂN PHỐI LABEL TRƯỚC KHI XỬ LÝ IMBALANCE ---
X_train_final = X_train_scaled
X_val_final = X_val_scaled
X_test_final = X_test_scaled

for lb in ['label_2W_8', 'label_1M_12', 'label_3M_26']:
    print("++++++++++++++++++++++++++++++++++++++++++++++++++++")
    print(lb)
    print(df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_test = y_test_m[lb]

    print("Label distribution before SMOTE:", np.bincount(y_train))
    # sns.countplot(x=y_train)
    # plt.title("Label distribution before SMOTE")
    # plt.show()

    # --- ÁP DỤNG SMOTE CHO MULTICLASS ---
    from imblearn.over_sampling import SMOTE

    smote = SMOTE(random_state=42)
    X_train_smote, y_train_smote = smote.fit_resample(X_train_final, y_train)

    print("Label distribution after SMOTE:", np.bincount(y_train_smote))
    # sns.countplot(x=y_train_smote)
    # plt.title("Label distribution after SMOTE")
    # plt.show()

    # --- PARAM GRID CHO MULTICLASS ---
    param_grid_mc = {
        'learning_rate': [0.04, 0.06],
        'n_estimators': [500, 700, 900],
        'max_depth': [6, 7, 8],
        'min_child_weight': [1, 2, 3],
        'subsample': [0.7, 0.8, 0.9],
        'colsample_bytree': [0.7, 0.8, 0.9],
        'gamma': [0, 0.05, 0.1],
        'reg_lambda': [1, 2, 3],
        'reg_alpha': [0.8, 1.0, 1.2],
        'max_bin': [128, 256, 512]
    }

    xgb_base_mc = xgb.XGBClassifier(
        tree_method='hist',
        objective='multi:softprob',
        num_class=3,
        n_jobs=35,
        eval_metric=['mlogloss', 'merror', 'auc_ovr'],
        random_state=42
    )

    search_mc = RandomizedSearchCV(
        estimator=xgb_base_mc,
        param_distributions=param_grid_mc,
        n_iter=15,
        scoring='f1_macro',
        cv=3,
        verbose=True,
        random_state=42,
        n_jobs=35,
        return_train_score=False
    )

    # --- FIT VỚI DỮ LIỆU SAU SMOTE ---
    search_mc.fit(X_train_smote, y_train_smote, verbose=False)

    print("\n===== BEST PARAMS & CV SCORE (SMOTE) =====")
    print("Best params:", search_mc.best_params_)
    print("Best macro F1 (CV):", search_mc.best_score_)

    # --- TRAIN FINAL MODEL VỚI DỮ LIỆU TRAIN+VAL (KHÔNG SMOTE LẠI, CHỈ GỘP TRAIN+VAL) ---
    # Lưu ý: Không dùng early_stopping ở bước này, vì không còn val set để check
    # X_train_all = np.vstack([X_train_smote, X_val_final])
    # y_train_all = np.hstack([y_train_smote, y_val])

    best_params_mc = search_mc.best_params_
    best_params_mc.update({
        'tree_method': 'hist',
        'objective': 'multi:softprob',
        'num_class': 3,
        'n_jobs': 25,
        'eval_metric': ['mlogloss', 'merror', 'auc_ovr'],
        'random_state': 42,
        'n_estimators': 2000,
        'early_stopping_rounds': 70,

    })

    final_model_mc = xgb.XGBClassifier(**best_params_mc)
    final_model_mc.fit(
        X_train_smote, y_train_smote,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # --- ĐÁNH GIÁ KẾT QUẢ ---
    y_pred_prob_mc = final_model_mc.predict_proba(X_test_final)
    y_pred_mc = y_pred_prob_mc.argmax(axis=1)

    acc_mc = accuracy_score(y_test, y_pred_mc)
    f1_mc = f1_score(y_test, y_pred_mc, average='macro')
    y_test_onehot_mc = label_binarize(y_test, classes=[0, 1, 2])
    roc_mc = roc_auc_score(y_test_onehot_mc, y_pred_prob_mc, multi_class='ovr', average='macro')

    print("\n===== FINAL TEST METRICS (SMOTE) =====")
    print(f'ACC={acc_mc * 100:.2f}% - F1={f1_mc * 100:.2f}% - ROC={roc_mc * 100:.2f}%')
    print(classification_report(y_test, y_pred_mc, digits=4))

    show_confusion_matrix(y_test, y_pred_prob_mc, class_names=['0', '1', '2'], normalize='true')
    print("++++++++++++++++++++++++++++++++++++++++++++++++++++")
