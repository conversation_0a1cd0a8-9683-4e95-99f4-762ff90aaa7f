#%%
import os
import json
import pandas as pd
from datetime import timedelta
from core_utils.base_eval import BaseEval
import re
import numpy as np
from core_utils.base_eval import TickerEval
from pathos.multiprocessing import ProcessingPool as Pool
import sys
from joblib import Memory
import xgboost as xgb
from scipy.stats import zscore
from collections import Counter
from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST

from core_utils.redis_cache import EvalRedis

memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# 1. Load filter.json
FILTER_PATH = 'report/filter.json'
TICKER_PATH = 'ticker_v1a/'
SAVE_PATH = 'deeplearning/dl_train.csv'

#%%
# HIT_FILE = 'deeplearning/pd_hit.csv'
HIT_FILE = 'deeplearning/pd_hit_email.csv'
#%%
pd_hit = pd.read_csv(HIT_FILE,
                     usecols=['ticker', 'time', 'Sell_time', 'filter', 'Sell_filter', 'Sell_profit', 'holding_period',
                              'P1W', 'P1M', 'P3M', 'P6M', 'P1Y', 'P2Y'])
# df = df.query("(time >= '2014-01-01') & (time <= '2026-01-01')")
pd_buy = pd_hit[['ticker', 'filter', 'Sell_filter', 'time', 'Sell_profit', 'holding_period']].rename(
    columns={'Sell_profit': 'profit'}).copy()
pd_sell = pd_hit[
    ['ticker', 'filter', 'Sell_filter', 'Sell_time', 'Sell_profit', 'holding_period', 'P1W', 'P1M', 'P3M', 'P6M', 'P1Y',
     'P2Y']].rename(
    columns={'Sell_time': 'time', 'Sell_profit': 'profit'}).copy()

#%%
percentiles = [i for i in np.arange(0.1, 1.1, 0.1)]

pd_pos = pd_buy[pd_buy['profit'] > 0].copy()
pd_neg = pd_buy[pd_buy['profit'] <= 0].copy()
pd_buy['temp'] = pd_buy['profit'] / pd_buy['holding_period']
pd_pos['temp'] = pd_pos['profit'] / pd_pos['holding_period']
pd_neg['temp'] = pd_neg['profit'] / pd_neg['holding_period']
pd_pos_temp = pd_pos[pd_pos['holding_period'] > 30]
#%%
percentiles = [i for i in np.arange(0.1, 1.1, 0.1)]

step_profit = []
for p in percentiles:
    step_profit.append(pd_pos['profit'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'profit': pd_pos['profit'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)

step_propd = []
for p in percentiles:
    step_propd.append(pd_pos_temp['temp'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'temp': pd_pos_temp['temp'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)
#%%
# SCORE FOR BUY

# SCORE 1
for i, step in enumerate(step_profit, start=1):
    mask = (pd_buy['profit'] >= step)
    pd_buy.loc[mask, 'score_1'] = i

pd_buy['score_1'] = pd_buy['score_1'].fillna(0)

# SCORE 2
for i, step in enumerate(step_propd, start=1):
    mask = (pd_buy['temp'] >= step) & (pd_buy['holding_period'] < 30)
    pd_buy.loc[mask, 'score_2'] = i / 2.5

for i, step in enumerate(step_propd, start=1):
    mask = (pd_buy['temp'] >= step) & (pd_buy['holding_period'] >= 30)
    pd_buy.loc[mask, 'score_2'] = i

pd_buy['score_2'] = pd_buy['score_2'].fillna(0)

# SCORE
pd_buy['score'] = pd_buy[['score_1', 'score_2']].mean(axis=1)
#%%
pd_buy.sort_values(by=['time', 'ticker'], ascending=[True, True], inplace=True)
buy_total = pd_buy.groupby(['ticker', 'time']).agg({'score': 'mean', 'profit': 'mean', 'temp': 'mean'}).reset_index()
#%%
percentiles = [i for i in np.arange(0.1, 1.1, 0.1)]

pd_sell = pd_sell[~pd_sell['Sell_filter'].isin(['Hold', 'cutloss'])].copy()
pd_sell_pos = pd_sell[pd_sell['profit'] > 0].copy()
pd_sell_neg = pd_sell[pd_sell['profit'] <= 0].copy()

pd_sell['temp'] = pd_sell['profit'] / pd_sell['holding_period']
pd_sell_pos['temp'] = pd_sell_pos['profit'] / pd_sell_pos['holding_period']
pd_sell_neg['temp'] = pd_sell_neg['profit'] / pd_sell_neg['holding_period']
pd_pos_temp = pd_sell_pos[pd_sell_pos['holding_period'] > 30]
#%%
percentiles = [i for i in np.arange(0.1, 1.1, 0.1)]

step_profit = []
for p in percentiles:
    step_profit.append(pd_sell_pos['profit'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'profit': pd_sell_pos['profit'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)

step_propd = []
for p in percentiles:
    step_propd.append(pd_pos_temp['temp'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'temp': pd_pos_temp['temp'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)

step_profit_neg = []
for p in percentiles:
    step_profit_neg.append(pd_sell_neg['profit'].quantile(p).round(2))
df_percentiles = pd.DataFrame({
    'profit': pd_sell_neg['profit'].quantile(percentiles).values
}, index=[f'{int(p * 100)}%' for p in percentiles])
print(df_percentiles)

#%%
mask = (pd_sell['profit'] >= -2)
df_sell_scores_2 = pd_sell[mask].copy()
df_sell_scores_2['score'] = 0
for i, step in enumerate(step_profit, start=1):
    mask = (pd_sell['profit'] >= step)
    df_sell_scores_2.loc[mask, 'score'] = -i

#%%
sell_scores = []
pd_sell_neg.drop_duplicates(subset=['ticker', 'time', 'holding_period'], keep='first', inplace=True)
for i, row in pd_sell_neg.iterrows():
    time = pd.to_datetime(row['time']) - pd.Timedelta(days=int(row['holding_period']))
    ticker = row['ticker']
    profit = row['profit']

    sort = {row['P1W']: 7, row['P1M']: 30, row['P3M']: 90, row['P6M']: 180, row['P1Y']: 365, row['P2Y']: 730}
    sort = {k: v for k, v in sort.items() if not (isinstance(k, float) and np.isnan(k))}
    sort = dict(sorted(sort.items(), key=lambda item: item[0], reverse=True))
    rank = 0
    for p, date in sort.items():
        temp = p / date
        if p >= 10 and temp > 0.055:
            sell_scores.append({
                'ticker': ticker,
                'time': (time + pd.Timedelta(days=date)).strftime('%Y-%m-%d'),
                'profit': p,
                'holding_period': date,
                'temp': temp,
                'rank': rank

            })
            rank += 1

df_sell_scores_1 = pd.DataFrame(sell_scores)

mask = (df_sell_scores_1['profit'] >= -2)
df_sell_scores_1.loc[mask, 'score_2'] = 0

for i, step in enumerate(step_profit, start=1):
    mask = (df_sell_scores_1['profit'] >= step)
    df_sell_scores_1.loc[mask, 'score_2'] = -i

# SCORE 2
for i, step in enumerate(step_propd, start=1):
    mask = (df_sell_scores_1['temp'] >= step) & (df_sell_scores_1['holding_period'] < 30)
    df_sell_scores_1.loc[mask, 'score_3'] = -i / 2.5

for i, step in enumerate(step_propd, start=1):
    mask = (df_sell_scores_1['temp'] >= step) & (df_sell_scores_1['holding_period'] >= 30)
    df_sell_scores_1.loc[mask, 'score_3'] = -i

df_sell_scores_1['rank'] = 1 - df_sell_scores_1['rank'] / df_sell_scores_1['rank'].max()
df_sell_scores_1['_score'] = df_sell_scores_1[['score_2', 'score_3']].mean(axis=1)
df_sell_scores_1['score'] = df_sell_scores_1['_score'] * df_sell_scores_1['rank']
#%%
# Better than old profit
sell_scores_pos = []
pd_sell_pos.drop_duplicates(subset=['ticker', 'time', 'holding_period'], keep='first', inplace=True)
for i, row in pd_sell_pos.iterrows():
    time = pd.to_datetime(row['time']) - pd.Timedelta(days=int(row['holding_period']))
    ticker = row['ticker']
    profit = row['profit']
    temp_row = row['temp']
    sort = {row['P1W']: 7, row['P1M']: 30, row['P3M']: 90, row['P6M']: 180, row['P1Y']: 365, row['P2Y']: 730}
    sort = {k: v for k, v in sort.items() if not (isinstance(k, float) and np.isnan(k))}
    sort = dict(sorted(sort.items(), key=lambda item: item[0], reverse=True))
    for p, date in sort.items():
        temp = p / date
        if p >= profit and temp > temp_row and temp > 0.08:
            sell_scores_pos.append({
                'ticker': ticker,
                'time': (time + pd.Timedelta(days=date)).strftime('%Y-%m-%d'),
                'profit': p,
                'holding_period': date,
                'temp': temp,

            })
            break

df_sell_scores_3 = pd.DataFrame(sell_scores_pos)
df_sell_scores_3['score_2'] = -1

for i, step in enumerate(step_profit, start=1):
    mask = (df_sell_scores_3['profit'] >= step)
    df_sell_scores_3.loc[mask, 'score_2'] = -i

# SCORE 2
for i, step in enumerate(step_propd, start=1):
    mask = (df_sell_scores_3['temp'] >= step) & (df_sell_scores_3['holding_period'] < 30)
    df_sell_scores_3.loc[mask, 'score_3'] = -i / 2.5

for i, step in enumerate(step_propd, start=1):
    mask = (df_sell_scores_3['temp'] >= step) & (df_sell_scores_3['holding_period'] >= 30)
    df_sell_scores_3.loc[mask, 'score_3'] = -i

df_sell_scores_3['score'] = df_sell_scores_3[['score_2', 'score_3']].mean(axis=1)

#%%
sell_total = pd.concat([df_sell_scores_1[['ticker', 'time', 'score', 'profit', 'temp']], df_sell_scores_2[['ticker', 'time', 'score', 'profit', 'temp']], df_sell_scores_3[['ticker', 'time', 'score', 'profit', 'temp']]], axis=0)

sell_total.sort_values(by=['time', 'ticker'], ascending=[True, True], inplace=True)
sell_total = sell_total.groupby(['ticker', 'time']).agg({'score': 'mean', 'profit': 'mean', 'temp': 'mean'}).reset_index()
#%%
pd_all = buy_total.merge(sell_total, on=['ticker', 'time'], how='outer')
pd_all['score'] = pd_all[['score_x', 'score_y']].max(axis=1)
pd_all['profit'] = pd_all[['profit_x', 'profit_y']].mean(axis=1)
pd_all['temp'] = pd_all[['temp_x', 'temp_y']].mean(axis=1)
pd_all_ = pd_all.drop_duplicates(subset=['ticker', 'time'], keep='first')
pd_all.to_csv('deeplearning/pd_score_all_email.csv', index=False)

#%%
pd_all_ = pd.read_csv('deeplearning/pd_score_all.csv')
pd_all = pd.read_csv('deeplearning/pd_score_all_email.csv')

pd_pp = pd.concat([pd_all[['ticker', 'time', 'score']], pd_all_[['ticker', 'time', 'score']]], axis=0)
pd_pp.sort_values(by=['time', 'ticker'], ascending=[True, True], inplace=True)
pd_pp = pd_pp.groupby(['ticker', 'time']).agg({'score': 'max'}).reset_index()

pd_pp.to_csv('deeplearning/pd_score_all.csv', index=False)
#%%
pd_a = pd.read_csv('deeplearning/pd_score_all.csv')
pd_weight = pd.read_csv('core_utils/test_weight_score_5.csv')
pd_weight = pd_weight.drop(columns=['score'])
pd_weight['time'] = pd.to_datetime(pd_weight['time'], format='%m/%d/%Y').dt.strftime('%Y-%m-%d')
#%%
pd_weight = pd_weight.merge(pd_a, on=['ticker', 'time'], how='left')
pd_weight['score'] = pd_weight['score'].ffill()
pd_weight['score'] = pd_weight['score'].fillna(0.0)
#%%
pd_weight.to_csv('core_utils/test_weight_score_dataset.csv', index=False)