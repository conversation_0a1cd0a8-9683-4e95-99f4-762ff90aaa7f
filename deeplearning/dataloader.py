import pandas as pd
from sklearn.preprocessing import StandardScaler

# Giả sử mày đã có df với indicator_cols là các cột input
indicator_cols = ['feature1', 'feature2', 'feature3', ...]  # Sửa đúng tên
label_map = {'giam_manh':0, 'giam_nhe':1, 'tang_yeu':2, 'tang_manh':3}
df['label'] = df['label'].map(label_map)  # Nếu chưa là số

# Chia train/test theo thời gian (ví dụ 80% đầu là train, 20% cuối là test)
split_idx = int(len(df)*0.8)
df_train = df.iloc[:split_idx]
df_test = df.iloc[split_idx:]

# Chuẩn hoá feature (fit trên train, transform cả train/test)
scaler = StandardScaler()
df_train[indicator_cols] = scaler.fit_transform(df_train[indicator_cols])
df_test[indicator_cols] = scaler.transform(df_test[indicator_cols])

# Lấy numpy array
X_train = df_train[indicator_cols].values
y_train = df_train['label'].values
X_test = df_test[indicator_cols].values
y_test = df_test['label'].values



import torch
from torch.utils.data import TensorDataset, DataLoader

# Convert numpy to tensor
X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
y_train_tensor = torch.tensor(y_train, dtype=torch.long)
X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
y_test_tensor = torch.tensor(y_test, dtype=torch.long)

train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)



import torch.nn as nn

class MLPClassifier(nn.Module):
    def __init__(self, input_dim, num_classes):
        super().__init__()
        self.fc1 = nn.Linear(input_dim, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_classes)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        return x


class CNN1DClassifier(nn.Module):
    def __init__(self, input_dim, num_classes):
        super().__init__()
        self.conv1 = nn.Conv1d(in_channels=1, out_channels=32, kernel_size=3, padding=1)
        self.relu = nn.ReLU()
        self.pool = nn.AdaptiveMaxPool1d(1)
        self.fc = nn.Linear(32, num_classes)

    def forward(self, x):
        # x shape: [batch, feature] -> [batch, 1, feature]
        x = x.unsqueeze(1)
        x = self.relu(self.conv1(x))
        x = self.pool(x).squeeze(-1)  # shape: [batch, 32]
        x = self.fc(x)
        return x


model = MLPClassifier(input_dim=X_train.shape[1], num_classes=4)  # hoặc CNN1DClassifier
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)



# . Đánh giá Metric
# Dùng sklearn.metrics.classification_report cho test set.
#
# Trong training loop, cứ lưu lại y_true và y_pred để tính.
from sklearn.metrics import classification_report, accuracy_score

all_y_true = []
all_y_pred = []

model.eval()
with torch.no_grad():
    for X_batch, y_batch in test_loader:
        outputs = model(X_batch)
        _, preds = torch.max(outputs, 1)
        all_y_true.extend(y_batch.numpy())
        all_y_pred.extend(preds.numpy())

print(classification_report(all_y_true, all_y_pred, target_names=label_map.keys()))


for epoch in range(epochs):
    model.train()
    for X_batch, y_batch in train_loader:
        optimizer.zero_grad()
        outputs = model(X_batch)
        loss = criterion(outputs, y_batch)
        loss.backward()
        optimizer.step()
    print(f"Epoch {epoch} - Loss: {loss.item():.4f}")