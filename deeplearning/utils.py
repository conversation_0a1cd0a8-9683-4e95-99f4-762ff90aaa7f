

def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : <PERSON><PERSON><PERSON> suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = pd.qcut(df_lift['y_prob'], q=5, labels=['0-20', '20-40', '40-60', '60-80', '80-100'])

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def feature_importance_df(model):
    booster = model.get_booster()
    gain = booster.get_score(importance_type="gain")
    cover = booster.get_score(importance_type="cover")
    weight = booster.get_score(importance_type="weight")
    keys = set(gain.keys()) | set(cover.keys()) | set(weight.keys())
    rows = []
    for k in keys:
        rows.append({
            "feature": k,
            "gain": gain.get(k, 0.0),
            "cover": cover.get(k, 0.0),
            "weight": weight.get(k, 0.0),
        })
    return pd.DataFrame(rows).sort_values("gain", ascending=False)


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2022-06-01', val_cutoff='2023-01-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    list_ticker = list(df['ticker'].unique())
    random.seed(42)
    random.shuffle(list_ticker)

    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_cal_time = (df[time_col] >= train_cutoff) & (df[time_col] < val_cutoff)
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_cal_time, cname_tvt] = 'cal'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'

    print(df[cname_tvt].value_counts())
    return df


def split_tvt_v1(df, time_col='time', cname_tvt='tvt',
                 train_end='2022-06-01',
                 val_end='2022-12-31',
                 test_start='2023-01-01'):
    """
    split_time_only
    """
    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])
    train_end = pd.to_datetime(train_end)
    val_end = pd.to_datetime(val_end)
    test_start = pd.to_datetime(test_start)

    df[cname_tvt] = 'other'
    df.loc[df[time_col] <= train_end, cname_tvt] = 'train'
    df.loc[(df[time_col] > train_end) & (df[time_col] <= val_end), cname_tvt] = 'val'
    df.loc[df[time_col] >= test_start, cname_tvt] = 'test'  # 2023+ làm test đúng bài
    return df


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5):
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    v = row[f'profit_{label}_center_{center_dict[label]}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0
