#!/usr/bin/env python3
"""
Test script for walk-forward validation implementation
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.insert(0, '/home/<USER>/dev/ta/kaffa_v2')

def create_sample_data():
    """Create sample time series data for testing"""
    
    # Create date range
    start_date = pd.to_datetime('2020-01-01')
    end_date = pd.to_datetime('2023-12-31')
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # Create sample tickers
    tickers = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'] * 20  # 100 tickers
    
    # Generate sample data
    data = []
    for date in dates:
        for ticker in tickers[:10]:  # Use 10 tickers for faster testing
            data.append({
                'time': date,
                'ticker': ticker,
                'feature1': np.random.randn(),
                'feature2': np.random.randn(),
                'feature3': np.random.randn(),
                'profit_1M': np.random.randn() * 10,
                'label_binary_1M': np.random.choice([0, 1], p=[0.7, 0.3])
            })
    
    df = pd.DataFrame(data)
    return df

def test_walk_forward_functions():
    """Test the walk-forward validation functions"""
    
    print("Testing walk-forward validation functions...")
    
    # Import the functions from the notebook (we'll need to extract them)
    # For now, let's define them here for testing
    
    def walk_forward_split(df, time_col='time', 
                          train_window_months=18, 
                          val_window_months=3,
                          purge_months=1, 
                          embargo_months=1,
                          step_months=3,
                          start_date='2020-01-01',
                          end_date='2023-12-31'):
        """
        Walk-forward validation with purge and embargo for time series data.
        """
        
        df = df.copy()
        df[time_col] = pd.to_datetime(df[time_col])
        
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        folds = []
        fold_id = 0
        
        current_train_start = start_date
        
        while True:
            # Calculate fold dates
            train_end = current_train_start + pd.DateOffset(months=train_window_months)
            purge_end = train_end + pd.DateOffset(months=purge_months)
            val_start = purge_end
            val_end = val_start + pd.DateOffset(months=val_window_months)
            embargo_end = val_end + pd.DateOffset(months=embargo_months)
            
            # Check if we have enough data for this fold
            if val_end > end_date:
                break
                
            # Check if we have enough data in the validation period
            val_data = df[(df[time_col] >= val_start) & (df[time_col] < val_end)]
            if len(val_data) < 100:  # Minimum validation samples
                break
                
            folds.append({
                'fold_id': fold_id,
                'train_start': current_train_start,
                'train_end': train_end,
                'purge_start': train_end,
                'purge_end': purge_end,
                'val_start': val_start,
                'val_end': val_end,
                'embargo_end': embargo_end
            })
            
            fold_id += 1
            current_train_start += pd.DateOffset(months=step_months)
        
        return folds
    
    # Create sample data
    df = create_sample_data()
    print(f"Created sample data: {df.shape}")
    print(f"Date range: {df['time'].min()} to {df['time'].max()}")
    
    # Test walk-forward split
    folds = walk_forward_split(
        df, 
        time_col='time',
        train_window_months=12,  # Shorter for testing
        val_window_months=3,
        purge_months=1,
        embargo_months=1,
        step_months=3,
        start_date='2020-06-01',
        end_date='2023-06-01'
    )
    
    print(f"\nCreated {len(folds)} folds:")
    for fold in folds:
        print(f"Fold {fold['fold_id']}: "
              f"Train {fold['train_start'].strftime('%Y-%m')} to {fold['train_end'].strftime('%Y-%m')}, "
              f"Val {fold['val_start'].strftime('%Y-%m')} to {fold['val_end'].strftime('%Y-%m')}")
        
        # Verify no overlap between train and validation
        train_data = df[(df['time'] >= fold['train_start']) & (df['time'] < fold['train_end'])]
        val_data = df[(df['time'] >= fold['val_start']) & (df['time'] < fold['val_end'])]
        
        print(f"  Train samples: {len(train_data)}, Val samples: {len(val_data)}")
        
        # Check for data leakage
        if len(train_data) > 0 and len(val_data) > 0:
            train_max_date = train_data['time'].max()
            val_min_date = val_data['time'].min()
            gap_days = (val_min_date - train_max_date).days
            print(f"  Gap between train and val: {gap_days} days")
            
            if gap_days < 30:  # Should be at least 1 month (purge period)
                print(f"  ⚠️  WARNING: Gap too small! Expected ~30 days, got {gap_days}")
            else:
                print(f"  ✅ Good purge gap")
    
    print("\n✅ Walk-forward validation test completed successfully!")
    return True

if __name__ == "__main__":
    test_walk_forward_functions()
