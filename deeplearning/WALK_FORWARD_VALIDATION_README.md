# Walk-Forward Validation with Purge/Embargo

## Problem Statement

The original data split method was inadequate for time series financial data because:

1. **Simple time-based split**: Used basic train/validation/test splits without considering temporal dependencies
2. **Data leakage**: No purge period between train and validation sets
3. **Overfitting to specific periods**: Model couldn't generalize to future unseen periods
4. **Unrealistic evaluation**: Didn't simulate real trading conditions with delays

## Solution: Walk-Forward Validation with Purge/Embargo

### Key Improvements

#### 1. Walk-Forward Validation
- **Multiple time-based folds**: Instead of one train/val split, creates multiple overlapping training windows
- **Rolling window approach**: Each fold uses a fixed training window that moves forward in time
- **Robust evaluation**: Performance metrics averaged across multiple folds provide more reliable estimates

#### 2. Purge Period
- **Purpose**: Eliminates look-ahead bias by creating a gap between training and validation data
- **Implementation**: 1-month gap between training end and validation start
- **Benefit**: Prevents the model from learning patterns that wouldn't be available in real trading

#### 3. Embargo Period
- **Purpose**: Simulates realistic trading delays and execution time
- **Implementation**: 1-month gap after validation period before next training window
- **Benefit**: Accounts for the time needed to implement trading decisions in practice

### Configuration Parameters

```python
walk_forward_split(
    df=df_data_all,
    time_col='time',
    train_window_months=18,    # 18 months of training data
    val_window_months=3,       # 3 months of validation data
    purge_months=1,            # 1 month purge to avoid look-ahead bias
    embargo_months=1,          # 1 month embargo to simulate trading delays
    step_months=3,             # Move forward 3 months for each fold
    start_date='2020-01-01',   # Start walk-forward from 2020
    end_date='2022-06-01'      # End before final test period
)
```

### Timeline Example

For data from 2020-2023, the walk-forward validation creates folds like:

```
Fold 1: Train [2020-01 to 2021-07] → Purge [2021-07 to 2021-08] → Val [2021-08 to 2021-11]
Fold 2: Train [2020-04 to 2021-10] → Purge [2021-10 to 2021-11] → Val [2021-11 to 2022-02]
Fold 3: Train [2020-07 to 2022-01] → Purge [2022-01 to 2022-02] → Val [2022-02 to 2022-05]
...
```

### Benefits

1. **Prevents Overfitting**: Multiple validation periods ensure the model generalizes across different market conditions
2. **Realistic Performance Estimates**: Simulates actual trading conditions with proper time gaps
3. **Confidence Intervals**: Multiple folds provide standard deviations for performance metrics
4. **Data Leakage Prevention**: Purge periods eliminate look-ahead bias
5. **Future Prediction Capability**: Model is tested on truly unseen future data

### Implementation Details

#### Core Functions

1. **`walk_forward_split()`**: Creates time-based fold definitions
2. **`apply_walk_forward_split()`**: Applies folds to dataframe and returns train/val sets
3. **`evaluate_walk_forward()`**: Trains and evaluates models across all folds

#### Evaluation Process

1. **Cross-Validation**: Train and evaluate model on each fold
2. **Aggregated Metrics**: Calculate mean and standard deviation across folds
3. **Final Model Training**: Train on all available data for final holdout test
4. **Holdout Test**: Evaluate on completely unseen data (2023+)

### Results Interpretation

#### Fold-wise Results
- Each fold shows performance on a specific time period
- Helps identify periods where the model struggles
- Reveals temporal stability of the model

#### Aggregated Results
- Mean performance across all folds
- Standard deviation indicates consistency
- Overall ROC-AUC on combined predictions

#### Holdout Test
- Final evaluation on completely unseen data
- Most realistic estimate of future performance
- Ultimate test of model generalization

### Best Practices

1. **Purge Period**: Should be at least as long as the prediction horizon
2. **Training Window**: Long enough to capture market cycles (12-24 months)
3. **Validation Window**: Short enough to be relevant but long enough for stable metrics (3-6 months)
4. **Step Size**: Balance between computational cost and evaluation granularity

### Comparison: Before vs After

| Aspect | Before | After |
|--------|--------|-------|
| Validation Method | Single time split | Multiple walk-forward folds |
| Data Leakage | Possible | Prevented with purge |
| Realism | Low | High (with embargo) |
| Confidence | Single estimate | Mean ± std across folds |
| Future Prediction | Questionable | Validated on multiple periods |

### Usage

Run the updated notebook `exp_ml_dl.ipynb` which now includes:

1. Walk-forward fold creation
2. Cross-validation training and evaluation
3. Final model training on all data
4. Holdout test on completely unseen data
5. Comprehensive performance reporting

The implementation ensures your model can reliably predict periods it hasn't been trained on, addressing the original problem of poor generalization to future data.
