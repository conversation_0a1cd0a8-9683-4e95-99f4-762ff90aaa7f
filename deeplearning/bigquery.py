import os
import re
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from io import BytesIO
import sys

import numpy as np
from pathos.multiprocessing import ProcessingPool as Pool

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/deeplearning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

import pandas as pd
from google.cloud import bigquery
from google.cloud import storage

from core_utils.gcstorage import GoogleCloudStorageService

filter = {
    "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
    "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
    "$TL3M": "BearDvg2, SellBV, SellBV2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sell<PERSON><PERSON><PERSON><PERSON>, Sell<PERSON><PERSON>tance1M",
    "$BuySupport": "BearDvg2, SellBV, Sell<PERSON>owGrowth, SellR<PERSON>tance, SellResistance1Y",
    "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
    "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
    "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
    "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
    "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
    "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
    "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
    "Init": "(Volume*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
    "_BKMA200": "{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0/NP_P1 > 1.1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
    "_TrendingGrowth": "{Init} &(Close/Volume_Max5Y_High> 1.0) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
    "_TL3M": "{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
    "_BuySupport": "{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & ((CF_OA_5Y/OShares)> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
    "_RSILow30": "{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
    "_UnderBV": "{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
    "_SuperGrowth": "{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
    "_SurpriseEarning": "{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
    "_Conservative": "(Volume*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
    "_BullDvg": "{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & ((CF_OA_5Y/OShares)> 7800.0) & (NP_P0/NP_P4 >=1.3)",
    "_VolMax1Y": "{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
    "~MA21": "(MA20/MA50<1.04) & (MA20_T1/MA50_T1>0.96)  &  (D_RSI/D_RSI_T1W < 1.21) & (Close < 0.81*VAP1M)  & (D_MACDdiff< 7.0) & (Close/Close_T1W < 0.95)",
    "~MA31": "(MA10/MA200<1) & (MA10_T1/MA200_T1>1) & (Close < 0.98*VAP3M)  & (Close/Close_T1W < 0.95)&   (D_RSI/D_RSI_T1W < 0.95) & (D_RSI < 0.5) & (D_MACDdiff< 0)",
    "~MA41": "(Close > 1.5*MA200) & (NP_P0/NP_P1 < 0.88) & (Volume>0.9*Volume_3M_P50)  & (Close/Close_T1W < 0.94) & (Close < 1.01*VAP1M)",
    "~S13": "(C_L1W>=1.29) & (D_CMB_Peak_T1>1.09*D_CMB) & (Close>1.12*MA10) & (D_CMB_XFast<7.0)",
    "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2) & (ID_Current -  ID_Release <= 10)",
    "~SellBV": "(Close > 1.85*BVPS) & (NP_P0 /NP_P1 < 0.91) &(Close < 0.97*VAP1M) & (Close_T1W > 0.92*VAP1M) & (Volume > 0.95* Volume_3M_P50) & (ICB_Code != 8633)",
    "~SellBV2": "(PB > 1.23*PB_MA5Y + 0.84*PB_SD5Y) & (NP_P0 /NP_P1 < 0.62)  & (Close < 0.99*VAP1M) & (Close_T1W > 0.8*VAP1M)  & (D_RSI > 0.28) & (Volume > 1.01*Volume_3M_P50)",
    "~SellPE": "(PE >= 1.29*PE_MA5Y  + 0.91*PE_SD5Y) & (NP_P0 /NP_P1 < 0.95)  & (Close < 1.02*VAP3M) & (Close_T1W > 0.9*VAP3M)  & (Close/Close_T1W < 0.97)  & (Volume > 1.01*Volume_3M_P50)",
    "~SellResistance": "(Open/Close< 0.95)  & (Close  <  0.89*Res_1Y)  & (Close/LO_3M_T1 > 1.22) & (Volume > 2.08*Volume_3M_P50)",
    "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 15.0) & (Close < 0.97*VAP1M) & (Close_T1 >  1.0*VAP1M) & (Volume > 1.01* Volume_3M_P50)& (D_RSI > 0.33)",
    "~SellResistance1Y": "(PB > 1.23*PB_MA5Y + 0.93*PB_SD5Y) & (NP_P0 /NP_P1 < 0.8)  &  (Close < 0.94*Res_1Y)  & (Volume  > 1.25*Volume_3M_P50)  & (Close_T1W > 0.86*VAP1M)  & (D_RSI > 0.43)",
    "~BearDvg2": "(D_RSI_Max1W/D_RSI > 0.82)  & (D_RSI_Max3M/D_RSI_Max1W > 1)  & (D_RSI_Max3M > 0.54)& (D_RSI_Max1W < 0.7)& (D_RSI_Max1W >0.59) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.16) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.28)  & (D_RSI_T1/D_RSI > 0.99) & (Volume > 1.13*Volume_1M)",
    "~SellVolMax": "(Close/Volume_MaxTop5_2Y_Close < 0.8) & (ID_Current - Volume_MaxTop5_2Y_ID <=150.0) & (Close < 1.13*VAP1W) & (D_RSI > 0.49)  & (Close/Close_T1 < 1.08) & (D_RSI/D_RSI_T1W < 1.1) & (Close_T1/LO_3M_T1 > 1.35)",
    "~BearDvgVNI1": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
    "~BearDvgVNI1~": "(time>='2014-01-01') & (time<='2026-01-01') & (VNINDEX_RSI_Max1W/VNINDEX_RSI > 1.044)  & (VNINDEX_RSI_Max3M > 0.74) & (VNINDEX_RSI_Max1W < 0.72) & (VNINDEX_RSI_Max1W>0.61) & (VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close > 1.028) & (VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD>1.11) & (VNINDEX_MACDdiff < 0)  & ( Close/VNINDEX_RSI_Max3M_Close > 0.96) & (VNINDEX_RSI_MinT3 > 0.43) & (VNINDEX_CMF < 0.13)",
    "~BearDvgVNI2": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15) & (Risk_Rating > 3)",
    "~BearDvgVNI2~": "(time>='2014-01-01') & (time<='2026-01-01') & (VNINDEX_RSI_Max1W/VNINDEX_RSI > 1.016)  & (VNINDEX_RSI_Max3M > 0.77) & (VNINDEX_RSI_Max1W < 0.79) & (VNINDEX_RSI_Max1W>0.6) & (VNINDEX_RSI_Max1W_Close/VNINDEX_RSI_Max3M_Close > 1.008) & (VNINDEX_RSI_Max3M_MACD/VNINDEX_RSI_Max1W_MACD>1.1) & (VNINDEX_MACDdiff < 0)  & ( Close/VNINDEX_RSI_Max3M_Close > 0.97) & (VNINDEX_RSI_MinT3 > 0.5) & (VNINDEX_CMF < 0.15) & (Risk_Rating > 3)"

}


def parse_indicators_from_filter(dict_filter):
    indicators = []
    dict_filter = {k: v for k, v in dict_filter.items() if
                   (k.startswith('_') or k.startswith('~'))}  # Remove empty filters

    for key, value in dict_filter.items():
        indicator = re.findall(r'\b[A-Za-z_]\w*\b', value)
        indicator = [var for var in indicator if not var.isdigit() and var.lower() not in
                     ('and', 'or', 'not', 'if', 'else', 'true', 'false', 'null', 'none', 'in', 'is', 'ge', 'le',
                      'gt', 'lt', 'eq', 'ne', '>=', '<=', '>', '<', '==', '!=', '(', ')', '[', ']', '{', '}',
                      '&', '|', 'abs', 'acos', 'acosh', 'asin', 'asinh', 'atan', 'atanh', 'ceil', 'cos',
                      'cosh')]
        indicators.extend(indicator)

    init_cols = ['time', 'ticker', 'Volume', 'EVEB_MA5Y', 'EVEB_MA1Y', 'EVEB_MA3M', 'EVEB_SD5Y', 'EVEB_SD1Y',
                 'EVEB_SD3M', 'EVEB',
                 'ROIC_Trailing', 'ROIC3Y', 'ROIC5Y', 'ROIC_Min3Y', 'ROIC_Min5Y', 'ROA_P0',
                 'EBITM_P0', 'NPM_P0', 'EBITDA_P0', 'CashR_P0', 'QuickR_P0', 'FinLev_P0', 'AssetTurn_P0', 'FATurn_P0',
                 'DSO_P0', 'DIO_P0', 'DPO_P0', 'CashCycle_P0', 'InvTurn_P0', 'STLTDebt_Eq_P0', 'Debt_Eq_P0', 'FA_Eq_P0',
                 'OwnEq_Cap_P0']

    indicators = init_cols + indicators
    indicators = list(set(indi.replace(' ', '') for indi in indicators))

    return indicators


indicators = parse_indicators_from_filter(filter)

df = pd.read_csv('deeplearning/sample.csv')
COLS = []
schema = []
for col in df.columns:
    if col not in indicators:
        continue
    kind = df[col].dtype.kind
    if kind == "i":
        dtype = "FLOAT64"
    elif kind in "fc":
        dtype = "FLOAT64"
    else:
        dtype = "STRING"
    if col == 'time':
        dtype = "DATE"
    schema.append(bigquery.SchemaField(col, dtype, mode="NULLABLE"))
    COLS.append(col)

# Config
PROJECT_ID = 'lithe-record-440915-m9'
BUCKET_NAME = 'tav2-gs'
DATASET_ID = 'tav2_bq'
TICKER_TABLE_ID = 'ticker'
RISK_TABLE_ID = 'risk_rating'
FPATH = "ticker_v1a/"

PARTITION_FIELD = 'time'
CLUSTER_FIELD = 'ticker'

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "core_utils/env/gcskey.json"
client = bigquery.Client.from_service_account_json(os.environ['GOOGLE_APPLICATION_CREDENTIALS'])
gcs_service = GoogleCloudStorageService(BUCKET_NAME)


def import_gcs_to_bigquery(
        bucket_name, prefix, dataset_id, table_id, schema, key_path, project_id
):
    bq_client = bigquery.Client.from_service_account_json(key_path, project=project_id)
    # storage_client = storage.Client.from_service_account_json(key_path)
    table_ref = f"{project_id}.{dataset_id}.{table_id}"
    # bq_client.delete_table(table_ref, not_found_ok=True)

    try:
        table = bigquery.Table(table_ref, schema=schema)
        table.time_partitioning = bigquery.TimePartitioning(type_="DAY", field=PARTITION_FIELD)
        table.clustering_fields = [CLUSTER_FIELD]
        bq_client.create_table(table)
    except Exception as e:
        print("Table already exists", e)

    # blobs = storage_client.list_blobs(bucket_name, prefix=prefix)
    # csv_files = [blob.name for blob in blobs if blob.name.endswith(".csv")]

    job_config = bigquery.LoadJobConfig(
        schema=schema,
        source_format=bigquery.SourceFormat.CSV,
        skip_leading_rows=1,
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
        time_partitioning=bigquery.TimePartitioning(type_=bigquery.TimePartitioningType.DAY, field=PARTITION_FIELD),
        clustering_fields=[CLUSTER_FIELD],
        max_bad_records=10,
    )

    uri = f"gs://{bucket_name}/{prefix}/*.csv"
    load_job = bq_client.load_table_from_uri(uri, table_ref, job_config=job_config)
    load_job.result()


def delete_gcs_files(bucket_name, prefix, key_path):
    client = storage.Client.from_service_account_json(key_path)
    blobs = client.list_blobs(bucket_name, prefix=prefix)
    for blob in blobs:
        blob.delete()
        print(f"Đã xóa: {blob.name}")


def process_upload_import(ticker):
    try:
        file_path = FPATH + ticker.replace('.csv', '').replace('*', '') + '.csv'
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, dtype={'time': str, 'ticker': str})
            df = df[df['time'] > '2014-01-01']
        else:
            return

        for col in COLS:
            if col not in df.columns:
                df[col] = pd.NA
        df = df[COLS]

        # 2. Upload GCS
        gcs_service.upload_from_memory(df, f"v2/{ticker}.csv")

    except Exception as e:
        print(f"[LỖI file {ticker}]: {e}")


def process_upload_import_risk():
    df_risk = gcs_service.download_file_to_memory("preprocess/others/risk_indicators.csv")
    df_risk = pd.read_csv(BytesIO(df_risk))

    c_downside = [c for c in df_risk.columns if not c.endswith('_bin') and c != 'quarter']
    tickers = sorted(list(set([c.split('_')[0] for c in c_downside])))

    result = []
    for ticker in tickers:

        beta = df_risk[f'{ticker}_downside-beta_bin']
        dev = df_risk[f'{ticker}_downside-deviation_bin']
        diff = (beta - dev).abs()

        d_risk_rating = np.where(diff <= 3, np.ceil((beta + dev) / 2), np.maximum(beta, dev))
        d_risk_rating = np.where(d_risk_rating is None, 3, d_risk_rating)

        beta = df_risk[f'{ticker}_beta_bin']
        dev = df_risk[f'{ticker}_deviation_bin']
        diff = (beta - dev).abs()

        risk_rating = np.where(diff <= 3, np.ceil((beta + dev) / 2), np.maximum(beta, dev))
        risk_rating = np.where(risk_rating is None, 3, risk_rating)

        risk = np.maximum(risk_rating, d_risk_rating)

        result.append(pd.DataFrame({
            'quarter': df_risk['quarter'],
            'ticker': ticker,
            'Beta': df_risk[f'{ticker}_beta_bin'],
            'D_Beta': df_risk[f'{ticker}_downside-beta_bin'],
            'Dev': df_risk[f'{ticker}_deviation_bin'],
            'D_Dev': df_risk[f'{ticker}_downside-deviation_bin'],
            'Risk_Rating': risk,
        }))

    return pd.concat(result, axis=0).reset_index(drop=True)


def upload_data_to_bigquery_risk(dataset_id, table_id, key_path, project_id):
    df = process_upload_import_risk()
    COLS = []
    schema = []

    columns = ['quarter', 'ticker', 'Beta', 'D_Beta', 'Dev', 'D_Dev', 'Risk_Rating']
    d_types = ['STRING', 'STRING', 'FLOAT64', 'FLOAT64', 'FLOAT64', 'FLOAT64', 'FLOAT64']
    for col, dtype in zip(columns, d_types):
        schema.append(bigquery.SchemaField(col, dtype, mode="NULLABLE"))
        COLS.append(col)

    bq_client = bigquery.Client.from_service_account_json(key_path, project=project_id)
    table_ref = bq_client.dataset(dataset_id).table(table_id)

    try:
        table = bigquery.Table(table_ref, schema=schema)
        table.clustering_fields = [CLUSTER_FIELD]
        bq_client.create_table(table)
    except Exception as e:
        print("Bảng đã tồn tại hoặc đã tạo trước đó.", e)

    job_config = bigquery.LoadJobConfig(
        schema=schema,
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
        clustering_fields=[CLUSTER_FIELD],
        max_bad_records=10,
    )

    job = bq_client.load_table_from_dataframe(df, table_ref, job_config=job_config)
    job.result()

    print(f"Data uploaded to {table_id} successfully.")


if __name__ == "__main__":
    ticker_df = gcs_service.download_file_to_memory("rawdata/stock_meta/latest/ticker_list.csv")
    ticker_df = pd.read_csv(BytesIO(ticker_df))
    list_ticker = [ticker['ticker'] for _, ticker in ticker_df.iterrows()]
    list_ticker = [ticker for ticker in list_ticker if
                   ticker not in ['VN30', 'HNXINDEX', 'HNX30', 'UPCOMINDEX']]
    # list_ticker = ['HPG', 'MWG', 'MBB', 'DPG', 'HHS', 'FPT', 'VCB', 'ACB', 'TCH', 'VGG']
    # process_upload_import('HPG')
    # with ThreadPoolExecutor(max_workers=8) as executor:
    # upload_data_to_bigquery_risk(dataset_id=DATASET_ID, table_id=RISK_TABLE_ID, key_path="core_utils/env/gcskey.json",
    #                              project_id=PROJECT_ID)
    with Pool(8) as executor:
        executor.map(process_upload_import, list_ticker)

    import_gcs_to_bigquery(
        BUCKET_NAME, "v2", DATASET_ID, TICKER_TABLE_ID, schema, "core_utils/env/gcskey.json", PROJECT_ID
    )
