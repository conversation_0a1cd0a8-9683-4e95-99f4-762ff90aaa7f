import matplotlib.pyplot as plt
import seaborn as sns



from ydata_profiling import ProfileReport

profile = ProfileReport(df_all, title="Stock Data Profiling Report", explorative=True)
profile.to_file("stock_data_report.html")
# Mở file html lên, xem tất tần tật insight!



# Phân phối label
sns.countplot(x='label', data=df_all)
plt.title("Label distribution")
plt.show()

# Phân phối indicator theo label (ví dụ: RSI)
sns.boxplot(x='label', y='RSI', data=df_all)
plt.title("RSI theo label")
plt.show()

# Correlation heatmap
corr = df_all.select_dtypes(include=['float', 'int']).corr()
sns.heatmap(corr, annot=False)
plt.title("Correlation heatmap")
plt.show()


features = ['RSI', 'MACD', 'Volume', '...']  # Đ<PERSON><PERSON>n các indicator muốn visualize
X = df_all[features].fillna(0)
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X)
df_all['pca1'] = X_pca[:, 0]
df_all['pca2'] = X_pca[:, 1]

sns.scatterplot(x='pca1', y='pca2', hue='label', data=df_all, palette='viridis')
plt.title("PCA Plot")
plt.show()