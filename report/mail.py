import base64
import os
import sendgrid
from sendgrid.helpers.mail import *
from mailjet_rest import Client

FAILED_LOAD_API_KEY = "Failed to load SendGrid API key."


class SendGridException(Exception):
    def __init__(self, message: str):
        super().__init__(message)


class EmailServices_v1:
    def __init__(self):
        self.api_key = os.environ.get("SENDGRID_API_KEY",
                                      "*********************************************************************")
        self.sg = sendgrid.SendGridAPIClient(api_key=self.api_key)

        self.from_email = "<EMAIL>"

    def send(self, to_emails, subject, html_content, attachments=None):
        if self.api_key is None:
            raise SendGridException(FAILED_LOAD_API_KEY)

        message = Mail(
            from_email=self.from_email,
            to_emails=to_emails,
            subject=subject,
            html_content=html_content
        )

        if attachments is not None:
            for attachment in attachments:
                message.add_attachment(attachment)
        try:
            response = self.sg.send(message)
            print(f"Status Code: {response.status_code}")
            print(f"Body: {response.body}")
            print(f"Headers: {response.headers}")

        except Exception as e:
            print(f"An error occurred: {e}")

    def pdf_attachment(self, file_path):
        with open(file_path, 'rb') as f:
            data = f.read()
            encoded_file = base64.b64encode(data).decode()

        attached_file = Attachment(
            FileContent(encoded_file),
            FileName(file_path.split('/')[-1]),
            FileType('application/pdf'),
            Disposition('attachment')
        )
        return attached_file

    def csv_attachment(self, file_path):
        with open(file_path, 'rb') as f:
            data = f.read()
            encoded_file = base64.b64encode(data).decode()

        attached_file = Attachment(
            FileContent(encoded_file),
            FileName(file_path.split('/')[-1]),
            FileType('text/csv'),
            Disposition('attachment')
        )
        return attached_file


class EmailServices:
    def __init__(self):
        self.api_key = os.environ.get("MAILJET_API_KEY")
        self.api_secret = os.environ.get("MAILJET_API_SECRET")
        self.from_email = "<EMAIL>"
        self.mailjet = Client(auth=(self.api_key, self.api_secret), version='v3.1')

    def send(self, to_emails, subject, html_content, attachments=None):
        to_list = [{"Email": email} for email in (to_emails if isinstance(to_emails, list) else [to_emails])]

        data = {
            'Messages': [
                {
                    "From": {
                        "Email": self.from_email
                    },
                    "To": to_list,
                    "Subject": subject,
                    "HTMLPart": html_content,
                }
            ]
        }

        if attachments:
            mj_attachments = []
            for att in attachments:
                mj_attachments.append({
                    "ContentType": att['type'],
                    "Filename": att['filename'],
                    "Base64Content": att['content'],
                })
            data['Messages'][0]["Attachments"] = mj_attachments

        result = self.mailjet.send.create(data=data)
        print(f"Send result: {result.status_code}, {result.json()}")

    def csv_attachment(self, file_path):
        import base64
        with open(file_path, 'rb') as f:
            encoded = base64.b64encode(f.read()).decode('utf-8')
        return {
            "filename": file_path.split('/')[-1],
            "content": encoded,
            "type": "text/csv"
        }

    def pdf_attachment(self, file_path):
        import base64
        with open(file_path, 'rb') as f:
            encoded = base64.b64encode(f.read()).decode('utf-8')
        return {
            "filename": file_path.split('/')[-1],
            "content": encoded,
            "type": "application/pdf"
        }