import asyncio
import base64
import os
import random
from datetime import datetime

import datapane as dp
import pandas as pd
import plotly.graph_objects as go
import plotly.subplots as sp
import pyppeteer
from datapane import Width

from report.utils import load_data, apply_filter

FPATH = '../ticker_v1a/'


def AMIRSI(df_input, period):
    df = df_input[['Close']].reset_index(drop=True).copy()
    """Calculate RSI using the same logic as AmiBroker."""
    df['diff'] = df['Close'].diff()
    df['W'] = df['diff'].apply(lambda x: x if x > 0 else 0)
    df['S'] = df['diff'].apply(lambda x: -x if x < 0 else 0)

    df['P'] = 0.0
    df['N'] = 0.0

    for i in range(1, len(df)):
        df.loc[i, 'P'] = ((period - 1) * df.loc[i - 1, 'P'] + df.loc[i, 'W']) / period
        df.loc[i, 'N'] = ((period - 1) * df.loc[i - 1, 'N'] + df.loc[i, 'S']) / period

    df['RSI'] = df['P'] / (df['P'] + df['N'])

    return df['RSI'].values

class PDFReport:
    def __init__(self, ticker, df=None):
        self.df = df if df is not None else load_data(ticker, fpath=FPATH)
        self.ticker = ticker
        self.subplots = {}
        # self._initialize()

    def _initialize(self):
        self.load_dataframe(self.ticker)

    def load_dataframe(self, stock_file):
        self.df = load_data(stock_file, fpath=FPATH)

    def render(self):
        sub_figs = []
        for k, v in self.subplots.items():
            sub_fig = sp.make_subplots(rows=len(v), cols=1, shared_xaxes=True, vertical_spacing=0.05)
            sub_fig.update_layout(xaxis_rangeslider_visible=False)
            sub_fig.update_yaxes(fixedrange=False)

            for row, (fig, yaxis) in enumerate(v, 1):
                sub_fig.add_trace(fig, row=row, col=1)
                sub_fig.update_xaxes(showgrid=True, showticklabels=True, row=row, col=1)
                if yaxis:
                    sub_fig.update_yaxes(yaxis, row=row, col=1)

            sub_fig.update_layout(
                xaxis_title='Date',
                yaxis_title='Value',
                height=200 * len(v),
                width=1200,
            )
            sub_figs.append(sub_fig)

        return sub_figs

    def add_subplot(self, fig, subplot_name, yaxis=None):
        if subplot_name in self.subplots:
            self.subplots[subplot_name].append([fig, yaxis])
        else:
            self.subplots[subplot_name] = [fig, yaxis]

    def plot_price(self):
        self.df["MA_1W"] = self.df["Close"].rolling(window=5).mean()
        self.df["MA_1M"] = self.df["Close"].rolling(window=20).mean()
        self.df["MA_3M"] = self.df["Close"].rolling(window=60).mean()

        trace0 = go.Scatter(x=self.df['ymd'], y=self.df.Close, name=self.ticker)
        trace1 = go.Scatter(x=self.df['ymd'], y=self.df["MA_1W"], name="MA_1W")
        trace2 = go.Scatter(x=self.df['ymd'], y=self.df["MA_1M"], name="MA_1M")
        trace3 = go.Scatter(x=self.df['ymd'], y=self.df["MA_3M"], name="MA_3M")
        fig = go.Figure([trace0, trace1, trace2, trace3])
        return fig


    def plot_RSI(self):
        cp_df = self.df.copy()
        cp_df.set_index('ymd', inplace=True)
        cp_df['ymd'] = pd.to_datetime(cp_df['time'])

        cp_df['Week'] = cp_df.index.to_period('W')
        df_weekly = cp_df.groupby('Week').apply(lambda x: x.iloc[-1])
        df_weekly['RSI_14'] = AMIRSI(df_weekly, 14)

        cp_df['Month'] = cp_df.index.to_period('M')
        df_monthly = cp_df.groupby('Month').apply(lambda x: x.iloc[-1])
        df_monthly['RSI_14'] = AMIRSI(df_monthly, 14)

        trace0 = go.Scatter(x=self.df['ymd'], y=self.df['D_RSI'], name="D_RSI")
        trace1 = go.Scatter(x=df_weekly['ymd'], y=df_weekly['RSI_14'], name="W_RSI")
        trace2 = go.Scatter(x=df_monthly['ymd'], y=df_monthly['RSI_14'], name="M_RSI")

        fig = go.Figure([trace0, trace1, trace2])
        return fig

    @staticmethod
    def b64_html_img_1(fig):
        img_bytes = fig.to_image(format="png")
        img_base64 = base64.b64encode(img_bytes).decode('utf-8')
        html_content = f'<img src="data:image/png;base64,{img_base64}" alt="Plotly Image"style="width:1200px;height:300px;">'
        html_block = dp.HTML(html_content)

        return html_block

    @staticmethod
    def b64_html_img(fig):
        img_bytes = fig.to_image(format="png")
        img_base64 = base64.b64encode(img_bytes).decode('utf-8')
        html_content = f'<img src="data:image/png;base64,{img_base64}" alt="Plotly Image">'
        html_block = dp.HTML(html_content)
        return html_block

    @staticmethod
    async def html2pdf(html_path):
        path = html_path.split(".")[0] + ".pdf"
        abs_path = os.path.abspath(html_path)
        # browser = await launch()
        # browser = await pyppeteer.launch(
        #     headless=True,
        #     executablePath='/usr/bin/chromium-browser',
        #     args=['--no-sandbox', '--disable-setuid-sandbox']
        #     # args=["--no-sandbox"]
        # )
        browser = await pyppeteer.connect({
            "browserWSEndpoint": 'ws://192.168.100.7:3000',
        })
        page = await browser.newPage()

        with open(abs_path, "r", encoding="utf-8") as f:
            html_content = f.read()
        await page.setContent(html_content)
        await page.waitForFunction('document.readyState === "complete"')
        # await page.goto(f'file://{abs_path}', {
        #     "waitUntil": 'networkidle2'
        # })
        await page.pdf(
            path=path,
            width="15in",
            height="25in",
            margin={
                "top": 5,
                "right": 5,
                "bottom": 5,
                "left": 5,

            },
            printBackground=True,
            # format='A4',
        )
        await browser.close()

        return path

    def export2pdf(self, path):
        return asyncio.get_event_loop().run_until_complete(self.html2pdf(path))
        # return html2pdf(path)

    def build(self, dictFilter, df: list[pd.DataFrame]):
        # dictFilter = preprocess_dictFilter("{}")
        fig = plot_stock_data(self.df, dictFilter=dictFilter)

        rsi_fig = self.plot_RSI()
        price_fig = self.plot_price()

        fig_block = self.b64_html_img(fig)
        price_fig_block = self.b64_html_img_1(price_fig)
        rsi_fig_block = self.b64_html_img_1(rsi_fig)
        tables = dp.Group(blocks=[dp.Table(d.copy().to_frame().T.reset_index(drop=True)) for d in df])

        today = self.df.iloc[-1]
        v = dp.View(
            dp.BigNumber(
                heading="{} Day Performance".format(self.ticker),
                value="đ {:,.0f}".format(today.Close),
                prev_value="đ {:,.0f}".format(today.Open),
                change="{:,.2f}%".format(abs((today.Close - today.Open) / today.Open) * 100),
                is_upward_change=((today.Close - today.Open) / today.Open) > 0
            ),
            tables,
            dp.Group(
                blocks=[
                    price_fig_block,
                    rsi_fig_block,
                    fig_block
                ],
                widths=[2]
            )

        )

        ymd = datetime.today().strftime('%Y-%m-%d')
        path = os.path.join("assets", f"{self.ticker}_{ymd}.html")
        dp.save_report(v, path, open=False, formatting=dp.Formatting(width=Width.FULL))
        path_pdf = self.export2pdf(path)
        return path_pdf


def plot_stock_data(df, dictFilter={}):
    """
    df :
    - index 0..n
    - ymd : datetime
    - time : str 2021-01-01
    - indicator : float
    """
    nrows = 6
    fig = sp.make_subplots(rows=nrows, cols=1, shared_xaxes=True, vertical_spacing=0.05)

    row = 1
    # Candlestick subplot
    candlestick = go.Candlestick(x=df['ymd'],
                                 open=df['Open'],
                                 high=df['High'],
                                 low=df['Low'],
                                 close=df['Close'],
                                 name='Candlestick')

    fig.add_trace(candlestick, row=row, col=1)
    fig.update_yaxes(fixedrange=False)
    fig.update_layout(xaxis_rangeslider_visible=False)

    row += 1
    fig.add_trace(go.Bar(x=df['ymd'], y=df['Volume'], showlegend=False), row=row, col=1)
    fig.update_layout(xaxis_rangeslider_visible=False)
    # Adjust y-axis range for Candlestick subplot based on x_range

    # RSI subplot
    row += 1
    fig.add_trace(go.Scatter(x=df['ymd'], y=df['D_RSI'], mode='lines', name='RSI'), row=row, col=1)

    # Display Filter
    for f in [k for k in dictFilter if k.startswith('_')]:
        try:
            fig.add_trace(go.Scatter(x=df['ymd'], y=apply_filter(df, dictFilter[f]), mode='lines', name=f[1:]), row=row,
                          col=1)
        except Exception as e:
            # print(f"An error occurred: {e}")
            pass
    fig.update_yaxes(range=[0, 1], row=row, col=1)

    COLOR = {'D_CMB': 'rgba(128, 0, 0, .8)', 'D_CMB_Fast': 'rgba(128, 0, 0, 0.5)', 'D_CMB_Slow': 'rgba(128, 0, 0, 0.2)',
             'W_CMB': 'rgba(0, 128, 0, .8)', 'W_CMB_Fast': 'rgba(0, 128, 0, 0.5)', 'W_CMB_Slow': 'rgba(0, 128, 0, 0.2)',
             'M_CMB': 'rgba(0, 0, 128, .8)', 'M_CMB_Fast': 'rgba(0, 0, 128, 0.5)', 'M_CMB_Slow': 'rgba(0, 0, 128, 0.2)',
             }
    row += 1
    # CMB daily
    for col in ['D_CMB', 'D_CMB_Fast', 'D_CMB_Slow']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    fig.update_yaxes(range=[-1, 2], row=row, col=1)
    row += 1
    # CMB weekly
    for col in ['W_CMB', 'W_CMB_Fast', 'W_CMB_Slow']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    # divergence
    PERIOD = 5
    pdxx = df[(df['W_CMB_Step'] > 0) & (df['W_CMB_LAG'] == 0)].copy()
    for i in range(pdxx.shape[0]):
        ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['W_CMB_Step'], pdxx.iloc[i]['W_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['W_CMB']
        y1 = y2 - step * (len)
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(128, 0, 0, .5)')), row=row, col=1)
    fig.update_yaxes(range=[-1, 2], row=row, col=1)

    row += 1
    # CMB monthly
    for col in ['M_CMB']:
        ii = df[col] == df[col]
        fig.add_trace(go.Scatter(x=df[ii]['ymd'], y=df[ii][col], mode='lines', name=col,
                                 line=dict(width=2, color=COLOR.get(col, None))), row=row, col=1)
    # divergence
    PERIOD = 20
    pdxx = df[(df['M_CMB_Step'] > 0) & (df['M_CMB_LAG'] == 0)].copy()
    for i in range(pdxx.shape[0]):
        ymd2, step, len = pdxx.iloc[i]['ymd'], pdxx.iloc[i]['M_CMB_Step'], pdxx.iloc[i]['M_CMB_LEN']
        j2 = pdxx.index[i]
        j1 = int(j2 - (len) * PERIOD)
        if j1 < 0:
            continue
        ymd1 = df.iloc[j1]['ymd']
        y2 = df.iloc[j2]['M_CMB']
        y1 = y2 - step * len
        pdyy = pd.DataFrame({'ymd': [ymd1, ymd2], 'trend': [y1, y2]})
        # print(i, step, pdyy)
        fig.add_trace(go.Scatter(x=pdyy['ymd'], y=pdyy['trend'], mode='lines', showlegend=False,
                                 line=dict(color='rgba(128, 0, 0, .5)')), row=row, col=1)
    fig.update_yaxes(range=[-1, 2], row=row, col=1)

    # Show grid and set figure size
    for r in range(nrows):
        fig.update_xaxes(showgrid=True, showticklabels=True, row=r + 1, col=1)
    fig.update_layout(
        xaxis_title='Date',
        yaxis_title='Value',
        height=1000,
        width=1200,
    )

    return fig


# fig = sp.make_subplots(rows=nrows, cols=1, shared_xaxes=True, vertical_spacing=0.05)
if __name__ == "__main__":
    stock_files = os.listdir(FPATH)
    stock_files = [f[:-4] for f in stock_files if f.endswith('.csv')]
    stock_files = random.choices(stock_files, k=10)
    stock_file = "MWG"
    # Report(stock_file).build()
