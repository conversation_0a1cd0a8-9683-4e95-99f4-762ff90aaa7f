{
  "$BKMA200": "MA2, MA3, MA4, S13, SellTest",
  "$RSILow30": "MA2, MA3, MA4, S13, SellTest",
  "$T2P5X": "MA2, MA3, MA4, S13, SellTest",
  "$T3P4": "MA2, MA3, MA4, S13, SellTest",
  "$T3P6": "MA2, MA3, MA4, S13, SellTest",
  "$TL3M": "MA2, MA3, MA4, S13, SellTest",
  "$VolMax5Y": "MA2, MA3, MA4, S13, SellTest",
  "$SuperGrowth": "SellLowGrowth, SellPE",
  "Init": "(Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')",
  "_BKMA200": "{Init} & ((ID_LO_3Y-ID_HI_3Y)>293) & (MA50/MA200>0.86) & (MA10/MA200<1.37) & (ROE5Y >0.09) & (PE <20) & (NP_P0 > 1.21*NP_P1)",
  "_VolMax5Y": "{Init} & (Close>1.02*Volume_Max5Y_High) & (ROE5Y>0.09)&(PE<13)& (NP_P0 > 1.21*NP_P1) & (PE >0)&(ID_Current-Volume_Max5Y_ID<=120)&(PB<2)",
  "_TL3M": "{Init} & (HI_3M_T1/LO_3M_T1<1.28) & (Volume > 1.16*Volume_3M_P90)& (ROE5Y>0.135) & (PE<20) & (PB < 1.97) & (FSCORE > 4) & (NP_P0 > 1.16*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
  "_BKVOL5Y": "{Init} & (Volume >= Volume_Max5Y) & (ROE5Y>0.095)  & (NP_P0 > NP_P1*1.26) & (PE<15) & (FSCORE>=5)",
  "_T2P5X": "{Init} & (C_H2Y<0.7) & (C_H3M<0.8)  & (W_CMB_Step>0)  &  (W_CMB_LEN>=5)  &  (W_CMB_LAG>0) &  (W_CMB_LAG<=4)  &  (M_CMB_Step>0)  &  (M_CMB_LEN>=3)  &  (M_CMB_LAG >0)  &  (M_CMB_LAG <=4)",
  "_T3P4": "{Init} & ((((W_CMB_Step>0.025) & (W_CMB_LEN>=2) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0.025) & (M_CMB_LEN>=2) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.13)&(ROE5Y<=0.2)& (NP_P0>1.17*NP_P4) &(PE<10)&(C_H2Y<0.84) & (C_H2Y>0.3)",
  "_T3P6": "{Init} & ((((W_CMB_Step>0) & (W_CMB_LEN>=5) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0) & (M_CMB_LEN>=3) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.1)&(ROE5Y<=0.2)&(NP_P0<0)&(PB<0.8)&(C_H2Y<0.6)&(C_H2Y>0.2)",
  "_RSILow30": "{Init} & (D_RSI<0.3)& (PE<18) & (PB < 3.5) & (ROE5Y>0.1) & (NP_P0 >0) & (PCF >0)  & (PCF < 25) & (FSCORE >=4) & (Cash_P0/totalAsset_P0 > 0.005)",
  "_SuperGrowth": "{Init} & (PE/((NP_P0/NP_P4 -1)*100) < 1) & (ROE_Min5Y > 0.1) &  ((FSCORE>=4)) & (NP_P0/NP_P4 > 1.2)  & (NP_P4 > 0)  & (PCF > 0) & (PCF < 25) & (CF_OA_5Y/OShares > 5000)",
  "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
  "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
  "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
  "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
  "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)",
  "~SellPKA": "(D_RSI_Max1W/D_RSI > 1.03) & (D_RSI_T1 /D_RSI > 1)  & (D_RSI_Max3M > 0.8)& (D_RSI_Max1W < 0.69) & (D_RSI_Max1W >0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.14) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.25)",
  "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2)",
  "~SellPE": "(PE >= PE_MA1Y + PE_SD1Y)  & (Close/VAP1M <0.95) "
}

{
 "$BKMA200": "MA2, MA3, MA4, S13, SellTest",
 "$RSILow30": "MA2, MA3, MA4, S13, SellTest, SellPKA",
 "$TL3M": "MA2, MA3, MA4, S13, SellTest",
 "$UnderBV": "MA2, MA3, MA4, S13, SellTest",
 "$T3P4": "MA2, MA3, MA4, S13, SellTest, SellPKA",
 "$VolMax5Y": "MA2, MA3, MA4, S13, SellTest",
 "$SuperGrowth": "SellLowGrowth",
 "$SurpriseEarning": "MA2, MA3, MA4, S13, SellBV, SellPE, SellResistance1M, SellResistance3M, BearDvg",
 "Init": "(Volume_3M*Price>1e+9) & (time>='2014-01-01') & (time<='2025-01-01')",
 "_BKMA200": "{Init} & ((ID_LO_3Y-ID_HI_3Y)>293) & (MA50/MA200>0.86) & (MA10/MA200<1.37) & (ROE5Y >0.09) & (PE <20) & (NP_P0 > 1.21*NP_P1)",
 "_VolMax5Y": "{Init} & (Close>1.02*Volume_Max5Y_High) & (ROE5Y>0.09)&(PE<13)& (NP_P0 > 1.21*NP_P1) & (PE >0)&(ID_Current-Volume_Max5Y_ID<=120)&(PB<2)",
 "_TL3M": "{Init} & (HI_3M_T1/LO_3M_T1<1.28) & (Volume > 1.16*Volume_3M_P90)& (ROE5Y>0.135) & (PE<20) & (PB < 1.97) & (FSCORE > 4) & (NP_P0 > 1.16*NP_P1) & (PCF>0) & (NP_P0 > 0) & (PE >0)",
 "_T3P4": "{Init} & ((((W_CMB_Step>0.025) & (W_CMB_LEN>=2) & (W_CMB_LAG>0)& (W_CMB_LAG<=3)) | ((M_CMB_Step>0.025) & (M_CMB_LEN>=2) & (M_CMB_LAG >=1) & (M_CMB_LAG <=4)))) & (ROE5Y>=0.13)&(ROE5Y<=0.2)& (NP_P0>1.17*NP_P4) &(PE<10)&(C_H2Y<0.84) & (C_H2Y>0.3)",
 "_RSILow30": "{Init} & (D_RSI<0.3)& (PE<18) & (PB < 3.5) & (ROE5Y>0.1) & (NP_P0 >0) & (PCF >0) & (PCF < 25) & (FSCORE >=4) & (Cash_P0/totalAsset_P0 > 0.005)",
 "_UnderBV": "{Init} & (HI_3M_T1/LO_3M_T1<1.95) & (Volume > 0.65*Volume_3M_P90) & (ROE5Y>0.03) & (PE<40) & (PB < 0.9) & (FSCORE > 4) & (NP_P0 > 0.90*NP_P1) & (PCF>1.65) & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 500 ) & (PE >2.23)",
 "_SuperGrowth": "{Init} & (PE/((NP_P0/NP_P4 -1)*100) < 1) & (ROE_Min5Y > 0.1) & ((FSCORE>=4)) & (NP_P0/NP_P4 > 1.2) & (NP_P4 > 0) & (PCF > 0) & (PCF < 25) & (CF_OA_5Y/OShares > 5000)",
 "_SurpriseEarning": "{Init} & (PE < 10) & (PB < 1) & (ROE_Min5Y > 0.05) & ((NP_P0 - NP_P4)/abs(NP_P4) > 0.22) & (NP_P0/NP_P1> 1.2) & (NP_P0 > 0) & (PCF > 0) & (PCF < 25) & (CF_OA_5Y/OShares > 5000) & (Cash_P0/totalAsset_P0 > 0.006)",
 "~MA2": "(PE>15) & (MA20/MA50<1) & (MA20_T1/MA50_T1>1)",
 "~MA3": "(PE<15) & (Close/MA200<1) & (Close_T1/MA200_T1>1)",
 "~MA4": "(Close > 1.5*MA200) & (NP_P0 < 0.8*NP_P1)",
 "~S13": "(C_L1W>=1.2) & (D_CMB_Peak_T1>D_CMB) & (Close>Close_T1) & (D_CMB_XFast<2)",
 "~SellTest": "(Close > 2*BVPS) & (NP_P0 < NP_P1) &(Close < VAP1M)",
 "~SellPKA": "(D_RSI_Max1W/D_RSI > 1.03) & (D_RSI_T1 /D_RSI > 1) & (D_RSI_Max3M > 0.8)& (D_RSI_Max1W < 0.69) & (D_RSI_Max1W >0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.14) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.25)",
 "~SellLowGrowth": "(NP_P0/NP_P4 < 1.2)",
 "~SellBV": "(Close > 2*BVPS) & (NP_P0 /NP_P1 < 0.85 ) & (Close < 0.98*VAP1M) & (Volume >= Volume_3M_P50)& (D_RSI > 0.3)",
 "~SellPE": "(PE >= PE_MA5Y + 0.5*PE_SD5Y) & (Close/VAP3M <0.98) & (Close_T1> 1.01*VAP3M)& (Volume >= Volume_3M_P50)& (D_RSI > 0.3)",
 "~SellResistance1M": "(ID_XVAP3M_Down_P0 - ID_XVAP1M_Down_P2 <= 30) & (Close < 0.98*VAP1M) & (Close_T1 > VAP1M) & (Volume >= Volume_3M_P50)& (D_RSI > 0.3)",
 "~SellResistance3M": "(ID_Current - ID_XVAP3M_Down_P2 <= 60) &( ID_XVAP3M_Down_P0 - ID_XVAP3M_Down_P1 >= 4) & (Close < 0.98*VAP3M) & (Close_T1W > 1.01*VAP3M) & (Volume >= Volume_3M_P50)& (D_RSI > 0.3)",
 "~BearDvg": "(D_RSI_Max1W/D_RSI > 1.1) & (D_RSI_Max3M > 0.75)& (D_RSI_Max1W < 0.69) & (D_RSI_Max1W >0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.18) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD > 1.25) & (D_RSI_T1/D_RSI > 1.05)"
}

 "_UnderBV": "{Init} & (HI_3M_T1/LO_3M_T1<1.95) & (Volume > 0.65*Volume_3M_P90) & (ROE5Y>0.03) & (PE<40) & (PB < 0.9) & (FSCORE > 4) & (NP_P0 > 0.90*NP_P1) & (PCF>1.65) & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 500 ) & (PE >2.23)",
 "_UnderBV": "{Init}  &  (PB < 0.9) & (FSCORE >= 4) & (NP_P0 > 0.90*NP_P1)  & (PCF>2)  & (PE >0)  & (PCF < 25)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 500) & (NP_P0/NP_P4 > 1.15)",
