<html>
<head>
    <title>Table Email</title>
</head>
<body>
<h2 style="text-align: center;">Report on {{ today }}</h2>

<h3>VNINDEX - Market Overview</h3>

{% if index_report.overheated%}
<p><strong>Overheating Alert:</strong> {{ index_report.overheated.type }}</p>
<p>Period: {{ index_report.overheated.start_group }} to {{ index_report.overheated.end_group }} | Return: {{ index_report.overheated.profit }}%</p>
{% endif %}

{% if index_report.is_pe%}
<p><strong>Overheating Alert:</strong> {{ index_report.is_pe }}</p>
{% endif %}

{% if index_report.is_bfi%}
<p><strong>Overheating Alert:</strong> {{ index_report.is_bfi}}</p>
{% endif %}

<h4>Today</h4>
<p>PE: {{ index_report.pe_today }}</p>
<p>PE/PE_MA5Y: {{ index_report.pe_pe5y_today }}</p>
<p>Buffett Indicator: {{ index_report.bfi_today }}</p>

<h4>3-Month Change</h4>
<p>Close: {{ index_report.close_rat }} %</p>
<p>PE/PE_MA5Y: {{ index_report.pe_ratio }} %</p>
<p>Trading Value: {{ index_report.trading_value_rat }} %</p>



<h3>Lastest buying signal table</h3>
<table style="border-collapse: collapse; border-style: double; border-color: #70bbd9; margin-left: auto; margin-right: auto;"
       border="3">
    <tbody>
    <tr style="height: 50px; background: #66EFC7;">
        <td style="width: 80.125px; text-align: center;"><strong>Ticker </strong></td>
        <td style="width: 191.047px; text-align: center;"><strong>Signal</strong></td>
        <td style="width: 191.047px; text-align: center;"><strong>Num Cutloss</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Deal Date</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Hit Date</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Deal Profit</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Hit Profit</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Current Price</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Current Volume</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Target</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Score</strong></td>
    </tr>
    {% for signal in latest_filtered_tickers %}
    <tr style="height: 30px;">
        <td style="width: 80.125px;">{{ signal.ticker }}</td>
        <td style="width: 191.047px;">{{ signal.filter }}</td>
        <td style="width: 191.047px;">{{ signal.num_of_cutloss }}</td>
        <td style="width: 190.922px;">{{ signal.begin }}</td>
        <td style="width: 190.922px;">{{ signal.time }}</td>
        <td style="width: 120.922px;">{{ signal.deal_profit_today }}</td>
        <td style="width: 120.922px;">{{ signal.hit_profit_today }}</td>
        <td style="width: 120.922px;">{{ signal.current_price }}</td>
        <td style="width: 120.922px;">{{ signal.current_volume }}</td>
        <td style="width: 120.922px;">{{ signal.upper_price }}</td>
        <td style="width: 120.922px;">{{ signal.score }}</td>

    </tr>
    {% endfor %}
    </tbody>
</table>


<h3>Buying signal table</h3>
<table style="border-collapse: collapse; border-style: double; border-color: #70bbd9; margin-left: auto; margin-right: auto;"
       border="3">
    <tbody>
    <tr style="height: 50px; background: #66EFC7;">
        <td style="width: 80.125px; text-align: center;"><strong>Ticker </strong></td>
        <td style="width: 191.047px; text-align: center;"><strong>Signal</strong></td>
        <td style="width: 191.047px; text-align: center;"><strong>Num Cutloss</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Deal Date</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Hit Date</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Deal Profit</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Hit Profit</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Current Price</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Current Volume</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Target</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Score</strong></td>
    </tr>
    {% for signal in filtered_signals %}
    {% if signal.filter.startswith('_') %}
    <tr style="height: 30px;">
        <td style="width: 80.125px;">{{ signal.ticker }}</td>
        <td style="width: 191.047px;">{{ signal.filter }}</td>
        <td style="width: 191.047px;">{{ signal.num_of_cutloss }}</td>
        <td style="width: 190.922px;">{{ signal.begin }}</td>
        <td style="width: 190.922px;">{{ signal.time }}</td>
        <td style="width: 120.922px;">{{ signal.deal_profit_today }}</td>
        <td style="width: 120.922px;">{{ signal.hit_profit_today }}</td>
        <td style="width: 120.922px;">{{ signal.current_price }}</td>
        <td style="width: 120.922px;">{{ signal.current_volume }}</td>
        <td style="width: 120.922px;">{{ signal.upper_price }}</td>
        <td style="width: 120.922px;">{{ signal.score }}</td>

    </tr>
    {% endif %}
    {% endfor %}
    </tbody>
</table>
<p> &nbsp &nbsp Note: Profit is calculated by the Open_1D price </p>
<p> &nbsp &nbsp Note: Deal dates searched for in the last 6 months</p>


<h3>Selling signal table</h3>
<table style="border-collapse: collapse; border-style: double; border-color: #70bbd9; margin-left: auto; margin-right: auto;"
       border="3">
    <tbody>
    <tr style="height: 50px; background: #66EFC7;">
        <td style="width: 80.125px; text-align: center;"><strong>Ticker</strong></td>
        <td style="width: 191.047px; text-align: center;"><strong>Signal</strong></td>
        <!--        <td style="width: 190.922px; text-align: center;"><strong>Buy Date</strong></td>-->
        <td style="width: 190.922px; text-align: center;"><strong>Sell Date</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Shortsell Profit</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Sell Price</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Current Price</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Holding Period</strong></td>
        <td style="width: 120.922px; text-align: center;"><strong>Score</strong></td>
    </tr>
    {% for signal in filtered_signals %}
    {% if signal.filter.startswith('~') %}
    {% if signal.ticker in (list_watchlist_tickers + buy_recommend_tickers) %}
    <tr style="height: 30px;">
        <td style="width: 80.125px;"><strong>{{ signal.ticker }}</strong></td>
        <td style="width: 191.047px;"><strong>{{ signal.filter }}</strong></td>
        <!--        <td style="width: 190.922px;"><strong>{{ signal.begin }}</strong></td>-->
        <td style="width: 190.922px;"><strong>{{ signal.time }}</strong></td>
        <td style="width: 120.922px;"><strong>{{ signal.hit_profit_today }}</strong></td>
        <td style="width: 120.922px;"><strong>{{ signal.hit_price }}</strong></td>
        <td style="width: 120.922px;"><strong>{{ signal.current_price }}</strong></td>
        <td style="width: 120.922px;"><strong>{{ signal.hit2now_holding_time }}</strong></td>
        <td style="width: 120.922px;"><strong>{{ signal.score }}</strong></td>
    </tr>
    {% endif %}
    {% endif %}
    {% endfor %}
    </tbody>
</table>
<p> &nbsp &nbsp Note: Shortsell Profit is calculated by the Open_1D price </p>


<h3>Alert interest stock</h3>

<table style="border-collapse: collapse; border-style: double; border-color: #70bbd9; margin-left: auto; margin-right: auto;"
       border="3">
    <tbody>
    <tr style="height: 50px; background: #66EFC7;">
        <td style="width: 80.125px; text-align: center;"><strong>Ticker</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Current</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Low</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Pattern</strong></td>
    </tr>
    {% for sell_alert in sell_alerts %}
    <tr style="height: 30px;">
        <td style="width: 80.125px;">{{ sell_alert.ticker }}</td>
        <td style="width: 190.922px;">{{ sell_alert.current }}</td>
        <td style="width: 190.922px;">{{ sell_alert.alert_price }}</td>
        <td style="width: 190.922px;">{{ sell_alert.filter }}</td>
    </tr>
    {% endfor %}
    </tbody>
</table>


<h3>Cutloss table in 6 months</h3>
<table style="border-collapse: collapse; border-style: double; border-color: #70bbd9; margin-left: auto; margin-right: auto;"
       border="3">
    <tbody>
    <tr style="height: 50px; background: #66EFC7;">
        <td style="width: 80.125px; text-align: center;"><strong>Ticker</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Signal</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Hit Date</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Hit Price</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Cutloss Date</strong></td>
        <td style="width: 190.922px; text-align: center;"><strong>Cutloss Price</strong></td>
    </tr>
    {% for cutlossed_signal in cutlossed_signals %}
    <tr style="height: 30px;">
        <td style="width: 80.125px;">{{ cutlossed_signal.ticker }}</td>
        <td style="width: 190.922px;">{{ cutlossed_signal.filter }}</td>
        <td style="width: 190.922px;">{{ cutlossed_signal.time }}</td>
        <td style="width: 190.922px;">{{ cutlossed_signal.price }}</td>
        <td style="width: 190.922px;">{{ cutlossed_signal.cutloss_time }}</td>
        <td style="width: 190.922px;">{{ cutlossed_signal.cutloss_price }}</td>
    </tr>
    {% endfor %}
    </tbody>
</table>
<p> &nbsp &nbsp Note: Hit Price is Open_1D and  Cutloss Price is Close</p>

{% if alert.obs_ticker %}
<p>Alert - Miss necessary indicators in Stock watchlist: {{alert.obs_ticker}}</p>
{% endif %}

{% if alert.over_threshold > 100 %}
<p>Alert - There are {{alert.over_threshold}} tickers missing the necessary indicator</p>
{% endif %}

<p><br/>Stock watchlist: {{watchlist_tickers}}</p>
<p>Stock with null indicator: {{monitor_tickers}}</p>

<h3><br/>Hit Details</h3>

{% for signal in filtered_signals %}
<table style="height: 28px; width: 100%; border-collapse: collapse; float: left;" border="1">
    <tbody>
    <tr style="height: 10px; background-color: #70bbd9;">
        <td style="width: 9.71125%; height: 10px;"><strong>{{ signal.ticker }}</strong></td>
        <td style="width: 21.3254%; height: 10px;"><strong>{{ signal.time }}</strong></td>
        <td style="width: 68.9632%; height: 10px;"><strong>{{ signal.filter }}</strong></td>
    </tr>
    <tr style="height: 18px;">
        <td style="width: 9.71125%; height: 18px;">Formula</td>
        <td style="height: 18px;" , colspan="2">{{ signal.formula }}</td>
    </tr>
    {% for row in signal.l_df %}
    {% if row[0] not in ['time', 'filter'] %}
    <tr style="height: 18px;">
        <td style="width: 9.71125%; height: 18px;">{{ row[0] }}</td>
        <td style="width: 21.3254%; height: 18px;">{{ row[1] }}</td>
        <td style="width: 68.9632%; height: 18px;">{{ ind_description.get(row[0], 'N/A') }}</td>
    </tr>
    {% endif %}
    {% endfor %}
    </tbody>
</table>
{% endfor %}
<br/>
<br/>
<p><br/></p>
<h3><br/>Monitor</h3>

<table style="height: 28px; width: 100%; border-collapse: collapse; float: left;" border="1">
    <tbody>
    <tr style="height: 18px;background-color: #25eaea;">
        <td style="width: 9.71125%; height: 18px;"><strong>Ticker</strong></td>
        <td style="height: 18px;" , colspan="2"><strong>Null indicators</strong></td>
    </tr>
    {% for ticker, value in monitor.items() %}
    {% if ticker not in monitor_null_nessary_indicator_tickers %}
    <tr style="height: 18px;">
        <td style="width: 9.71125%; height: 18px;">{{ticker}}</td>
        <td style="height: 18px;" , colspan="2">{{value}}</td>
    </tr>
    {% else %}
    <tr style="height: 18px;">
        <td style="width: 9.71125%; height: 18px;"><strong>{{ticker}}</strong></td>
        <td style="height: 18px;" , colspan="2"><strong>{{value}}</strong></td>
    </tr>
    {% endif %}
    {% endfor %}
    </tbody>
</table>
</body>
</html>
